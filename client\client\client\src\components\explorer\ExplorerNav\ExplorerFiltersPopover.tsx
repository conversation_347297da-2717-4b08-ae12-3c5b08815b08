import { FilterOutlined } from '@ant-design/icons'
import { Button, Popover } from 'antd'
import { useSelector } from '@store'
import { getExplorersFilterDirty } from '@store/ui/selectors'
import ExplorerFiltersMenu from './ExplorerFiltersMenu'

const ExplorerFiltersPopover = () => {
  const active = useSelector(getExplorersFilterDirty)

  return (
    <Popover
      content={<ExplorerFiltersMenu />}
      placement="bottomLeft"
      trigger={'click'}
      title="Filters"
    >
      <Button icon={<FilterOutlined />} type={active ? 'ghost' : 'default'}>
        Filters
      </Button>
    </Popover>
  )
}

export default ExplorerFiltersPopover
