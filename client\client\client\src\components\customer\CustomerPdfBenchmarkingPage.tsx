import { useMemo } from 'react'
import { Text, View } from '@react-pdf/renderer'
import {
  PdfChartImage,
  PdfPage,
  PdfTable,
  PdfText,
  stylesheet,
} from '@components/pdf'
import { WIDTH } from '@components/pdf/const'
import type useReport from '@store/hooks/useReport'
import CustomerKpiGraph from './CustomerKpiGraph'
import type { CustomerPdfProps } from './CustomerPdf'
import { DENOMINATOR_LOOKUP } from './static'
import { getMeasureType, getMeasureFormattedValue } from './helpers'

type Props = {
  data: ReturnType<typeof useReport>['data']
} & Pick<CustomerPdfProps, 'denominator'>

const CustomerPdfBenchmarkingPage = ({ data, denominator }: Props) => {
  const getFormattedValueWithDenominator = useMemo(
    () => getMeasureFormattedValue(denominator),
    [denominator]
  )

  if (!data?.length) return null

  return (
    <>
      {data.map((measureData, i) => {
        return (
          <PdfPage key={`measure-${measureData.label}-${i}`}>
            <Text style={stylesheet.headingL}>{measureData.label}</Text>
            {DENOMINATOR_LOOKUP[denominator] && (
              <Text style={stylesheet.subheading}>
                {DENOMINATOR_LOOKUP[denominator]}
              </Text>
            )}
            <PdfText>{measureData.description}</PdfText>
            <PdfChartImage width={WIDTH.FULL}>
              <CustomerKpiGraph
                data={measureData}
                metric={getMeasureType(measureData.measureType, denominator)}
              />
            </PdfChartImage>
            <View wrap={false}>
              <PdfTable
                columns={[
                  { key: 'year', isHeader: true },
                  { key: 'lq', title: 'LQ' },
                  { key: 'median' },
                  { key: 'uq', title: 'UQ' },
                  {
                    key: 'target',
                    title: 'Customer',
                  },
                ]}
                rows={measureData.benchmarks.map(
                  ({ lq, median, uq, year, target, measureType }) => {
                    return {
                      year,
                      lq: getFormattedValueWithDenominator(lq, measureType),
                      median: getFormattedValueWithDenominator(
                        median,
                        measureType
                      ),
                      uq: getFormattedValueWithDenominator(uq, measureType),
                      target: getFormattedValueWithDenominator(
                        target,
                        measureType
                      ),
                    }
                  }
                )}
              />
            </View>
          </PdfPage>
        )
      })}
    </>
  )
}

export default CustomerPdfBenchmarkingPage
