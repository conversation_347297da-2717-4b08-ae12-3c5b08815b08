import classNames from 'classnames'
import { Pane } from 'react-leaflet'
import { useSelector } from 'react-redux'
import { MapSelectedAddressTitleLayer } from '@components/map'
import { getSelectedAddressIds } from '@store/ui/selectors'
import { truthy } from '@util/guards'
import styles from './ExplorerSelectedAddressesPane.module.scss'

const ExplorerSelectedAddressPane = () => {
  const addressIds = useSelector(getSelectedAddressIds)

  return (
    <Pane
      name="selectedAddresses"
      className={classNames('ExplorerSelectedAddressPane', styles.container)}
    >
      {addressIds.filter(truthy).map((id, i) => (
        <MapSelectedAddressTitleLayer
          key={id}
          addressId={+id}
          addressIndex={i}
        />
      ))}
    </Pane>
  )
}

export default ExplorerSelectedAddressPane
