import { format } from 'date-fns'
import { PdfCoverPage, PdfCoverPageHeading } from '@components/pdf'
import type { CustomerPdfProps } from './CustomerPdf'

type Props = Pick<CustomerPdfProps, 'reportTitle' | 'customerName'>

const CustomerPdfCoverPage = ({ reportTitle, customerName }: Props) => {
  return (
    <PdfCoverPage>
      <PdfCoverPageHeading size="l">{reportTitle}</PdfCoverPageHeading>
      <PdfCoverPageHeading>{customerName}</PdfCoverPageHeading>
      <PdfCoverPageHeading>
        {format(new Date(), 'dd MMMM yyyy')}
      </PdfCoverPageHeading>
    </PdfCoverPage>
  )
}

export default CustomerPdfCoverPage
