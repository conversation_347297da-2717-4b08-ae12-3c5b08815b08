import { CheckCircleOutlined } from '@ant-design/icons'
import { Form, Table } from 'antd'
import type React from 'react'
import { useEffect, useState } from 'react'
import { getFieldName } from '../../helpers'
import type { CcraPhysicalRisk } from '@store/services/sdk'

const StaticTableFieldPhysicalRisks = ({
  providedValues,
}: { providedValues: CcraPhysicalRisk[] }) => {
  const form = Form.useFormInstance()
  const physicalRisksValue = Form.useWatch(getFieldName('physicalRisks'), form)

  const [data, setData] = useState<CcraPhysicalRisk[]>([])

  useEffect(() => {
    if (physicalRisksValue) {
      setData(physicalRisksValue)
    }
  }, [physicalRisksValue])

  useEffect(() => {
    if (providedValues) {
      setData(providedValues)
    }
  }, [providedValues])

  const columns = [
    {
      title: 'Risks',
      dataIndex: 'risk',
      key: 'risk',
      render: (risk: string, _record: CcraPhysicalRisk) => <span>{risk}</span>,
    },
    {
      title: 'Material to customer?',
      dataIndex: 'materialToCustomer',
      key: 'materialToCustomer',
      width: '150px',
      render: (materialToCustomer: string, _record: CcraPhysicalRisk) => (
        <span>{materialToCustomer || '-'}</span>
      ),
    },
    {
      title: 'Measures taken to mitigate, if any',
      dataIndex: 'measureTaken',
      key: 'measureTaken',
      render: (measureTaken: string, _record: CcraPhysicalRisk) => (
        <span>{measureTaken || '-'}</span>
      ),
    },
    {
      title:
        "Has the customer advised they're insured for this (partly or fully)?",
      dataIndex: 'isInsured',
      key: 'isInsured',
      width: '230px',
      render: (isInsured: boolean) =>
        isInsured ? <CheckCircleOutlined style={{ fontSize: '18px' }} /> : null,
    },
  ]

  return (
    <Table
      dataSource={data}
      columns={columns}
      rowKey="index"
      pagination={false}
      size="small"
    />
  )
}

export default StaticTableFieldPhysicalRisks
