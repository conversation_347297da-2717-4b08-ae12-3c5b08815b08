import { Select, type SelectProps } from 'antd'
import React, { useMemo } from 'react'
import type { AgriUpliftEmissionsSource } from '@store/services/finance/codegen'
import { PLACEHOLDER_CHAR } from '@util/const'
import EmissionsSourceTitle from './EmissionsSourceTitle'

type Props = SelectProps & {
  emissionsSources: AgriUpliftEmissionsSource[]
}

const EmissionsSourceSelect = ({ emissionsSources, ...props }: Props) => {
  const options = useMemo(
    () =>
      emissionsSources.map((source) => ({
        value: source.pk,
        label: <EmissionsSourceTitle source={source} />,
      })),
    [emissionsSources]
  )

  return <Select options={options} placeholder={PLACEHOLDER_CHAR} {...props} />
}

export default EmissionsSourceSelect
