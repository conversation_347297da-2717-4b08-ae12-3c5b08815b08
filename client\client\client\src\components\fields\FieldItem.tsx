import React, { useEffect, useMemo } from 'react'
import { useFormContext } from 'react-hook-form'
import type { FieldNode } from '@store/services/fields/codegen'
import { not } from '@util/helpers'
import font from '@styles/new/font.module.css'
import util from '@styles/new/util.module.css'
import { FieldMessage } from './FieldContainer'
import FieldValuesController from './FieldValuesController'
import CheckboxGroup, {
  useSelectedDescendants,
  useSelectedValues,
  type CheckboxGroupProps,
} from './CheckboxGroup'
import DatePicker from './DatePicker'
import RadioGroup, { type RadioGroupProps } from './RadioGroup'
import TextArea from './TextArea'
import useFieldMessages from './useFieldMessages'
import {
  type FieldType,
  type FieldValue,
  type FieldComponent,
  valueByFieldId,
  isSelectField,
  findFieldByValue,
} from './util'
import styles from './FieldItem.module.css'
import { uniqBy } from 'lodash'
import classNames from 'classnames'
import Heading from '@components/anz/Heading'

type FieldComponents = Record<
  Exclude<FieldType, 'RADIO' | 'BOOLEAN' | 'STRING'>,
  FieldComponent
>

interface FieldsState {
  components: FieldComponents
  fieldKeyComponents: Record<string, FieldComponent>
}

interface FieldsProviderProps {
  children: React.ReactNode
  components?: Partial<FieldComponents>
  fieldKeyComponents?: Record<string, FieldComponent>
}

const initialState: FieldsState = {
  components: {
    DOCUMENT: SectionField,
    SECTION: SectionField,
    DATE: DateField,
    TEXT: TextField,
    SELECT: RadioField,
    SELECT_MANY: CheckboxField,
    ANY_SELECT: AnySelectField,
  },
  fieldKeyComponents: {},
}

const FieldsContext = React.createContext<FieldsState>(initialState)

export function FieldsProvider({
  children,
  components = {},
  fieldKeyComponents = {},
}: FieldsProviderProps) {
  const value = useMemo(
    () => ({
      components: {
        ...initialState.components,
        ...components,
      },
      fieldKeyComponents: {
        ...initialState.fieldKeyComponents,
        ...fieldKeyComponents,
      },
    }),
    [components, fieldKeyComponents]
  )

  return (
    <FieldsContext.Provider value={value}>{children}</FieldsContext.Provider>
  )
}

export function useFieldComponents() {
  const context = React.useContext(FieldsContext)
  if (context === undefined) {
    throw new Error('useFields must be used within a FieldsProvider')
  }
  return context
}

export function useFieldComponent(fieldType: FieldType, fieldKey: string) {
  const { components, fieldKeyComponents } = useFieldComponents()

  const fieldKeyComponent: FieldComponent | undefined =
    fieldKeyComponents[fieldKey]

  if (fieldKeyComponent) return fieldKeyComponent

  return components[fieldType as keyof FieldComponents]
}

function FieldItems({ fields = [] }: { fields?: FieldNode[] }) {
  return (
    <>
      {fields.map((field) => (
        <FieldItem key={field.id} {...field} />
      ))}
    </>
  )
}

function SectionField(field: FieldNode) {
  return (
    <>
      {!!field.description && (
        <Heading
          color="deepSea"
          size={5}
          tag="h3"
          style={{ margin: '3rem 0 1.5rem' }}
        >
          {field.description}
        </Heading>
      )}
      <FieldItems fields={field.descendants} />
    </>
  )
}

function TextField(field: FieldNode) {
  return <FieldValuesController field={field} component={TextArea} />
}

function RadioSection({
  field,
  value: selectedValue,
  onChange,
}: RadioGroupProps) {
  const selectedDescendant = useMemo(
    () => findFieldByValue(field.descendants, selectedValue?.value),
    [field.descendants, selectedValue?.value]
  )

  return (
    <>
      <RadioGroup field={field} value={selectedValue} onChange={onChange}>
        <FieldMessage>{selectedDescendant?.metadata?.message}</FieldMessage>
      </RadioGroup>
      <FieldItems fields={selectedDescendant?.descendants} />
    </>
  )
}

function RadioField(field: FieldNode) {
  return <FieldValuesController field={field} component={RadioSection} />
}

function CheckboxSection({
  field,
  value: selectedValue,
  onChange,
}: CheckboxGroupProps) {
  const selectedValues = useSelectedValues(selectedValue)
  const selectedDescendants = useSelectedDescendants(field, selectedValues)
  const selectedDescendantMessages = useFieldMessages(selectedDescendants)

  return (
    <>
      <CheckboxGroup field={field} value={selectedValue} onChange={onChange}>
        {selectedDescendantMessages.map(({ id, message }) => (
          <FieldMessage key={id}>{message}</FieldMessage>
        ))}
      </CheckboxGroup>
      {uniqBy(selectedDescendants, 'field_key').map((selectedDescendant) => (
        <FieldItems
          key={selectedDescendant.id}
          fields={selectedDescendant.descendants}
        />
      ))}
    </>
  )
}

function CheckboxField(field: FieldNode) {
  return <FieldValuesController field={field} component={CheckboxSection} />
}

function DateField(field: FieldNode) {
  return <FieldValuesController field={field} component={DatePicker} />
}

// TODO:
// - Could this be improved?
// - Allow match of value via metadata?
function AnySelectField(field: FieldNode) {
  const { watch } = useFormContext()

  const selectFields = field.descendants.filter(isSelectField)
  const selectFieldsIds = selectFields.map((field) => field.id)

  const values: FieldValue[] = watch('values')

  const selectFieldsValues = values.filter((value) =>
    selectFieldsIds.includes(value.fieldNodeId)
  )

  // TODO: Tie this to server definition, metadata?
  const anyTrue = selectFieldsValues.some(({ value = '' }) => value === 'Yes')

  return (
    <>
      <FieldItems fields={selectFields} />
      {anyTrue && (
        <FieldItems fields={field.descendants.filter(not(isSelectField))} />
      )}
    </>
  )
}

export default function FieldItem(field: FieldNode) {
  const FieldComponent = useFieldComponent(field.fieldType, field.fieldKey)

  const { getValues, setValue } = useFormContext()

  // Unregister
  useEffect(() => {
    return () => {
      const values = getValues('values') || []
      setValue('values', values.filter(not(valueByFieldId(field.id))))
    }
  }, [field.id, getValues, setValue])

  return <FieldComponent {...field} />
}
