import { Form, Input } from 'antd'
import React, { memo, useMemo } from 'react'
import { concatPaths } from '@util/helpers'
import Emissions from './Emissions'
import type { NamePath } from 'rc-field-form/es/interface'
import type {
  AgriUpliftEmissionsSource,
  AgriUpliftFacility,
} from '@store/services/finance/codegen'
import { getResourcePath } from './helpers'

type Props = {
  path: NamePath
  // Refers to an 'invalid' source with no report, that stores facilities that aren't eligible.
  invalid?: boolean
  lockSource?: boolean
  facilities?: AgriUpliftFacility[]
}

const EmissionsSource = ({
  path,
  facilities = [],
  invalid,
  lockSource,
}: Props) => {
  const getFieldPath = useMemo(
    () => getResourcePath(path)<AgriUpliftEmissionsSource>,
    [path]
  )

  return (
    <Form.Item hidden={invalid}>
      <Form.Item name={getFieldPath('pk')} hidden>
        <Input />
      </Form.Item>
      <Form.Item
        name={getFieldPath('name')}
        label="Supplier Number or NZ FAI Membership Number"
        rules={[{ required: true }]}
        messageVariables={{ name: 'Name' }}
      >
        <Input />
      </Form.Item>
      {!invalid && (
        <Emissions path={getFieldPath('emissions')} lockSource={lockSource} />
      )}
      {facilities?.map((facility, j) => {
        const facilityPath = (key: keyof AgriUpliftFacility) =>
          concatPaths(getFieldPath('facilities'), [j, key])
        return (
          <React.Fragment key={facility.loanId}>
            <Form.Item name={facilityPath('loanId')} hidden>
              <Input />
            </Form.Item>
            <Form.Item name={facilityPath('eligible')} hidden>
              <Input />
            </Form.Item>
          </React.Fragment>
        )
      })}
    </Form.Item>
  )
}

export default memo(EmissionsSource)
