import { TableOutlined } from '@ant-design/icons'
import { Alert, List } from 'antd'
import classNames from 'classnames'
import type React from 'react'
import { useState } from 'react'
import { useDispatch } from 'react-redux'
import ExplorerSelectedSaleDetail from '@components/addresses/SaleDetailCard'
import { setWidgetState } from '@store/ui/actions'
import styles from '../SelectedAddresses/SelectedAddresses.module.scss'
import { SelectedSaleItem } from '../SelectedSaleItem'
import SelectedTabButtons, { SelectedTabButton } from '../SelectedTabButtons'

interface SelectedSalesAndListingsProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
  saleIds?: string[]
}

const SelectedSales = ({
  saleIds,
  ...divProps
}: SelectedSalesAndListingsProps) => {
  const dispatch = useDispatch()

  const [detailId, setDetailId] = useState<string>()

  const noSalesMessage = () => {
    return (
      <div
        style={{
          textAlign: 'center',
          padding: '13px',
          paddingTop: '0',
        }}
      >
        <Alert
          message={`No sales
                     have been selected
                                yet...`}
          type="info"
          showIcon
        />
      </div>
    )
  }

  return (
    <div
      className={classNames('SelectedAddresses', styles.container)}
      {...divProps}
    >
      <SelectedTabButtons layer="sale">
        <SelectedTabButton
          onClick={() =>
            dispatch(
              setWidgetState({
                widgetKey: 'salesModal',
                widgetState: true,
              })
            )
          }
          icon={<TableOutlined />}
        >
          View Detailed Summary
        </SelectedTabButton>
      </SelectedTabButtons>
      {saleIds?.length ? (
        <>
          <div className={styles.addresses}>
            <List size="small" className={styles.list}>
              {saleIds
                ?.filter((x: string) => x !== null)
                ?.map((saleId: string, index: number) => {
                  return (
                    <SelectedSaleItem
                      position={index}
                      saleId={Number(saleId)}
                      key={saleId}
                      saleType={'SALE'}
                      onClick={() => {
                        setDetailId(saleId)
                      }}
                    />
                  )
                })}
            </List>
          </div>
        </>
      ) : (
        noSalesMessage()
      )}
      <ExplorerSelectedSaleDetail id={Number(detailId)} />
    </div>
  )
}

export default SelectedSales
