.ExportDetails {
  font-size: var(--export-primary-font-size);
}

.ExportDetails:not([data-type='memorials']) .export-details-item-value {
  max-width: 10cm;
}

.ExportDetails[data-type='memorials'] .ExportDetailsItem {
  padding: 0.1cm;
}

.ExportDetails[data-type='memorials'] .ExportDetailsItem:nth-child(even) {
  background-color: rgba(250, 250, 250, 1);
}

.ExportDetails:not(:first-child) {
  border-left: 1px solid #ccc;
  margin-left: 1cm;
  padding-left: 1cm;
}

.ExportDetails[data-type='financials'] .export-details-title {
  font-style: italic;
}

.ExportDetails[data-type='financials'] .export-details-content {
  padding-left: 0.5cm;
}

.ExportDetails[data-type='financials']
  .ExportDetailsItem[data-type='placeholder']
  .export-details-item-label {
  opacity: 0;
}

.ExportDetails[data-type='financials']
  .ExportDetailsItem[data-type='total']
  .export-details-item-value:before,
.ExportDetails[data-type='financials']
  .ExportDetailsItem[data-type='subtotal']
  .export-details-item-value:before,
.ExportDetails[data-type='financials']
  .ExportDetailsItem[data-type='number']
  .export-details-item-value:before {
  content: '$ ';
}

.ExportDetails[data-type='financials']
  .ExportDetailsItem[data-type='total']
  .export-details-item-label {
  padding-left: 0.5cm;
  font-weight: bold;
}

.ExportDetails[data-type='financials']
  .ExportDetailsItem[data-type='total']
  .export-details-item-value {
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
}
