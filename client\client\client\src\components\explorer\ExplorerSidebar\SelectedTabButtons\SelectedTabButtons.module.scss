.container {
  position: sticky;
  top: 0;
  z-index: 3;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  padding: 0 12px 12px;
  border-bottom: 1px solid lightgrey;
  background: white;

  > *:nth-last-child(2) {
    margin-left: auto;
  }

  @container (max-width: 460px) {
    :global .ant-btn {
      > span:last-child:not([role='img']) {
        display: none;
      }
    }
  }
}
