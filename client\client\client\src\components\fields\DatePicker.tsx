import { memo, useCallback, useState } from 'react'
import { IsoStringDatePicker } from '@components/form'
import type { FieldNode } from '@store/services/fields/codegen'
import FieldContainer from './FieldContainer'
import type { FieldValue } from './util'

export type DatePickerProps = {
  field: FieldNode
  value?: FieldValue
  onChange?: (newValue: FieldValue | undefined) => void
  children?: React.ReactNode
}

function DatePicker({
  field,
  value: initialValue,
  onChange = () => undefined,
  children,
}: DatePickerProps) {
  const [value] = useState(initialValue)

  const handleChange = useCallback(
    (newDate: string | null) => {
      const newValue: FieldValue | undefined = newDate
        ? {
            id: initialValue?.id || Date.now(),
            fieldNodeId: field.id,
            fieldKey: field.fieldKey,
            value: newDate,
          }
        : undefined
      onChange(newValue)
    },
    [field.id, field.fieldKey, initialValue?.id, onChange]
  )

  return (
    <FieldContainer
      tagName="label"
      title={field.description}
      message={field.metadata?.message}
      guidance={field.metadata?.guidance}
    >
      <IsoStringDatePicker
        withoutUTCConversion
        value={value?.value}
        onChange={handleChange}
      />
      {children}
    </FieldContainer>
  )
}

export default DatePicker
