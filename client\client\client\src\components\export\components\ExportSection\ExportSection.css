.ExportSection {
  display: block;
  flex-grow: 0;
}

.ExportSection span {
  font-family: MyriadPro !important;
  font-weight: 100;
  font-size: var(--export-primary-font-size);
}

.ExportSection:not(:last-child) {
  margin-bottom: 8mm;
}

.ExportSection ul {
  margin-bottom: 0;
  padding-bottom: 0;
}

.ExportSection .export-section-content .export-section-inner {
  margin-top: 8mm;
}

.ExportSection .export-section-content .export-section-inner.flex {
  display: flex;
}

.ExportSection .export-section-content .export-section-inner.flex > div {
  flex: 50%;
}

.ExportSection
  .export-section-content
  .export-section-inner.flex
  > div:first-child {
  margin-right: 1cm;
}

.ExportSection .export-section-content.flex {
  display: flex;
  width: 100%;
}

.ExportSection .export-section-content.flex .export-graph {
  flex: 50%;
}

.ExportSection .export-section-content.flex .export-graph:not(:last-child) {
  padding-right: 2cm;
}

.ExportSection .export-section-title {
  font-size: var(--export-header-font-size);
  margin-bottom: 0.1cm;
  font-family: MyriadPro;
  color: black;
}

@media print {
  .ExportSection {
    page-break-inside: avoid;
  }
}
