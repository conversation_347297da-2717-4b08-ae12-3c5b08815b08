import classNames from 'classnames'
import React from 'react'
import { ButtonWidget } from '@components/generic'
import ToggleFullscreenButton from '@components/generic/ToggleFullscreenButton'
import { ExplorerExportPreviewToggleButton } from '../ExplorerExportPreview'
import { ExplorerSidebarToggle } from '../ExplorerSidebar'
import ExplorerFiltersPopover from './ExplorerFiltersPopover'
import ExplorerLayersPopover from './ExplorerLayersPopover'
import styles from './ExplorerNav.module.scss'
import ExplorerSearch from './ExplorerSearch'
import ExplorerTitleModeDropdown from './ExplorerTitleModeDropdown'

const ExplorerNav = () => {
  return (
    <div className={classNames('ExplorerNav', styles.container)}>
      <ExplorerSearch />
      <div className={styles.scroll}>
        <ExplorerTitleModeDropdown />
        <ButtonWidget className={styles.buttons}>
          <ExplorerLayersPopover />
          <ExplorerFiltersPopover />
        </ButtonWidget>
        <ExplorerExportPreviewToggleButton />
      </div>
      <ToggleFullscreenButton />
      <ExplorerSidebarToggle />
    </div>
  )
}

export default ExplorerNav
