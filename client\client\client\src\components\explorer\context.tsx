import React, { type ReactNode, useContext, useState } from 'react'
import { useLocalStorage } from 'react-use'

type Context =
  | {
      sidebarOpen: boolean | undefined
      setSidebarOpen: React.Dispatch<React.SetStateAction<boolean | undefined>>
    }
  | undefined

export const ExplorerContext = React.createContext<Context>(undefined)

/**
 *
 * @deprecated Move to explorer slice
 */
export const ExplorerProvider = ({ children }: { children: ReactNode }) => {
  const [sidebarOpen, setSidebarOpen] = useLocalStorage(
    'explorerSidebarOpen',
    true
  )

  const value = {
    sidebarOpen,
    setSidebarOpen,
  }

  return (
    <ExplorerContext.Provider value={value}>
      {children}
    </ExplorerContext.Provider>
  )
}

/**
 *
 * @deprecated Move to explorer slice
 */
export const useExplorer = () => {
  const explorer = useContext(ExplorerContext)
  if (explorer === undefined) {
    throw new Error('useExplorer must be used inside ExplorerProvider')
  }
  return explorer
}
