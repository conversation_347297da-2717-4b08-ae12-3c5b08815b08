import React, { type ReactChild } from 'react'
import { shallowEqual, useSelector } from 'react-redux'
import type { RootState } from '../../../../store'
import { uiSelectors } from '../../../../store/ui'

interface PageProps {
  children: ReactChild | ReactChild[]
  className?: string
}

const selector = (state: RootState, props: PageProps) => {
  return {
    children: props.children,
    className: props.className,
    user: uiSelectors.getUser(state),
  }
}

export const Page = (props: PageProps) => {
  const { children, className, user } = useSelector(
    (state: RootState) => selector(state, props),
    shallowEqual
  )

  return (
    <div {...props} className={`page ${className || ''}`}>
      {children}
      <footer>
        <div className="footer-container">
          <div className="footer-timestamp">{new Date().toLocaleString()}</div>
          <div className="footer-classification">Internal/Uncontrolled</div>
          <div className="footer-user">
            {`${user?.username?.toLocaleLowerCase()}@anz.com`}
          </div>
        </div>
      </footer>
    </div>
  )
}
