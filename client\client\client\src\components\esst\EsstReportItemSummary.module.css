.title {
  /*margin-top: 3rem;*/
  margin: 2rem 0 1.5rem;

  color: var(--color-pacific-blue);
}

.sectionTitle {
  color: var(--color-primary);
  margin: 2rem 0 1rem;
}

.summaryValue {
  display: flex;
  gap: .5rem;
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
  border-bottom: var(--border);

  &:before {
    content: '•';
    color: var(--color-primary);
  }
}

.question {
  max-width: var(--text-max-l);
  font-weight: 500;

  p {
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.answer {
  margin: 0;
}

.list {
  margin: 0;
}

.summaryCardHeading {
  display: flex;
  align-items: center;
  gap: 2rem;
  /*TODO: use section heading rule*/
  margin: 3rem 0 1.5rem;
}

.meta {
  display: flex;
}

.legend {
  composes: bodySmall from "@styles/new/font.module.css";
  
  display: flex;
  align-items: baseline;
  gap: .5em;
  padding: .25rem .5rem;
  border: var(--border);
  border-radius: var(--border-radius);
}

.legendTitle {
  color: var(--color-primary);
  letter-spacing: 0.001em;
  font-size: inherit;
  margin: 0 0 0.25em;
}
