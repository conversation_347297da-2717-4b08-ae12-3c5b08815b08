import { skipToken } from '@reduxjs/toolkit/dist/query'
import React, { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { ExportPropertySatelitePage } from '@components/export/pages/ExportPropertySatelitePage'
import { ExportPropertyTitlesSection } from '@components/export/sections/ExportPropertyTitlesSection'
import { LoadingOverlay } from '@components/generic'
import { useGetSalePDFDataQuery } from '@store/services/sale'
import type { SalePDFData } from '@models/sales/SalePDFData'
import { formatArea } from '@util'
import {
  ExportBreakdownTable,
  ExportControlOverlay,
  ExportDetailTable,
  ExportGrid,
} from './components'
import {
  ExportAttributions,
  ExportAttributionsItem,
} from './components/ExportAttributions'
import { ExportPage } from './components/ExportPage'
import { ExportParagraph } from './components/ExportParagraph'
import { ExportSection } from './components/ExportSection'
import { ExportWrapper } from './components/ExportWrapper'
import { CoverPage } from './pages/CoverPage'
import { ExportContourProfilePage } from './pages/ExportContourProfilePage'
import { ExportLUCPage } from './pages/ExportLUCPage'
import { ExportPSPage } from './pages/ExportPSPage'
import { ExportTitlePages } from './pages/ExportTitlePage'
import { ExportVegetationPage } from './pages/ExportVegetationPage'
import { LegalDisclaimerPage } from './pages/LegalDisclaimerPage'
import { ExportDistrictValuationRollSection } from './sections/ExportDistrictValuationRollSection'
import { ExportPropertyBoundariesSection } from './sections/ExportPropertyBoundariesSection'

export interface ExportSalePageRouteParams {
  addressId: string
}

export const ExportSaleView = () => {
  const { saleId = '' } = useParams()

  const { data, isFetching } = useGetSalePDFDataQuery({
    saleId: saleId ?? skipToken,
  })

  const {
    documentTitle,
    filename,
    center,
    sale,
    titles,
    districtValuationRoll,
    anzUnion,
    summary,
  } = useMemo(() => {
    return { ...data } as SalePDFData
  }, [data])

  document.title = filename

  const documentSubtitle = useMemo(() => {
    return (
      <React.Fragment>
        <div>{sale?.properties?.fullAddress}</div>
      </React.Fragment>
    )
  }, [sale])

  const labelType = useMemo(
    () => (sale?.properties?.status === 'LISTING' ? 'Listing' : 'Sale'),
    [sale]
  )

  if (!data) {
    return <LoadingOverlay data-testid="export-sale-page" />
  }

  return (
    <React.Fragment>
      <ExportControlOverlay isFetching={isFetching} />
      <ExportWrapper data-testid="export-sale-page">
        <CoverPage
          title={documentTitle}
          isFetching={isFetching}
          subtitle={documentSubtitle}
        />
        <LegalDisclaimerPage />
        <ExportPage
          title="Property Overview"
          subtitle="The titles of a section of land have been simplified
                            into broad categories to summarise the overall
                            profile of a given property."
        >
          <ExportGrid>
            <ExportSection title="Land Description">
              <ExportParagraph html={summary?.serviceCentres} />
              <ExportParagraph html={summary?.landDescription} />
            </ExportSection>
            <ExportSection title="Elevation Profile">
              <ExportParagraph html={summary?.elevation} />
            </ExportSection>
            {districtValuationRoll?.map((dvr) => (
              <ExportDistrictValuationRollSection
                key={dvr.dvrId}
                districtValuationRoll={dvr}
                isFetching={isFetching}
              />
            ))}
          </ExportGrid>
          <ExportAttributions>
            <ExportAttributionsItem type="valocity" />
            <ExportAttributionsItem type="linz" />
          </ExportAttributions>
        </ExportPage>
        {sale?.properties?.status !== 'LISTING' ? (
          <ExportPage title={`${labelType} Details`}>
            <ExportGrid>
              <ExportSection title={`Particulars of ${labelType}`}>
                <ExportDetailTable
                  data={[
                    {
                      label: `${labelType} Date`,
                      value: sale?.properties?.saleDate
                        ? new Date(sale?.properties?.saleDate)?.toDateString()
                        : '',
                    },
                    {
                      label: 'Purchaser',
                      value: sale?.properties?.purchaser,
                    },
                    {
                      label: 'Vendor Bank',
                      value: sale?.properties?.vendorBank,
                    },
                    {
                      label: 'Purchaser Bank',
                      value: sale?.properties?.purchaserBank,
                    },
                    {
                      label: 'Effective Area',
                      value: formatArea(
                        sale?.properties?.effectiveHa ?? 0,
                        'ha'
                      ),
                    },
                    {
                      label: 'Total Area',
                      value: formatArea(sale?.properties?.totalHa ?? 0, 'ha'),
                    },
                  ]}
                />
              </ExportSection>
              <ExportSection title="Breakdown of Price">
                <ExportBreakdownTable
                  totalLabel="Gross Sales Price"
                  data={[
                    {
                      label: 'Improvements Value',
                      value: sale?.properties?.improvementsValue ?? undefined,
                    },
                    {
                      label: 'Chattels, Stock & Other',
                      value: sale?.properties?.chattelsStockOther ?? undefined,
                    },
                    {
                      label: 'Notional Site Value',
                      value: sale?.properties?.notionalSiteValue ?? undefined,
                    },
                    {
                      label: 'Land Value Without Buildings',
                      value:
                        sale?.properties?.landWithoutBuildingsValue ??
                        undefined,
                    },
                  ]}
                />
              </ExportSection>
            </ExportGrid>
          </ExportPage>
        ) : null}
        <ExportPage
          title="Legal Definition and Boundaries"
          subtitle="This section summarises the legal definition of the property in regards to the property's legal titles and legal boundaries. "
        >
          <ExportPropertyTitlesSection
            isFetching={isFetching}
            titles={titles}
          />
          <ExportPropertyBoundariesSection
            center={center}
            isFetching={isFetching}
            titles={titles}
          />
        </ExportPage>
        <ExportTitlePages titles={titles} />
        <ExportPropertySatelitePage
          center={center}
          isFetching={isFetching}
          titles={titles}
        />
        <ExportLUCPage
          anzUnion={anzUnion}
          luc={summary?.anzUnion?.luc}
          center={center}
        />
        <ExportPSPage
          anzUnion={anzUnion}
          ps={summary?.anzUnion?.ps}
          center={center}
        />
        <ExportVegetationPage
          anzUnion={anzUnion}
          vegetation={summary?.anzUnion?.vegetation}
          center={center}
        />
        <ExportContourProfilePage
          center={center}
          isFetching={isFetching}
          titles={titles}
        />
      </ExportWrapper>
    </React.Fragment>
  )
}

export default ExportSaleView
