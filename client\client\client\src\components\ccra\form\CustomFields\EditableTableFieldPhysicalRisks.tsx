import {
  DeleteOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Input,
  Popconfirm,
  Radio,
  type RadioChangeEvent,
  Table,
  Tooltip,
} from 'antd'
import React, { useEffect, useState } from 'react'
import { RANKED_PHYSICAL_RISKS } from '../const'
import type { RankedPhysicalRiskItem } from '../types'
import styles from './EditableTable.module.scss'
import type { CheckboxChangeEvent } from 'antd/lib/checkbox/Checkbox'

const EditableTableFieldPhysicalRisks = ({
  fieldName,
}: { fieldName: string[] }) => {
  const form = Form.useFormInstance()
  const initialValue = form.getFieldValue(fieldName)
  const [orderedItems, setOrderedItems] = useState<RankedPhysicalRiskItem[]>(
    initialValue && initialValue.length > 0
      ? initialValue
      : RANKED_PHYSICAL_RISKS
  )

  const handleInputChange = (i: number, value: string) => {
    const newItems = [...orderedItems]
    newItems[i].measureTaken = value
    setOrderedItems(newItems)
  }

  const handleCheckboxChange = (i: number, e: CheckboxChangeEvent) => {
    const newItems = [...orderedItems]
    newItems[i].isInsured = e.target.checked
    setOrderedItems(newItems)
  }

  const handleRadioChange = (i: number, e: RadioChangeEvent) => {
    const newItems = [...orderedItems]
    newItems[i].materialToCustomer = e.target.value
    setOrderedItems(newItems)
  }

  const tableColumns = [
    {
      title: 'Risks',
      dataIndex: 'risk',
      key: 'risks',
      width: '220px',
      render: (risk: string, record: RankedPhysicalRiskItem) => (
        <span>
          {risk}
          {record.helpText && (
            <Tooltip title={record.helpText}>
              <QuestionCircleOutlined
                style={{ marginLeft: 6, position: 'relative', top: '-2px' }}
              />
            </Tooltip>
          )}
        </span>
      ),
    },
    {
      title: 'Material to customer?',
      dataIndex: 'materialToCustomer',
      key: 'materialToCustomer',
      width: '250px',
      render: (_: string, _record: RankedPhysicalRiskItem, i: number) => (
        <Radio.Group
          buttonStyle="solid"
          size="small"
          onChange={(e) => handleRadioChange(i, e)}
          defaultValue={orderedItems[i]?.materialToCustomer || null}
        >
          <Radio.Button value="Yes">Yes</Radio.Button>
          <Radio.Button value="No">No</Radio.Button>
          <Radio.Button value="Unsure">Unsure</Radio.Button>
          <Radio.Button value="N/A">N/A</Radio.Button>
        </Radio.Group>
      ),
    },
    {
      title: 'Measures taken to mitigate, if any',
      dataIndex: 'measureTaken',
      key: 'measureTaken',
      render: (_: string, record: RankedPhysicalRiskItem, index: number) => (
        <Input
          value={record.measureTaken}
          onChange={(e) => handleInputChange(index, e.target.value)}
          placeholder="Enter measures taken..."
        />
      ),
    },
    {
      title:
        "Has the customer advised they're insured for this (at least in part)",
      dataIndex: 'isInsured',
      key: 'isInsured',
      width: '150px',
      render: (
        isInsured: boolean,
        _record: RankedPhysicalRiskItem,
        i: number
      ) => (
        <Checkbox
          checked={isInsured}
          onChange={(e) => handleCheckboxChange(i, e)}
        />
      ),
    },
  ]

  useEffect(() => {
    form.setFieldValue(fieldName, orderedItems)
  }, [orderedItems, fieldName, form])

  return (
    <>
      <Table
        dataSource={orderedItems}
        columns={tableColumns}
        rowKey="index"
        pagination={false}
      />
      <Input type="hidden" value={JSON.stringify(orderedItems)} />
    </>
  )
}

export default EditableTableFieldPhysicalRisks
