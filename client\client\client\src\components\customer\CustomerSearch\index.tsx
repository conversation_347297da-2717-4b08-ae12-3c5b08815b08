import { LoadingOutlined, SearchOutlined } from '@ant-design/icons'
import { AutoComplete, type AutoCompleteProps, Empty, Input } from 'antd'
import type React from 'react'
import { useMemo, useState } from 'react'
import type { Customer } from '@store/services/sdk'
import { useDebounce } from '@util/useDebounce'
import useCustomerSearchHistory from './useCustomerSearchHistory'
import useCustomerSearchResults from './useCustomerSearchResults'
import styles from './styles.module.css'
import classNames from 'classnames'

type CustomerSegment = 'R' | 'W'

type CustomerSearchOption = {
  value: string
  customerId: number
  customerNumber: string
  entityName: string
  // groups customer belongs to
  tradingGroupId: string | undefined
  customerGroupId: number | undefined
  label: JSX.Element
}

type CustomerSearchProps = AutoCompleteProps<unknown, CustomerSearchOption> & {
  customerSetCode?: string
  segment?: CustomerSegment
}

export const PLACEHOLDER_TEXT = 'Customer Name or No.'

const toOption = ({
  customerId,
  customerNumber,
  entityName,
  tradingGroup,
  customerGroupId,
}: Customer): CustomerSearchOption => ({
  value: customerNumber.toString(),
  entityName,
  customerId,
  customerNumber,
  tradingGroupId: tradingGroup?.tradingGroupId,
  customerGroupId: customerGroupId,
  label: (
    <div className={styles.label}>
      <div>{entityName}</div>
      <div>{customerNumber}</div>
    </div>
  ),
})

const CustomerSearch = ({
  customerSetCode,
  segment,
  onSelect,
  ...props
}: CustomerSearchProps) => {
  const { searchHistory, addSearchHistory } = useCustomerSearchHistory()

  const [searchValue, setSearchValue] = useState('')
  const [selectedValue, setSelectedValue] = useState<string | undefined>()
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const { results, isFetching, isLoading } = useCustomerSearchResults(
    {
      match: debouncedSearchValue,
      customerSetCode,
      segment: segment as string,
    },
    {
      skip: !debouncedSearchValue,
    }
  )

  const options = useMemo(() => {
    const customers = debouncedSearchValue ? results : searchHistory
    return customers.map(toOption)
  }, [results, searchHistory, debouncedSearchValue])

  const loading = isFetching || isLoading

  return (
    <AutoComplete
      {...props}
      className={classNames(styles.container, props.className)}
      dropdownClassName={props.className}
      allowClear={true}
      value={searchValue || selectedValue}
      options={options}
      onClear={() => {
        props.onClear?.()
        setSearchValue('')
        setSelectedValue(undefined)
      }}
      onSearch={setSearchValue}
      onSelect={(_, option: CustomerSearchOption) => {
        addSearchHistory(option.customerId.toString())
        onSelect?.(option.customerNumber, option)
        setSearchValue('')
        setSelectedValue(option.entityName)
      }}
      notFoundContent={
        Boolean(debouncedSearchValue?.length) &&
        !loading && (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No customers found"
          />
        )
      }
    >
      <Input
        prefix={loading ? <LoadingOutlined /> : <SearchOutlined />}
        placeholder={PLACEHOLDER_TEXT}
      />
    </AutoComplete>
  )
}

export default CustomerSearch
