import { Form, Input, Select, Space } from 'antd'
import React, { useEffect, useMemo } from 'react'

export const VALIDATION_OPTIONS = [
  { value: 1, label: 'Reviewed – No Concerns' },
  { value: 2, label: 'Reviewed – With Concerns' },
]

export const COMMENT_OPTION = VALIDATION_OPTIONS[VALIDATION_OPTIONS.length - 1]

interface Props {
  initialValue?: {
    status?: 0 | 1 | 2
    comments?: string
  } | null
  path?: string | string[]
  status?: boolean
}

const FormReviewStatusItems = ({ initialValue, path = [], status }: Props) => {
  const form = Form.useFormInstance()
  const itemPath = useMemo(
    () => [...(Array.isArray(path) ? path : [path])],
    [path]
  )
  const commentPath = useMemo(() => [...itemPath, 'comments'], [itemPath])
  const statusPath = useMemo(() => [...itemPath, 'status'], [itemPath])
  const commentValue = Form.useWatch(commentPath, form)
  const selectValue = Form.useWatch(statusPath, form)

  useEffect(() => {
    if (form.isFieldsTouched()) {
      if (!commentValue) {
        form.setFieldValue(statusPath, undefined)
      } else {
        form.setFieldValue(statusPath, VALIDATION_OPTIONS[1].value)
      }
    }
  }, [form, commentValue, statusPath])

  return (
    <Space direction="vertical" size="small" style={{ width: '100%' }}>
      {status && (
        <Form.Item
          initialValue={initialValue?.status}
          noStyle
          name={statusPath}
          rules={[{ required: true }]}
        >
          <Select
            options={VALIDATION_OPTIONS}
            placeholder="Not Reviewed"
            style={{ width: '100%' }}
          />
        </Form.Item>
      )}
      <Form.Item
        initialValue={initialValue?.comments}
        noStyle
        name={commentPath}
        // rules={[{ required: commentOptionSelected }]}
      >
        <Input.TextArea
          rows={3}
          disabled={selectValue === 1}
          // required={commentOptionSelected}
        />
      </Form.Item>
    </Space>
  )
}

export default FormReviewStatusItems
