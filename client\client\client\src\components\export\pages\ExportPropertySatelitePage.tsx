import { TitlesLayer } from '@components/generic/TitlesLayer'
import type { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { ExportPage } from '../components'
import { ExportLegend, ExportLegendItem } from '../components/ExportLegend'
import { ExportMap } from '../components/ExportMap'
import { ExportSection } from '../components/ExportSection'

interface ExportPropertySatelitePageProps {
  titles: TitleFeatureCollection
  center: { lat: number; lng: number }
  isFetching: boolean
}

export const ExportPropertySatelitePage = (
  props: ExportPropertySatelitePageProps
) => {
  const { titles, isFetching, center } = props

  return (
    <ExportPage title="Aerial Imagery">
      <ExportSection>
        <ExportMap
          type="terrain"
          center={center}
          isFetching={isFetching}
          size="full"
        >
          {titles ? (
            <TitlesLayer position={1} type="map" titles={titles?.features} />
          ) : null}
        </ExportMap>
        <ExportLegend>
          <ExportLegendItem
            label="Title Boundary"
            borderColor="red"
            weight={3}
          />
        </ExportLegend>
      </ExportSection>
    </ExportPage>
  )
}
