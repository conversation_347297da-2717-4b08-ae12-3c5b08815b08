.container {
  --nav-padding-x: 12px;
  --nav-padding-y: 12px;

  z-index: 2;
  position: relative;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--nav-padding-y) var(--nav-padding-x);

  background-color: var(--secondary);
  border-bottom: 1px solid #004168;
  padding: var(--nav-padding-y) var(--nav-padding-x) 0;
  overflow: visible;

  > *:first-child {
    max-width: 700px;
  }
}

.container :global {
  .ant-btn-ghost {
    color: white;
  }

  .ant-btn:focus,
  .ant-btn-ghost:focus {
    border-color: #d9d9d9;
  }
}

.scroll {
  width: calc(100% + (var(--nav-padding-x) * 2));
  grid-row: 2;
  grid-column: 1 / -1;
  display: grid;
  grid-auto-flow: column;
  gap: var(--nav-padding-x);
  margin-left: calc(-1 * var(--nav-padding-x));
  padding: 0 var(--nav-padding-x);
  padding-bottom: var(--nav-padding-y);
  overflow-x: auto;
  overflow-y: hidden;
}

@media (max-width: 700px) {
  .container :global {
    .ExplorerExportPreviewToggleButton {
      display: none;
    }
  }

  .buttons {
    display: contents;
  }
}

@media (min-width: 700px) {
  .container {
    display: flex;
    padding-bottom: var(--nav-padding-y);
  }

  .scroll {
    display: contents;
  }

  .buttons {
    margin-right: auto;
  }
}

.extra {
  display: flex;
  gap: 8px;
  margin-left: auto;
}
