import React, { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { ExportPropertySatelitePage } from '@components/export/pages/ExportPropertySatelitePage'
import { ExportPropertyTitlesSection } from '@components/export/sections/ExportPropertyTitlesSection'
import { LoadingOverlay } from '@components/generic'
import type { AddressPDFData } from '../../models/address/AddressPDFData'
import { useGetAddressPDFDataQuery } from '../../store/services/address'
import { ExportControlOverlay, ExportGrid } from './components'
import {
  ExportAttributions,
  ExportAttributionsItem,
} from './components/ExportAttributions'
import { ExportPage } from './components/ExportPage'
import { ExportParagraph } from './components/ExportParagraph'
import { ExportSection } from './components/ExportSection'
import { ExportWrapper } from './components/ExportWrapper'
import { useExportTitleToggle } from './hooks/useExportToggle'
import { CoverPage } from './pages/CoverPage'
import { ExportContourProfilePage } from './pages/ExportContourProfilePage'
import { ExportLUCPage } from './pages/ExportLUCPage'
import { ExportPSPage } from './pages/ExportPSPage'
import { ExportTitlePages } from './pages/ExportTitlePage'
import { ExportVegetationPage } from './pages/ExportVegetationPage'
import { LegalDisclaimerPage } from './pages/LegalDisclaimerPage'
import { ExportDistrictValuationRollSection } from './sections/ExportDistrictValuationRollSection'
import { ExportPropertyBoundariesSection } from './sections/ExportPropertyBoundariesSection'

export interface ExportAddressPageRouteParams {
  addressId: string
}

export const ExportAddressView = () => {
  const { addressId = '' } = useParams<{ addressId: string }>()

  const [selectorElem, , excluded] = useExportTitleToggle()
  const { data, isFetching } = useGetAddressPDFDataQuery(
    { addressId, excluded },
    { skip: addressId === undefined }
  )

  const {
    documentTitle,
    filename,
    center,
    address,
    titles,
    districtValuationRoll,
    anzUnion,
    summary,
  } = useMemo(() => {
    return { ...data } as AddressPDFData
  }, [data])

  document.title = filename

  if (!data) {
    return <LoadingOverlay data-testid="export-address-page" />
  }

  return (
    <React.Fragment>
      <ExportControlOverlay isFetching={isFetching}>
        {selectorElem}
      </ExportControlOverlay>
      <ExportWrapper data-testid="export-address-page">
        <CoverPage
          title={documentTitle}
          isFetching={isFetching}
          subtitle={address?.properties?.fullAddress?.toLocaleLowerCase()}
        />
        <LegalDisclaimerPage />
        <ExportPage
          title="Property Overview"
          subtitle="The titles of a section of land have been simplified
                            into broad categories to summarise the overall
                            profile of a given property."
        >
          <ExportGrid>
            <ExportSection title="Elevation Profile">
              <ExportParagraph html={summary?.elevation} />
            </ExportSection>
            <ExportSection title="Land Description">
              <ExportParagraph html={summary?.serviceCentres} />
              <ExportParagraph html={summary?.landDescription} />
            </ExportSection>
            <ExportDistrictValuationRollSection
              districtValuationRoll={districtValuationRoll}
              isFetching={isFetching}
            />
          </ExportGrid>
          <ExportAttributions>
            <ExportAttributionsItem type="valocity" />
            <ExportAttributionsItem type="linz" />
          </ExportAttributions>
        </ExportPage>
        <ExportPage
          title="Legal Definition and Boundaries"
          subtitle="This section summarises the legal definition of the property in regards to the property's legal titles and legal boundaries. "
        >
          <ExportPropertyTitlesSection
            isFetching={isFetching}
            titles={titles}
          />
          <ExportPropertyBoundariesSection
            center={center}
            isFetching={isFetching}
            titles={titles}
          />
        </ExportPage>
        <ExportTitlePages titles={titles} />
        <ExportPropertySatelitePage
          center={center}
          isFetching={isFetching}
          titles={titles}
        />
        <ExportLUCPage
          anzUnion={anzUnion}
          luc={summary?.anzUnion?.luc}
          center={center}
        />
        <ExportPSPage
          anzUnion={anzUnion}
          ps={summary?.anzUnion?.ps}
          center={center}
        />
        <ExportVegetationPage
          anzUnion={anzUnion}
          vegetation={summary?.anzUnion?.vegetation}
          center={center}
        />
        <ExportContourProfilePage
          center={center}
          isFetching={isFetching}
          titles={titles}
        />
      </ExportWrapper>
    </React.Fragment>
  )
}

export default ExportAddressView
