import { AimOutlined } from '@ant-design/icons'
import { Menu, type MenuProps } from 'antd'
import { useCallback } from 'react'
import { useLeaflet } from 'react-leaflet'
import { useDispatch } from 'react-redux'
import { MenuLabelButton } from '@components/generic'
import { useSelector } from '@store'
import { getMenuPoint } from '@store/features/explorer'
import { mapApi } from '@store/services/map'
import { selectAddressFromLatLng } from './helpers'
import { close } from './menuItems'

interface Props extends MenuProps {
  id: string
}

const ToggleUnderlying = () => {
  const dispatch = useDispatch()

  const [trigger] = mapApi.endpoints.findAddressFromClick.useLazyQuery()

  const leaflet = useLeaflet()
  const point = useSelector(getMenuPoint)

  const handleClick = useCallback(() => {
    const { map } = leaflet
    if (!map) return
    void selectAddressFromLatLng(
      dispatch,
      trigger
    )(map.containerPointToLatLng(point))
  }, [dispatch, leaflet, point, trigger])

  return (
    <MenuLabelButton onClick={handleClick}>
      Toggle Underlying Address
    </MenuLabelButton>
  )
}

const items = [
  {
    key: 'toggleUnderlyingAddress',
    icon: <AimOutlined />,
    label: <ToggleUnderlying />,
  },
  close,
]

const ExplorerMapBaseMenu = ({ ...props }: Props) => {
  return <Menu items={items} {...props} />
}

export default ExplorerMapBaseMenu
