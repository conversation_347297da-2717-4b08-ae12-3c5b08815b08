import React from 'react'
import { ExportPage } from '@components/export/components'
import { ExportLandBestUseSummarySection } from '@components/export/sections/ExportLandBestUseSummarySection'
import type { Valuation } from '@types'

interface ExportLandBestUseSummaryPageProps {
  valuation: Valuation
}

const ExportLandBestUseSummaryPage = ({
  valuation,
}: ExportLandBestUseSummaryPageProps) => {
  return (
    <ExportPage className="ExportLandBestUseSummaryPage">
      <ExportLandBestUseSummarySection valuation={valuation} />
    </ExportPage>
  )
}

export default ExportLandBestUseSummaryPage
