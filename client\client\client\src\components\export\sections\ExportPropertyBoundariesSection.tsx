import React from 'react'
import { ViewportTitlesLayer } from '@components/map/layers/ViewportTitlesLayer'
import { TitleLayerProvider } from '@components/map/titleLayerContext'
import type { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { ExportLegend, ExportLegendItem } from '../components/ExportLegend'
import { ExportMap } from '../components/ExportMap'
import { ExportSection } from '../components/ExportSection'

interface ExportPropertyBoundariesSectionProps {
  titles: TitleFeatureCollection | undefined
  center: { lat: number; lng: number }
  isFetching: boolean
}

export const ExportPropertyBoundariesSection = (
  props: ExportPropertyBoundariesSectionProps
) => {
  const { titles, isFetching, center } = props

  if (!titles) {
    return null
  }

  return (
    <ExportSection isFetching={isFetching} title="Defined Boundaries">
      <TitleLayerProvider key="property-boundaries">
        <ExportMap
          type="road"
          center={center}
          isFetching={isFetching}
          size="half"
        >
          {titles && (
            <ViewportTitlesLayer
              position={1}
              titles={titles}
              exportMode
              forcedTitleMode
            />
          )}
        </ExportMap>
      </TitleLayerProvider>
      <ExportLegend>
        <ExportLegendItem
          label="Title Boundary"
          borderColor="blue"
          weight={3}
        />
      </ExportLegend>
    </ExportSection>
  )
}
