import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import {
  But<PERSON>,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Select,
  Table,
} from 'antd'
import moment from 'moment'
import type React from 'react'
import { useEffect, useRef, useState } from 'react'
import { getFieldName } from '../helpers'
import { EMISSIONS_DATA_OUTPUT_UNITS } from '../const'

interface DataType {
  key: React.Key
  yearEnd: string | null
  quantity: number | null
  unit: string
  tag?: string
}

const KG_MILK_SOLIDS_UNIT =
  EMISSIONS_DATA_OUTPUT_UNITS.find((unit) => unit.value === 'kgMilkSolids')
    ?.value || ''

const { Option } = Select

const EditableTableFieldRevenueOrOutput = ({
  fieldName,
}: {
  fieldName: string[]
}) => {
  const form = Form.useFormInstance()
  const emissionsMeasurementMethodValue = Form.useWatch(
    getFieldName('emissionsMeasurementMethod'),
    form
  )
  const kgmsValue = Form.useWatch(getFieldName('kgms'), form)
  const synlaitKgmsValue = Form.useWatch(getFieldName('synlaitKgms'), form)
  const initialMount = useRef(true)
  const initialValue = useRef(form.getFieldValue(fieldName))

  const [count, setCount] = useState(
    initialValue.current ? initialValue.current.length : 0
  )

  const [dataSource, setDataSource] = useState<DataType[]>(
    initialValue.current ? initialValue.current : []
  )

  const handleAdd = () => {
    const newData = {
      key: count + 1,
      yearEnd: null,
      quantity: 0,
      unit: '',
    }
    setDataSource([...dataSource, newData])
    setCount(count + 1)
  }

  const handleSave = (row: DataType) => {
    const newData = [...dataSource]
    const index = newData.findIndex((item) => row.key === item.key)
    const item = newData[index]
    newData.splice(index, 1, { ...item, ...row })
    setDataSource(newData)
  }

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter((item) => item.key !== key)
    setDataSource(newData)
  }

  useEffect(() => {
    form.setFieldValue(fieldName, dataSource)
  }, [dataSource, fieldName, form])

  // biome-ignore lint/correctness/useExhaustiveDependencies: infinite rerender loop
  useEffect(() => {
    const updateDataSource = (tag: string, value: number) => {
      const existingIndex = dataSource.findIndex(
        (item) => item.tag === `${tag}Kgms`
      )

      let yearEnd =
        existingIndex !== -1 ? dataSource[existingIndex].yearEnd : null
      const rowKey =
        existingIndex !== -1 ? dataSource[existingIndex].key : count + 1

      if (initialMount.current && initialValue.current) {
        yearEnd = initialValue.current[0]?.yearEnd ?? null
        initialMount.current = false
      }

      const newData = {
        key: rowKey,
        yearEnd,
        quantity: value,
        unit: KG_MILK_SOLIDS_UNIT,
        tag: `${tag}Kgms`,
      }

      if (existingIndex !== -1) {
        const updatedDataSource = [...dataSource]
        updatedDataSource[existingIndex] = newData
        setDataSource(updatedDataSource)
      } else {
        setDataSource([newData, ...dataSource])
        setCount(count + 1)
      }
    }

    if (emissionsMeasurementMethodValue === 'Fonterra' && kgmsValue) {
      updateDataSource('fonterra', kgmsValue)
    } else {
      setDataSource((prevDataSource) =>
        prevDataSource.filter((item) => item.tag !== 'fonterraKgms')
      )
    }

    if (emissionsMeasurementMethodValue === 'Synlait' && synlaitKgmsValue) {
      updateDataSource('synlait', synlaitKgmsValue)
    } else {
      setDataSource((prevDataSource) =>
        prevDataSource.filter((item) => item.tag !== 'synlaitKgms')
      )
    }
  }, [emissionsMeasurementMethodValue, kgmsValue, synlaitKgmsValue])

  const columns = [
    {
      title: 'Year End',
      dataIndex: 'yearEnd',
      width: 150,
      render: (_: string, record: DataType) => (
        <DatePicker
          value={record.yearEnd ? moment(record.yearEnd, 'YYYY') : null}
          onChange={(_date, dateString) =>
            handleSave({ ...record, yearEnd: dateString })
          }
          picker="year"
        />
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      render: (value: number, record: DataType) => (
        <InputNumber
          value={value}
          defaultValue={3}
          onChange={(value) => handleSave({ ...record, quantity: value })}
          precision={2}
        />
      ),
    },
    {
      title: 'Unit',
      dataIndex: 'unit',
      render: (text: string, record: DataType) => (
        <Select
          value={text}
          onChange={(value) => handleSave({ ...record, unit: value })}
          style={{ width: 120 }}
        >
          {EMISSIONS_DATA_OUTPUT_UNITS.map(({ value, label }) => (
            <Option key={label} value={value}>
              {label}
            </Option>
          ))}
        </Select>
      ),
    },
    {
      title: 'Actions',
      dataIndex: 'actions',
      render: (_: string, record: DataType) =>
        dataSource.length >= 1 ? (
          <Popconfirm
            title="Are you sure you want to delete this record?"
            onConfirm={() => handleDelete(record.key)}
          >
            <Button icon={<DeleteOutlined />} />
          </Popconfirm>
        ) : null,
    },
  ]

  return (
    <>
      <Table
        dataSource={dataSource}
        columns={columns}
        rowClassName="editable-row"
        pagination={false}
        locale={{
          emptyText: (
            <span>
              Please click on Add Row below to add revenue or output emissions
              data
            </span>
          ),
        }}
      />
      <Button
        onClick={handleAdd}
        icon={<PlusOutlined />}
        style={{ marginTop: 16 }}
      >
        Add row
      </Button>
      <Input type="hidden" />
    </>
  )
}

export default EditableTableFieldRevenueOrOutput
