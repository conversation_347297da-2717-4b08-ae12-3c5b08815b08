import { Dropdown } from 'antd'
import { createRef } from 'react'
import ExplorerSelectedValocitySaleMenu from '@components/explorer/ExplorerSelectedValocitySaleMenu'
import { useValocitySalesRetrieveQuery } from '@store/services/sdk'
import { formatArea } from '@util'
import { skipArgObject } from '@util/helpers'

interface SelectedValocitySaleItemProps {
  valocitySaleId: number
}

export const SelectedValocitySaleItem = ({
  valocitySaleId,
}: SelectedValocitySaleItemProps) => {
  const ref = createRef<HTMLTableRowElement>()

  const { data: valocitySale } = useValocitySalesRetrieveQuery(
    skipArgObject({ pk: valocitySaleId })
  )

  if (!valocitySale) return null

  return (
    <Dropdown
      overlay={<ExplorerSelectedValocitySaleMenu id={valocitySaleId} />}
      placement="bottom"
      trigger={['click']}
    >
      <tr className="map-container-list-item" ref={ref}>
        <td>{valocitySale?.properties?.fullAddress}</td>
        <td>{valocitySale?.properties?.saleDate}</td>
        <td>{formatArea(valocitySale?.properties?.totalHa ?? 0, 'ha')}</td>
        <td>{valocitySale?.properties?.grossSalesPrice}</td>
      </tr>
    </Dropdown>
  )
}
