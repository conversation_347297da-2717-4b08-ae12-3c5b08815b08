import moment, { type Moment } from 'moment'

export type DateInputValue = Moment | string | undefined | null

export type DateInputRangeValue = [DateInputValue, DateInputValue] | undefined

export const toISOString = (date: DateInputValue) => {
  if (date === undefined) return undefined
  return moment.isMoment(date) ? date.toISOString() : null
}

export const toFormattedString = (format: string) => (date: DateInputValue) => {
  if (date === undefined) return undefined
  if (moment.isMoment(date)) {
    return date.format(format)
  }
  return null
}

export const fromString = (date: DateInputValue) => {
  if (date === undefined) return undefined
  if (date == null || moment.isMoment(date)) return date
  const newDate = moment(date)
  if (!moment.isMoment(newDate)) return null
  return newDate
}

export const fromFormattedString =
  (format: string) => (date: DateInputValue) => {
    if (date === undefined) return undefined
    if (date == null || moment.isMoment(date)) return date
    const newDate = moment(date, format)
    if (!moment.isMoment(newDate)) return null
    return newDate
  }
