import type React from 'react'
import { But<PERSON>, Form, Popconfirm } from 'antd'
import Divider from './Divider'
import classNames from 'classnames'
import styles from './Submit.module.css'
import { useFinanceAgriUpliftDestroyMutation } from '@store/services/finance/codegen'
import { useNavigate } from 'react-router-dom'
import useDestroyMutation from '@hooks/useDestroyMutation'
import { is } from 'date-fns/locale'

type Props = React.HTMLProps<HTMLDivElement> & {
  pk: number | undefined
  saveDraft: () => void
  isDraftOrNew: boolean
  loading?: boolean
}

const Submit = ({
  pk,
  loading,
  isDraftOrNew,
  saveDraft,
  className,
  ...props
}: Props) => {
  const form = Form.useFormInstance()

  const navigate = useNavigate()
  const [deleteTrigger, deleteStatus] = useDestroyMutation(
    useFinanceAgriUpliftDestroyMutation
  )

  return (
    <div {...props} className={classNames(styles.container, className)}>
      <Divider />
      <Form.Item shouldUpdate>
        {() => {
          const touched = form.isFieldsTouched()
          const disabledTitle = !touched
            ? 'No changes'
            : loading
              ? 'Loading...'
              : undefined
          return (
            <div className={styles.buttons}>
              <Popconfirm
                title={
                  <div className={styles.submitWarning}>
                    Submitting a deal will place it into a 'Completed' state,
                    this means you will not be able to make further changes to
                    it. Are you sure?
                  </div>
                }
                disabled={!isDraftOrNew || !touched || loading}
                onConfirm={() => {
                  form.submit()
                }}
              >
                <Button
                  htmlType="submit"
                  type="primary"
                  loading={loading}
                  disabled={!isDraftOrNew || !touched || loading}
                  title={disabledTitle}
                >
                  Submit
                </Button>
              </Popconfirm>
              {isDraftOrNew ? (
                <Button
                  onClick={() => saveDraft()}
                  loading={loading}
                  disabled={!touched || loading}
                  title={disabledTitle}
                >
                  Save Draft
                </Button>
              ) : null}
              <div className={styles.buttonsRight}>
                <Popconfirm
                  title="Are you sure?"
                  disabled={!isDraftOrNew || !touched || loading}
                  onConfirm={() => form.resetFields()}
                >
                  <Button
                    disabled={!isDraftOrNew || !touched || loading}
                    loading={loading}
                    className={styles.reset}
                  >
                    Reset
                  </Button>
                </Popconfirm>
                {pk !== undefined && (
                  <Popconfirm
                    title="Are you sure?"
                    disabled={
                      !isDraftOrNew || loading || deleteStatus.isLoading
                    }
                    onConfirm={async () => {
                      await deleteTrigger({ pk })
                      navigate('/finance/agri-uplift')
                    }}
                  >
                    <Button
                      disabled={
                        !isDraftOrNew || loading || deleteStatus.isLoading
                      }
                      loading={deleteStatus.isLoading}
                      className={styles.delete}
                      danger
                    >
                      Delete
                    </Button>
                  </Popconfirm>
                )}
              </div>
            </div>
          )
        }}
      </Form.Item>
    </div>
  )
}

export default Submit
