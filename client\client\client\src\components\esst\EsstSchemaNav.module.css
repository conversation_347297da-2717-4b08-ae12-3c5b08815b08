.container {
  --color-muted: rgb(from var(--color-white) r g b / 80%);
  --nav-inset: calc(var(--inset-x) + var(--space-0));

  display: flex;
  flex-direction: column;
  row-gap: var(--space-4);
  padding: var(--space-3) var(--nav-inset) var(--space-2);
  margin: 0 calc(-1 * var(--nav-inset));
  background: var(--color-ui-blue);
  border-radius: var(--border-radius);
}

.list {
  display: flex;
  align-items: baseline;
  row-gap: var(--space-4);
  margin: 0;
  padding: 0;

  list-style: none;
  counter-reset: schema;
}

.item {
    flex: 1;
    position: relative;
    margin-right: var(--space-3);

    &:before {
      content: '';
      position: absolute;
      top: 10px;
      left: 0;
      right: 0;
      border-top: 1px solid var(--color-border-blue);
    }
}

.link,
.sectionLink {
  > *,
  &:before {
    color: var(--color-muted);
  }

  &:global(.active),
  &:hover {
    > *,
    &:before {
      color: var(--color-white);
    }
  }
}

.sectionLink {
  display: flex;
  flex-direction: column;
  align-items: start;
  text-align: left;
  text-decoration: none !important;

  &:before {
    --counter-offset: .5rem;
    --counter-diameter: 21px;

    counter-increment: schema;
    content: counter(schema);

    position: relative;
    width: var(--counter-diameter);
    height: var(--counter-diameter);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: calc(var(--counter-offset));

    font-size: 14px;

    background-color: var(--color-ui-blue);
    box-shadow: 0 0 0 var(--counter-offset) var(--color-ui-blue);
    border: 1px solid currentColor;
    border-radius: 50%;
  }

  &:global(.active) {
    &:before {
      color: var(--color-ui-blue);
      border-color: var(--color-white);
      background: white;
    }
  }
}

.title,
.subtitle {
  line-height: 1.2;
}

.title {
  position: relative;
  padding-right: 1.5rem;
  margin-bottom: var(--space-0);
}

.loading {
  position: absolute;
  top: 0;
  right: 0;
}

.sectionLink:not(:global(.activeSaving)) {
  .loading {
    display: none;
  }
}


.heading {
  color: var(--color-muted);
  line-height: 1;
  margin: 0 0 var(--space-6) !important;
}

.footer {
  display: flex;
  gap: var(--space-4);
}
