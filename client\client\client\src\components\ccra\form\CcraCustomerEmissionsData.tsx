import { Col, Form, Input, InputNumber, Radio, Row, Select, Tag } from 'antd'
import { useEffect } from 'react'
import EditableTableFieldRevenueOrOutput from './CustomFields/EditableTableFieldRevenueOrOutput'
import ProcessFuels from './CustomFields/ProcessFuels'
import { getFieldName, getUnitForField } from './helpers'
import {
  CCRA_AGRI_FORM_FIELD,
  EMISSIONS_MEASUREMENT_METHOD,
  EMISSIONS_MEASUREMENT_PLAN,
  EMISSIONS_VERIFICATION_METHOD,
  ROW_PROPS,
} from './const'
import ReportingPeriod from './ReportingPeriod'

import { FileInput } from '@components/form'
import CcraFormItem from './CustomFields/CcraFormItem'
import { FormSection } from './CustomFields/CcraFormComponents'
import EmissionsReportFields from './EmissionsReportFields'
import styles from './CcraCustomerEmissionsData.module.css'

const { TextArea } = Input

const CustomerEmissionsData = () => {
  const form = Form.useFormInstance()
  const businessUnit = Form.useWatch('businessUnit', form)
  const knowsEmissionsValue = Form.useWatch(
    getFieldName('knowsEmissions'),
    form
  )
  const co2MtValue = Form.useWatch(getFieldName('co2Mt'), form)
  const synlaitCo2eKgmsValue = Form.useWatch(
    getFieldName('synlaitCo2eKgms'),
    form
  )
  const hasCarbonCreditsValue = Form.useWatch(
    getFieldName('hasCarbonCredits'),
    form
  )
  const newAttachmentsValue = Form.useWatch(
    getFieldName('newAttachments'),
    form
  )

  useEffect(() => {
    form.setFieldValue(
      getFieldName('co2eKg'),
      co2MtValue ? Math.round(co2MtValue * 1000) : undefined
    )

    form.setFieldValue(
      getFieldName('synlaitKgms'),
      co2MtValue && synlaitCo2eKgmsValue
        ? Math.round((co2MtValue * 1000) / synlaitCo2eKgmsValue)
        : undefined
    )
  }, [co2MtValue, synlaitCo2eKgmsValue, form])

  return (
    <div>
      <Row {...ROW_PROPS}>
        <Col span={24}>
          <CcraFormItem fieldName="knowsEmissions">
            <Radio.Group buttonStyle="solid">
              <Radio.Button value={true}>Yes</Radio.Button>
              <Radio.Button value={false}>No</Radio.Button>
            </Radio.Group>
          </CcraFormItem>
          {knowsEmissionsValue && (
            <>
              <CcraFormItem fieldName="emissionsMeasurementMethod">
                <Select
                  placeholder="Select a value..."
                  options={EMISSIONS_MEASUREMENT_METHOD}
                  className={styles.selectInput}
                />
              </CcraFormItem>
              <CcraFormItem fieldName="emissionsVerificationMethod">
                <Select
                  className={styles.selectInput}
                  placeholder="Select a value..."
                  options={EMISSIONS_VERIFICATION_METHOD}
                />
              </CcraFormItem>
              <FormSection
                title="Emissions Data Entry"
                tooltip="See KnowHow for worked examples"
              >
                <CcraFormItem fieldName="periodStartEndDate">
                  <ReportingPeriod />
                </CcraFormItem>
              </FormSection>

              <EmissionsReportFields />

              <CcraFormItem fieldName="tradingGroupRevenueReportingPercentage">
                <InputNumber
                  addonAfter={getUnitForField(
                    'tradingGroupRevenueReportingPercentage'
                  )}
                  min={0}
                  max={100}
                  precision={2}
                />
              </CcraFormItem>
              <CcraFormItem fieldName="newAttachments" isRequired={false}>
                <FileInput />
              </CcraFormItem>

              {Boolean(newAttachmentsValue?.length) && (
                <Tag
                  style={{ position: 'relative', top: '-25px' }}
                  color={'success'}
                >
                  File Attached
                </Tag>
              )}

              {businessUnit === CCRA_AGRI_FORM_FIELD && (
                <FormSection title="Production Data">
                  <CcraFormItem fieldName="revenueOrOutput" isRequired={false}>
                    <EditableTableFieldRevenueOrOutput
                      fieldName={getFieldName('revenueOrOutput')}
                    />
                  </CcraFormItem>
                  <CcraFormItem fieldName="processFuels" isRequired={false}>
                    <ProcessFuels />
                  </CcraFormItem>
                </FormSection>
              )}
              <FormSection title="Carbon Credits">
                <CcraFormItem fieldName="hasCarbonCredits">
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value={true}>Yes</Radio.Button>
                    <Radio.Button value={false}>No</Radio.Button>
                  </Radio.Group>
                </CcraFormItem>
                <CcraFormItem
                  fieldName="carbonCreditsDetail"
                  isRequired={!!hasCarbonCreditsValue}
                  disabled={!hasCarbonCreditsValue}
                >
                  <TextArea
                    className={styles.selectInput}
                    disabled={!hasCarbonCreditsValue}
                  />
                </CcraFormItem>
              </FormSection>
            </>
          )}
          {knowsEmissionsValue === false && (
            <>
              <CcraFormItem fieldName="emissionsMeasurementPlan">
                <Select
                  className={styles.selectInput}
                  placeholder="Select a value..."
                  options={EMISSIONS_MEASUREMENT_PLAN}
                />
              </CcraFormItem>
              {businessUnit === CCRA_AGRI_FORM_FIELD && (
                <CcraFormItem fieldName="processFuels" isRequired={false}>
                  <ProcessFuels />
                </CcraFormItem>
              )}
            </>
          )}
        </Col>
      </Row>
    </div>
  )
}

export default CustomerEmissionsData
