import { message } from 'antd'
import type React from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useGetAddressTitlesQuery } from '../../../store/services/address'
import { ExportControlWidget, ExportControlWidgetList } from '../components'
import { ExportControlWidgetListItem } from '../components/ExportControlWidgetList'

export const useExportTitleToggle = (): [
  React.ReactNode,
  Array<number>,
  Array<number>,
] => {
  const { addressId = '' } = useParams()

  const { data: titles } = useGetAddressTitlesQuery(addressId)

  const [selected, setSelected] = useState<{ [titleId: number]: boolean }>({})

  useEffect(() => {
    setSelected((prev) => {
      const newState: typeof selected = { ...prev }
      for (const title of titles?.features || []) {
        const titleId = (title?.id || 0) as number
        newState[titleId] = true
      }
      return newState
    })
  }, [titles])

  const [included, excluded] = useMemo(() => {
    const included: Array<number> = []
    const excluded: Array<number> = []

    for (const titleId of Object.keys(selected)) {
      const id = Number(titleId)
      if (selected[id]) {
        included.push(id)
      } else {
        excluded.push(id)
      }
    }

    return [included, excluded]
  }, [selected])

  const toggleTitle = (titleId: number | undefined) => {
    if (titleId) {
      setSelected((prev) => {
        const newState: typeof selected = { ...prev }
        newState[titleId] = !newState[titleId]

        const count = Object.values(newState).filter((x) => x === true)
        if (count.length === 0) {
          message.error(
            "Can't select less than 1 title, select another title first and try again."
          )
          return prev
        }

        return newState
      })
    }
  }

  const elem =
    titles && titles?.features?.length > 1 ? (
      <ExportControlWidget
        title="Titles Report Included"
        hint="Customise the included titles within this report, clicking an entry below will deselect it."
      >
        <ExportControlWidgetList>
          {titles?.features?.map((title) => {
            return (
              <ExportControlWidgetListItem
                data-selected={
                  title?.id ? selected[title?.id as number] : false
                }
                key={title?.id}
                onClick={() => toggleTitle(title?.id as number)}
              >
                {title?.properties?.titleNo}
              </ExportControlWidgetListItem>
            )
          })}
        </ExportControlWidgetList>
      </ExportControlWidget>
    ) : null

  return [elem, included, excluded]
}
