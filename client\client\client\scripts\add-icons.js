const fs = require('node:fs')
const path = require('node:path')

const FILL_COLOR = '#0572E6'

const filePaths = process.argv.slice(2)

if (!filePaths.length) {
  console.error('Usage: node add-icons.js <...svgFilePath>')
  process.exit(1)
}

function toPascalCase(str) {
  return str
    .replace(/(^\w|[A-Z]|\b\w|\s+)/g, (match, index) =>
      index === 0 ? match.toUpperCase() : match.toUpperCase()
    )
    .replace(/\s+/g, '')
}

function addIcon(filePath) {
  const fileName = filePath.replace(/^.*[\\\/]/, '').replace(/\.[^/.]+$/, '')
  const componentName = toPascalCase(fileName)

  const svgContent = fs.readFileSync(filePath, 'utf-8')

  // Replace fill attribute with 'currentColor' if it matches the given hex code
  const updatedSvgData = svgContent
    .replace(new RegExp(`fill="${FILL_COLOR}"`, 'gi'), 'fill="currentColor"')
    .replace(/<svg.+>/, '')
    .replace(/<\/svg>/, '')
    .trim()

  const reactComponent = `import type React from 'react';

/**
 * ${fileName} Icon
 */
export default function ${componentName}(props: React.HtmlHTMLAttributes<SVGElement>) {
  return (
    <svg
      viewBox="0 0 96 96"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <title>${fileName} Icon</title>
      ${updatedSvgData}
    </svg>
  );
}
`
  const outputFilePath = path.join(
    'src',
    'components',
    'icons',
    `${componentName}.tsx`
  )
  fs.writeFileSync(outputFilePath, reactComponent, 'utf-8')

  console.log(`React component created at ${outputFilePath}`)
}

for (const filePath of filePaths) {
  addIcon(filePath)
}
