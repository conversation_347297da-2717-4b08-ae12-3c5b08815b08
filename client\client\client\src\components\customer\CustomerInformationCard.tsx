import { Card, type CardProps, Descriptions, Table } from 'antd'
import DescriptionsItem from 'antd/lib/descriptions/Item'
import { Link, useParams } from 'react-router-dom'
import ErrorBounds from '@components/ErrorBounds'
import {
  type Customer,
  useCustomerGroupRetrieveQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import styles from './CustomerInformationCard.module.scss'

export const CustomerTable = ({ customers }: { customers: Customer[] }) => {
  const { customerId } = useParams<{ customerId: string }>()

  return (
    <Table
      style={{ marginTop: '16px' }}
      pagination={false}
      className={'unstyle'}
      size="small"
      dataSource={customers ?? []}
      rowKey={({ customerId }) => Number(customerId)}
      key={'customerId'}
      rowClassName={(row) =>
        row?.customerId === Number(customerId)
          ? styles.selectedCustomer
          : 'null'
      }
      columns={[
        {
          dataIndex: 'entityName',
          title: 'Name',
          render: (value, record) => (
            <Link to={`/customer/${record.customerId || ''}/`}>{value}</Link>
          ),
        },
        {
          dataIndex: ['tradingGroup', 'tradingGroupName'],
          title: 'Trading Group',
        },
        { dataIndex: 'customerNumber', title: 'Customer Number' },
        { dataIndex: 'anzsic', title: 'ANZSIC' },
        { dataIndex: 'customerSetCode', title: 'Set Code' },
      ]}
    />
  )
}

export const CustomerInformationCard = ({ children, ...props }: CardProps) => {
  const { customerId } = useParams<{ customerId: string }>()
  const {
    data: group,
    isFetching,
    isLoading,
    error,
  } = useCustomerGroupRetrieveQuery(skipArgObject({ pk: Number(customerId) }))

  const descriptions = [
    { label: 'Group Name', value: group?.name },
    { label: 'Source System', value: group?.segment },
  ]

  return (
    <Card
      title="Group Information"
      {...props}
      loading={isFetching || isLoading}
    >
      <ErrorBounds error={error}>
        <Descriptions
          bordered={true}
          column={1}
          size="small"
          colon={false}
          labelStyle={{ fontWeight: 'bold', color: '#444' }}
        >
          {descriptions.map((description) => {
            return (
              <DescriptionsItem
                key={description.label}
                label={description.label}
              >
                {description.value}
              </DescriptionsItem>
            )
          })}
        </Descriptions>
        <CustomerTable customers={group?.customers ?? []} />
      </ErrorBounds>
    </Card>
  )
}
