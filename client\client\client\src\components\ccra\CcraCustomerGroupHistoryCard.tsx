import {
  type <PERSON><PERSON><PERSON>,
  useCcraCcraListQuery,
  useCcraCcraDestroyMutation,
} from '@store/services/sdk'
import { Button, Card, Popconfirm, Table, Tooltip, message } from 'antd'
import { useMemo, type Dispatch, type SetStateAction } from 'react'
import { useParams } from 'react-router-dom'
import type { CcraFormStatus } from './form/types'
import { InfoCircleOutlined } from '@ant-design/icons'
import StatusTag from './StatusTag'
import moment from 'moment'

interface CcraCustomerGroupHistoryCardProps {
  setView: Dispatch<SetStateAction<string>>
  setPk: Dispatch<SetStateAction<number | undefined>>
}

const CcraCustomerGroupHistoryCard = ({
  setView,
  setPk,
}: CcraCustomerGroupHistoryCardProps) => {
  const { customerId } = useParams()

  const { data: ccraData } = useCcraCcraListQuery({
    customerId: Number(customerId),
  })

  const [deleteCcra] = useCcraCcraDestroyMutation()

  const handleDelete = async (id: number) => {
    try {
      await deleteCcra({ pk: id }).unwrap()
      void message.success('Customer Climate Information deleted')
    } catch (error) {
      void message.error(error)
    }
  }

  const columns = useMemo(
    () => [
      {
        title: 'Customers',
        dataIndex: 'customerNames',
        key: 'customerNames',
        render: (customers: string) => customers || 'Unknown',
      },
      {
        title: 'Last Updated',
        dataIndex: 'dateCompleted',
        key: 'dateCompleted',
        render: (date: string) =>
          date && moment(date).format('DD/MM/YYYY - h:mmA'),
      },
      { title: 'Creator', dataIndex: 'creatorName', key: 'creatorName' },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        render: (status: CcraFormStatus) => <StatusTag status={status} />,
      },
      {
        title: '',
        dataIndex: 'ccraId',
        key: 'ccraId',
        render: (ccraId: number, ccra: Ccra) => (
          <div style={{ display: 'flex' }}>
            {ccra.status === 'complete' && ccra.canEdit && (
              <Button
                type="link"
                onClick={() => {
                  setPk(ccraId)
                  setView('editForm')
                }}
              >
                <Tooltip
                  placement="top"
                  title={`The ability to edit is available within 60 days from completion
                    (e.g. to upload an emissions report you have received).
                    For major changes or changes outside that timeframe,
                    please complete a new Customer Climate Information submission.`}
                >
                  <InfoCircleOutlined /> Edit
                </Tooltip>
              </Button>
            )}
            <Button
              type="link"
              onClick={() => {
                setPk(ccraId)
                if (ccra.status === 'incomplete') {
                  setView('editForm')
                } else {
                  setView('detail')
                }
              }}
            >
              {ccra.status === 'incomplete' ? 'Edit' : 'View'}
            </Button>
            {ccra.status !== 'complete' && (
              <Popconfirm
                title="Are you sure you want to delete? This process cannot be undone"
                onConfirm={() => {
                  handleDelete(ccraId)
                }}
              >
                <Button type="link" danger>
                  Delete
                </Button>
              </Popconfirm>
            )}
          </div>
        ),
      },
    ],
    // biome-ignore lint/correctness/useExhaustiveDependencies: Can't change with confidence
    [setPk, setView, handleDelete]
  )

  const dataSource = ccraData?.map((item) => ({
    ...item,
    key: item.id,
    customerNames: item.customerNames || 'No Name Available',
    dateCompleted: item.updatedDatetime || 'No Date',
    status: item.status,
    ccraId: item.id,
    creatorName: item.creatorName || '',
  }))

  return (
    <Card title="Completed Customer Climate Information">
      <Table columns={columns} dataSource={dataSource} className="unstyle" />
    </Card>
  )
}

export default CcraCustomerGroupHistoryCard
