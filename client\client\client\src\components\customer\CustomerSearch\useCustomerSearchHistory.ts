import { skipArgObject } from '@util/helpers'
import { useCallback, useMemo } from 'react'
import { useLocalStorage } from 'react-use'
import useCustomerSearchResults from './useCustomerSearchResults'

export const STORAGE_KEY = 'customer-search-history-ids'

function useCustomerSearchHistory() {
  const [savedHistory, setSavedHistory] = useLocalStorage<Set<string>>(
    STORAGE_KEY,
    new Set(),
    {
      raw: false,
      serializer: (value) => JSON.stringify([...value]),
      deserializer: (value) => {
        return new Set(JSON.parse(value))
      },
    }
  )

  const searchHistoryIds = useMemo(() => {
    const ids = savedHistory || new Set()
    return Array.from(ids).join(',')
  }, [savedHistory])

  const addSearchHistory = useCallback(
    (newEntry: string) => {
      setSavedHistory(savedHistory?.add(newEntry))
    },
    [savedHistory, setSavedHistory]
  )

  const { results, isFetching, isLoading } = useCustomerSearchResults(
    skipArgObject({
      ids: searchHistoryIds,
    })
  )

  return {
    searchHistory: results,
    isLoading: isFetching || isLoading,
    addSearchHistory,
  }
}

export default useCustomerSearchHistory
