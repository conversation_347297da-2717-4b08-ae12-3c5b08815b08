import { Alert, Empty, Tooltip } from 'antd'
import React from 'react'
import { Outlet, useParams } from 'react-router-dom'
import { useCustomerRetrieveQuery } from '@store/services/sdk'
import { LoadingContainer } from '@components/layout'
import useEntitlements from '@hooks/useEntitlements'
import { CUSTOMER_BENCHMARKING_VIEW_ENTITLEMENTS } from '@util/entitlements'
import { skipArgObject } from '@util/helpers'
import CustomerDrawer from '../CustomerDrawer'
import CustomerReportDrawerButton from '../CustomerReportDrawerButton'
import { CustomerProvider } from '../context'
import {
  CUSTOMER_REPORT_DISABLED_MESSAGE,
  CUSTOMER_REPORT_ENABLED,
} from '../static'
import HeaderBar from './HeaderBar'
import Sidebar from './Sidebar'
import styles from './styles.module.css'

// Could store this as an admin setting on the server or something
const alertMessage = ''

const CustomerLayout = () => {
  const { customerId } = useParams()

  const { data: customer, isLoading } = useCustomerRetrieveQuery(
    skipArgObject({ pk: customerId as number | undefined })
  )

  const hasBenchmarkingPermission = useEntitlements(
    CUSTOMER_BENCHMARKING_VIEW_ENTITLEMENTS
  )

  return (
    <>
      {Boolean(alertMessage) && (
        <Alert
          style={{ margin: '5px' }}
          type="warning"
          message={alertMessage}
        />
      )}
      <div className={styles.container} data-testid="customer-page">
        <Sidebar customerId={customerId} />
        <div className={styles.inner}>
          <HeaderBar
            customerId={customerId}
            actions={
              CUSTOMER_REPORT_ENABLED ? (
                <CustomerReportDrawerButton
                  disabled={!hasBenchmarkingPermission || !customer}
                />
              ) : (
                <Tooltip title={CUSTOMER_REPORT_DISABLED_MESSAGE}>
                  {/* Disabling the button stops the tooltip from working, */}
                  {/* and the tooltip is stopping the button from working, */}
                  {/* just antd things. */}
                  <CustomerReportDrawerButton />
                </Tooltip>
              )
            }
          />
          <LoadingContainer loading={isLoading}>
            <div className={styles.content}>
              {customer ? (
                <CustomerProvider pk={customer.pk}>
                  <CustomerDrawer />
                  <Outlet />
                </CustomerProvider>
              ) : (
                <div className={styles.empty}>
                  <Empty description="Search for a customer" />
                </div>
              )}
            </div>
          </LoadingContainer>
        </div>
      </div>
    </>
  )
}

export default CustomerLayout
