.container {
  --column-gap: var(--space-4);
  --row-gap: var(--space-4);

  display: contents;
}

.container :global(.ant-form-item) {
  margin-bottom: 0;
}

.container :global(.ant-form-item-control-input-content) > :only-child {
  width: 100%;
}

.selection {
  display: flex;
  column-gap: var(--column-gap);

  > * {
    flex: 1;
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(100px, 1fr));
  column-gap: var(--column-gap);
  row-gap: var(--row-gap);
}

.grid + .grid {
  margin-top: var(--row-gap);
}
