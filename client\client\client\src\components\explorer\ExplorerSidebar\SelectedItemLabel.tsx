import React from 'react'
import type { LayerStyles } from '../../../store/features/map/types'
import { integerToAlpha } from '../../../util/intgerToAlpha'
import { DEFAULT_STYLES } from '../../../util/layers'
import './SelectedItemLabel.css'

interface SelectedItemLabelProps {
  position: number
  styles?: LayerStyles
}

function SelectedItemLabel({ position, styles }: SelectedItemLabelProps) {
  const itemStyles = { ...DEFAULT_STYLES, ...styles }
  return (
    <div
      className="SelectedItemLabel"
      style={{ borderColor: itemStyles.color }}
    >
      {/* Using a separate element here so we don't have to muck around with converting the named CSS colors to Hex/RGB to use the alpha channel */}
      <div
        className="background"
        style={{
          backgroundColor: itemStyles.fillColor || itemStyles.color,
          opacity: itemStyles.fillOpacity,
        }}
      />
      {integerToAlpha(position)}
    </div>
  )
}

export default SelectedItemLabel
