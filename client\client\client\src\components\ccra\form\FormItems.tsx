import { QuestionCircleOutlined } from '@ant-design/icons'
import { Form, Tooltip } from 'antd'
import type { Rule } from 'antd/lib/form'

import styles from './FormItems.module.scss'
import classNames from 'classnames'
import type { CcraFormItem } from './types'

type FormItemsProps = {
  items: CcraFormItem[]
}

const HeadingTooltip = ({ text }: { text: string }) => {
  return (
    <Tooltip title={text}>
      <QuestionCircleOutlined
        style={{
          marginLeft: 5,
          fontSize: '0.7em',
          position: 'relative',
          top: '-0.20em',
        }}
      />
    </Tooltip>
  )
}

/**
 * @deprecated buggy rerendering. switch to regular form item definitions
 */

const FormItems = ({ items }: FormItemsProps) => {
  const form = Form.useFormInstance()
  const businessUnit = form.getFieldValue('businessUnit')
  return (
    <>
      {items.map((item: CcraFormItem, index: number) => {
        if (item.disabled) {
          return null
        }

        if (item.businessUnit && item.businessUnit !== businessUnit) {
          return null
        }

        if (item.customField) {
          return item.children
        }

        if (item.heading) {
          return (
            // biome-ignore lint/suspicious/noArrayIndexKey: Deprecated
            <h2 key={index}>
              {item.heading}
              {!!item.tooltip && <HeadingTooltip text={item.tooltip} />}
            </h2>
          )
        }

        if (item.subHeading) {
          return (
            // biome-ignore lint/suspicious/noArrayIndexKey: Deprecated
            <h3 key={index}>
              {item.subHeading}
              {!!item.tooltip && <HeadingTooltip text={item.tooltip} />}
            </h3>
          )
        }

        if (item.paragraph) {
          // biome-ignore lint/suspicious/noArrayIndexKey: Deprecated
          return <p key={index}>{item.paragraph}</p>
        }

        const isRequired = item.required !== undefined ? item.required : true
        let rules: Rule[] = isRequired
          ? [
              {
                required: true,
                message: item.customRuleMessage ?? 'This field is required',
              },
            ]
          : []

        if (item.customRules) {
          rules = rules.concat(item.customRules)
        }

        const checkboxConfirmRules: Rule[] = [
          {
            validator: async (_: Rule, value: boolean) =>
              value
                ? Promise.resolve()
                : Promise.reject(
                    new Error('You must confirm before preceding')
                  ),
          },
        ]

        return (
          <Form.Item
            // biome-ignore lint/suspicious/noArrayIndexKey: Deprecated
            key={index}
            name={item.name}
            label={item.label}
            tooltip={item.tooltip}
            rules={item.checkboxConfirm ? checkboxConfirmRules : rules}
            valuePropName={item.valuePropName}
            className={classNames(item.inlineStyle ? styles.halfWidth : '')}
            hidden={!!item.hidden}
          >
            {item.children}
          </Form.Item>
        )
      })}
    </>
  )
}

export default FormItems
