import { Form, InputNumber, type InputNumberProps } from 'antd'
import CcraBeefLambEmissionsData from './CustomFields/CcraBeefLambEmissionsData'
import CcraFonterraEmissionsData from './CustomFields/CcraFonterraEmissionsData'
import CcraFormItem from './CustomFields/CcraFormItem'
import { getFieldName, getUnitForField } from './helpers'
import type { FormItemOptions } from './types'
import styles from './FormItems.module.scss'
import TextArea from 'antd/lib/input/TextArea'
import { FormParagraph } from './CustomFields/CcraFormComponents'
import type { RuleObject } from 'antd/lib/form'
import type { StoreValue } from 'rc-field-form/lib/interface'

const emissionsScopeRules = [
  ({ getFieldValue }: { getFieldValue: (name: string[]) => StoreValue }) => ({
    validator(_: RuleObject, _value: StoreValue) {
      const scope1 = getFieldValue(getFieldName('emissionsScope1'))
      const scope2 = getFieldValue(getFieldName('emissionsScope2'))
      const scope3 = getFieldValue(getFieldName('emissionsScope3'))

      if (scope1 || scope2 || scope3) {
        return Promise.resolve()
      }

      return Promise.reject(
        new Error('At least one of the emissions scope fields must be filled')
      )
    },
  }),
]

const EmissionsScopeInput = ({
  fieldName,
  ...props
}: { fieldName: string } & InputNumberProps) => (
  <InputNumber
    controls={false}
    min={0}
    addonAfter={getUnitForField(fieldName)}
    precision={2}
    {...props}
  />
)

const EmissionsReportFields = () => {
  const form = Form.useFormInstance()
  const emissionsScope3Value = Form.useWatch(
    getFieldName('emissionsScope3'),
    form
  )
  const emissionsMeasurementMethodValue = Form.useWatch(
    getFieldName('emissionsMeasurementMethod'),
    form
  )
  const getScopeFieldProps = (
    fieldName: string,
    additionalProps: Partial<FormItemOptions> = {}
  ) => {
    return {
      fieldName,
      isRequired: false,
      customRules: emissionsScopeRules,
      ...additionalProps,
    }
  }

  switch (emissionsMeasurementMethodValue) {
    case 'Fonterra':
      return <CcraFonterraEmissionsData />
    case 'Beef + Lamb':
      return <CcraBeefLambEmissionsData />
    case 'Overseer':
      return (
        <>
          <CcraFormItem fieldName="methane">
            <InputNumber
              addonAfter={getUnitForField('methane')}
              controls={false}
              min={0}
            />
          </CcraFormItem>
          <CcraFormItem fieldName="nitrogen">
            <InputNumber
              addonAfter={getUnitForField('nitrogen')}
              controls={false}
              min={0}
            />
          </CcraFormItem>
          <CcraFormItem fieldName="carbon">
            <InputNumber
              addonAfter={getUnitForField('carbon')}
              controls={false}
              min={0}
            />
          </CcraFormItem>
        </>
      )
    case 'Synlait':
      return (
        <>
          <CcraFormItem fieldName="co2Mt">
            <InputNumber
              addonAfter={getUnitForField('co2Mt')}
              controls={false}
              min={0}
            />
          </CcraFormItem>
          <CcraFormItem fieldName="co2eKg" isRequired={false}>
            <InputNumber
              addonAfter={getUnitForField('co2eKg')}
              readOnly
              disabled
            />
          </CcraFormItem>
          <CcraFormItem fieldName="synlaitCo2eKgms">
            <InputNumber
              addonAfter={getUnitForField('synlaitCo2eKgms')}
              controls={false}
              min={0}
            />
          </CcraFormItem>
          <CcraFormItem fieldName="synlaitKgms" isRequired={false}>
            <InputNumber
              addonAfter={getUnitForField('synlaitKgms')}
              readOnly
              disabled
            />
          </CcraFormItem>
        </>
      )
    default:
      return (
        <>
          <FormParagraph text="Ensure at least one of the scope fields are populated" />
          <CcraFormItem {...getScopeFieldProps('emissionsScope1')}>
            <EmissionsScopeInput fieldName="emissionsScope1" />
          </CcraFormItem>
          <CcraFormItem {...getScopeFieldProps('emissionsScope2')}>
            <EmissionsScopeInput fieldName="emissionsScope2" />
          </CcraFormItem>
          <CcraFormItem
            {...getScopeFieldProps('emissionsScope3', {
              className: styles.halfWidth,
            })}
          >
            <EmissionsScopeInput fieldName="emissionsScope3" />
          </CcraFormItem>
          <CcraFormItem
            fieldName="emissionsScope3SupplyChainAspects"
            isRequired={!!emissionsScope3Value}
            disabled={!emissionsScope3Value}
            className={styles.halfWidth}
          >
            <TextArea disabled={!emissionsScope3Value} />
          </CcraFormItem>
        </>
      )
  }
}

export default EmissionsReportFields
