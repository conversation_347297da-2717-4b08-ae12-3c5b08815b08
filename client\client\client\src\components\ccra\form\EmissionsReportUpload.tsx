import { But<PERSON>, Card, Divider, Form, Popconfirm, message } from 'antd'
import React from 'react'
import {
  type CcraAttachment,
  useCcraCcraUploadFileCreateMutation,
  useCcraFilesListQuery,
  useCcraFilesDestroyMutation,
} from '@store/services/sdk/codegen'
import { DeleteOutlined, DownloadOutlined } from '@ant-design/icons'
import { toURL } from '@util'
import { createErrorMessage } from '@util/error'

const FileList = ({
  files,
  canEdit,
}: { files: CcraAttachment[]; canEdit: boolean }) => {
  const [deleteFile] = useCcraFilesDestroyMutation()

  const handleDelete = async (id: number) => {
    try {
      await deleteFile({ pk: id }).unwrap()
      void message.success('File deleted')
    } catch (error) {
      void message.error(error)
    }
  }

  if (!files || files.length === 0) {
    return null
  }

  return (
    <>
      <table>
        <thead>
          <tr>
            <th>File Name</th>
            <th>File Size</th>
            <th>Download</th>
            {canEdit && <th>Delete</th>}
          </tr>
        </thead>
        <tbody>
          {files?.map((file) => (
            <tr key={file.id}>
              <td style={{ paddingRight: 'var(--space-5)' }}>{file.name}</td>
              <td>{(file.size / 1000000).toFixed(1)} MB</td>
              <td>
                <a href={toURL(`/api/ccra/files/${file.id}/`)}>
                  <Button size="small" icon={<DownloadOutlined />} />
                </a>
              </td>
              <td>
                {canEdit && (
                  <Popconfirm
                    placement="topRight"
                    title={
                      'Are you sure you want to delete this file? This action cannot be undone.'
                    }
                    onConfirm={() => handleDelete(file.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button size="small" icon={<DeleteOutlined />} />
                  </Popconfirm>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </>
  )
}

const EmissionsReportUpload = ({
  ccraId,
  canEdit,
}: { ccraId: number; canEdit: boolean }) => {
  const [uploadForm] = Form.useForm()
  const [uploadFile, { isLoading }] = useCcraCcraUploadFileCreateMutation()
  const { data: files, refetch } = useCcraFilesListQuery({
    ccraId,
  })

  const handleFileUpload = async () => {
    try {
      const values = await uploadForm.validateFields()

      if (!values.file) {
        void message.error('Please select a file to upload')
        return
      }
      const upload = values.file
      const formData = new FormData()

      formData.append('file', upload as Blob)

      await uploadFile({
        pk: ccraId,
        // @ts-ignore - this functionality is getting removed next ticket. upload will be changed to upload as part of the regular form submit
        upload: formData,
      }).unwrap()
      refetch()
      uploadForm.resetFields()
      void message.success('File uploaded')
    } catch (error) {
      const errorMessage = createErrorMessage(error)
      message.error(errorMessage)
      console.error(error)
    }
  }
  return (
    <Card title="Emissions Report" style={{ margin: 0, marginTop: 16 }}>
      {canEdit && (
        <Form form={uploadForm} onFinish={handleFileUpload}>
          <Form.Item name={['file']} label="">
            <p>Upload the Customer Emissions Report</p>
            <input
              onChange={(e) =>
                uploadForm.setFieldsValue({
                  file: e?.target?.files?.[0] || null,
                })
              }
              type="file"
            />
          </Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            style={{ marginBottom: '10px' }}
          >
            Upload
          </Button>
          <Divider />
        </Form>
      )}

      <FileList files={files as CcraAttachment[]} canEdit={canEdit} />
    </Card>
  )
}

export default EmissionsReportUpload
