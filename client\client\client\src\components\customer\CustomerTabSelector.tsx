import { Divider, Radio } from 'antd'
import { useDispatch } from 'react-redux'
import { useParams } from 'react-router-dom'
import { useSelector } from '@store'
import { actions } from '@store/features/customer'
import { getCustomerPageTab } from '@store/features/customer/selectors'
import { useCustomerRetrieveQuery } from '@store/services/sdk'
import { uiSelectors } from '@store/ui'
import { skipArgObject } from '@util/helpers'
import { useEntitled } from '@components/EntitledComponent'

const CustomerTabSelector = () => {
  const { customerId } = useParams<{ customerId: string }>()
  const tab = useSelector((state) => getCustomerPageTab(state))

  const { data: customer } = useCustomerRetrieveQuery(
    skipArgObject({ pk: Number(customerId) })
  )
  const dispatch = useDispatch()
  const isValuer = useSelector(uiSelectors.isValuer)
  const hasCcraPreviewPermission = useEntitled([
    'client:customer_portal:ccra:view',
  ])
  const hasBenchmarkingPermission = useEntitled([
    'client:customer_portal:benchmarking:view',
  ])
  const hasFinancialsPermission = useEntitled([
    'client:customer_portal:financials:view',
  ])

  return (
    <Divider>
      <Radio.Group
        value={tab}
        disabled={!customer}
        onChange={(e) =>
          dispatch(actions.setCustomerTab({ tab: e.target.value }))
        }
      >
        <Radio.Button value="profile">Profile</Radio.Button>
        <Radio.Button value="balances">Cash Balances</Radio.Button>
        <Radio.Button value="funding">Facilities</Radio.Button>
        <Radio.Button value="financials" disabled={!hasFinancialsPermission}>
          Financials
        </Radio.Button>
        <Radio.Button
          value="benchmarking"
          disabled={!hasBenchmarkingPermission}
        >
          Benchmarking
        </Radio.Button>
        <Radio.Button
          disabled={!customer || customer.segment === 'W' || !isValuer}
          value="emissions"
        >
          Emissions
        </Radio.Button>
        {hasCcraPreviewPermission && (
          <Radio.Button value="ccra">ESG Data</Radio.Button>
        )}
      </Radio.Group>
    </Divider>
  )
}

export default CustomerTabSelector
