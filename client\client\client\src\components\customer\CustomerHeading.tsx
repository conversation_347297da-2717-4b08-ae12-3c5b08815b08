import { pick } from 'lodash'
import {
  useCustomerRetrieveQuery,
  useTradingGroupCustomersListQuery,
} from '@store/services/sdk'
import DelayedTooltip from '@components/DelayedTooltip'
import { skipArgObject } from '@util/helpers'
import CustomerCategories from './CustomerCategories'
import CustomerSelect from './CustomerSelect'
import styles from './CustomerHeading.module.css'

type Props = {
  customerId: number
}

export const CustomerHeading = ({ customerId }: Props) => {
  const { data: customer } = useCustomerRetrieveQuery(
    skipArgObject({ pk: customerId })
  )

  const group = customer?.tradingGroup

  const { data: customers } = useTradingGroupCustomersListQuery(
    skipArgObject({ pk: group?.tradingGroupId })
  )

  if (customer)
    return (
      <div className={styles.container}>
        {group != null && (
          <DelayedTooltip title="Trading Group">
            <span className={styles.group}>{group.tradingGroupName}</span>
          </DelayedTooltip>
        )}
        <CustomerSelect
          customers={group ? customers : [customer]}
          selectedCustomerId={customerId}
          className={styles.select}
        />
        <div className={styles.categories}>
          <CustomerCategories
            {...pick(customer, [
              'customerNumber',
              'anzsic',
              'customerSetCode',
              'businessUnit',
            ])}
          />
        </div>
      </div>
    )

  return null
}

export default CustomerHeading
