import { uniqBy } from 'lodash'
import { useMemo } from 'react'
import type { FieldNode } from '@store/services/fields/codegen'

export default function useFieldMessages(fields: FieldNode[] | undefined = []) {
  const messages = useMemo(
    () =>
      uniqBy(
        fields
          .map((descendant) => ({
            id: descendant.id,
            message: descendant.metadata?.message,
          }))
          .filter((obj) => !!obj.message),
        'message'
      ),
    [fields]
  )

  return messages
}
