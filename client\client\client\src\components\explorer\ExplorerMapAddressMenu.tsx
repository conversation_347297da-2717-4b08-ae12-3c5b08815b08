import { CloseOutlined } from '@ant-design/icons'
import { Menu, type MenuProps } from 'antd'
import { useGetAddressQuery } from '@store/services/address'
import {
  close,
  createListingFromAddress,
  createSaleFromAddress,
  createValuationFromAddress,
  linkAddressToTradingGroup,
  ownerSearch,
  toggleSelectedFeature,
  viewRecordInDashboard,
  zoomToProperty,
} from './menuItems'
import useSelectedItem from './useSelectedItem'

interface Props extends MenuProps {
  id: string
}

const ExplorerMapAddressMenu = ({ id, ...props }: Props) => {
  const { data, isLoading } = useGetAddressQuery(id)
  const { selected, toggle } = useSelectedItem({ id, type: 'address' })

  const items = [
    toggleSelectedFeature(selected, toggle, 'Property'),
    // viewTradingGroup(data?.tradingGroupId, isLoading),
    viewRecordInDashboard(id, 'address'),
    ownerSearch(data?.owners, isLoading),
    zoomToProperty(data && { lat: data?.lat, lng: data?.lng }, isLoading),
    createSaleFromAddress(id),
    createListingFromAddress(id),
    createValuationFromAddress(id),
    linkAddressToTradingGroup(id),
    close,
  ]

  return <Menu items={items} {...props} />
}

export default ExplorerMapAddressMenu
