import { EditOutlined } from '@ant-design/icons'
import type { AgriUplift } from '@store/services/finance/codegen'
import { Tag } from 'antd'
import { capitalize } from 'lodash'

const StatusTag = ({ status }: { status: AgriUplift['status'] }) => {
  return (
    <Tag
      style={{
        marginRight: 0,
      }}
      color={status === 'draft' ? 'processing' : 'success'}
      icon={<EditOutlined />}
      data-testid="draft-icon"
    >
      {capitalize(status)}
    </Tag>
  )
}

export default StatusTag
