import { Skeleton } from 'antd'
import { LatLng, type LatLngBounds, type LatLngBoundsExpression } from 'leaflet'
import React, { type ReactNode, useEffect, useState } from 'react'
import {
  FeatureGroup,
  Map as LeafletMap,
  ScaleControl,
  TileLayer,
} from 'react-leaflet'

export interface ExportMapProps {
  center: { lat: number; lng: number }
  children?: ReactNode | ReactNode[]
  type: 'road' | 'terrain' | 'contour'
  isFetching?: boolean
  size?: 'full' | 'half' | 'default'
  maxZoom?: number
}

/**
 * @deprecated Bad, needs to be rewritten
 */
const ExportMap = (props: ExportMapProps) => {
  const { center, children } = props

  // biome-ignore lint: Will be removed
  const [layerRef, setLayerRef] = useState<any>()
  const [mapRef, setMapRef] = useState<LeafletMap | undefined | null>()

  // biome-ignore lint/correctness/useExhaustiveDependencies: without props?.children the maps don't load correctly, don't have time to diagnose properly.
  useEffect(() => {
    let newBounds: LatLngBounds | undefined =
      layerRef?.leafletElement?.getBounds()
    if (!newBounds?.isValid()) {
      newBounds = undefined
    } else if (newBounds) {
      mapRef?.leafletElement.fitBounds(newBounds as LatLngBoundsExpression)
    }
  }, [layerRef, props?.children, mapRef?.leafletElement])

  useEffect(() => {
    if (mapRef) {
      mapRef?.leafletElement.dragging.disable()
      mapRef?.leafletElement.touchZoom.disable()
      mapRef?.leafletElement.doubleClickZoom.disable()
      mapRef?.leafletElement.scrollWheelZoom.disable()
      mapRef?.leafletElement.boxZoom.disable()
      mapRef?.leafletElement.keyboard.disable()
    }
  }, [mapRef])

  if (props?.isFetching) {
    return <Skeleton />
  }

  return (
    <div className={`ExportMap ${props?.size ?? 'default'}`}>
      <LeafletMap
        maxZoom={props.maxZoom ?? 17}
        zoomSnap={1}
        center={new LatLng(center.lat, center.lng)}
        style={{ height: '100%', width: '100%' }}
        ref={setMapRef}
      >
        <ScaleControl position="bottomleft" />
        {props?.type === 'terrain' ? (
          <TileLayer
            attribution="Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community"
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
          />
        ) : (
          <></>
        )}
        {props?.type === 'road' ? (
          <TileLayer
            attribution='&amp;copy <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
        ) : (
          <></>
        )}
        {props?.type === 'contour' ? (
          <TileLayer
            attribution='Powered by <a href="https://linz.govt.nz">LINZ'
            url="https://tiles-cdn.koordinates.com/services;key=9f7f7e0f623544afae68bfd23d69e52a/tiles/v4/layer=52343/EPSG:3857/{z}/{x}/{y}.png"
          />
        ) : (
          <></>
        )}
        <FeatureGroup ref={setLayerRef}>{children}</FeatureGroup>
      </LeafletMap>
    </div>
  )
}

export default ExportMap
