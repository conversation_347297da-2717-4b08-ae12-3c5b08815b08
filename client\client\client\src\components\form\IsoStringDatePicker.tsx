import { DatePicker, type DatePickerProps } from 'antd'
import { useCallback, useMemo } from 'react'
import moment, { type Moment } from 'moment'

type Props = Omit<DatePickerProps, 'onChange' | 'value'> & {
  value?: string | null
  outputFormat?: string
  onChange?: (date: string | null) => void
  withoutUTCConversion?: boolean
}

const IsoStringDatePicker = ({
  value,
  onChange,
  outputFormat = 'YYYY-MM-DDTHH:mm:ss.SSS[Z]',
  withoutUTCConversion = false,
  ...props
}: Props) => {
  const pickerValue = useMemo(() => {
    if (value == null || moment.isMoment(value)) return value
    const newValue = moment(value)
    if (!moment.isMoment(newValue)) return null
    return newValue
  }, [value])

  const handleChange = useCallback(
    (date: Moment | null) => {
      let newValue: string | null = null
      if (moment.isMoment(date)) {
        let mmnt = date
        if (!withoutUTCConversion) {
          mmnt = date.utc()
        }
        newValue = mmnt.format(outputFormat)
      }
      onChange?.(newValue)
    },
    [withoutUTCConversion, onChange, outputFormat]
  )

  return (
    <DatePicker
      value={pickerValue}
      onChange={handleChange}
      format={(value) => value.format('DD-MM-YYYY')}
      {...props}
    />
  )
}

export default IsoStringDatePicker
