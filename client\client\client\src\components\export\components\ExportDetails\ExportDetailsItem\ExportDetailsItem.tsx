import type React from 'react'
import { useMemo } from 'react'

interface ExportDetailsItemProps {
  label?: string
  children?: React.ReactNode
  type?:
    | 'total'
    | 'subtotal'
    | 'information'
    | 'placeholder'
    | 'number'
    | 'date'
  hidden?: boolean
  variant?: 'warning'
}

const ExportDetailsItem = (props: ExportDetailsItemProps) => {
  const formatterFunction = useMemo(() => {
    if (
      props?.type === 'total' ||
      props?.type === 'subtotal' ||
      props?.type === 'number'
    ) {
      return (value: unknown) =>
        ((value ?? 0) as number).toLocaleString('en-NZ', {
          maximumFractionDigits: 2,
        })
    }
    if (props?.type === 'date') {
      return (value: unknown) => (value ?? '')?.toString()?.slice(0, 10)
    }
    return (value: unknown) => value
  }, [props?.type])

  if (props?.hidden) {
    return <></>
  }

  return (
    <div
      className="ExportDetailsItem"
      data-type={props?.type ? props?.type : 'information'}
      data-variant={props?.variant ? props?.variant : 'default'}
    >
      <div className="export-details-item-label">{props.label}</div>
      <div className="export-details-item-value">
        {formatterFunction(props.children)}
      </div>
    </div>
  )
}

export default ExportDetailsItem
