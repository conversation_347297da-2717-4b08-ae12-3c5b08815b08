import { Bar } from '@ant-design/charts'
import React, { useMemo } from 'react'
import type { AnzUnionFeatureCollection } from '../../../models/gis/AnzUnionFeatureCollection'
import { getLegend } from '../../../types/GISLayerDescriptors'
import { GeoJSON } from '../../leaflet/GeoJSON'
import {
  ExportAttributions,
  ExportAttributionsItem,
} from '../components/ExportAttributions'
import { ExportGraphContainer } from '../components/ExportGraphContainer'
import { ExportLegend, ExportLegendItem } from '../components/ExportLegend'
import { ExportMap } from '../components/ExportMap'
import { ExportParagraph } from '../components/ExportParagraph'
import { ExportSection } from '../components/ExportSection'

export interface ExportLUCSectionProps {
  luc: { [key: string]: number } | undefined
  center: { lat: number; lng: number }
  anzUnion: AnzUnionFeatureCollection | undefined
  excluded?: Array<number>
}

export const ExportLUCSection = (props: ExportLUCSectionProps) => {
  const { luc = {}, center, anzUnion, excluded } = props

  const chartData = useMemo(() => {
    return Object.keys(luc).map((k) => {
      return { label: k, value: luc[k] }
    })
  }, [luc])

  const legend = getLegend('luc')

  if (!legend) {
    return null
  }

  return (
    <React.Fragment>
      <ExportSection>
        <ExportParagraph>
          The land use capability system as used in New Zealand has eight LUC
          classes. Classes 1 to 4 are classified as potentially arable land,
          while LUC classes 5 to 8 are potentially non-arable.
        </ExportParagraph>
        <ExportParagraph>
          It can tell us more about the land and provides a simple assessment
          regarding the potential land suitability for sustainable use.
        </ExportParagraph>
        <div className="export-section-inner flex">
          <ExportGraphContainer>
            <Bar
              color={({ label }) => (label ? legend.getColor(label) : '#ccc')}
              data={chartData}
              xField="value"
              yField="label"
            />
          </ExportGraphContainer>
          <ExportLegend direction="vertical">
            {Object.keys(legend.legendEntries ?? {})?.map((k) => {
              const entry = legend.legendEntries[k]
              if (
                // Non relevant values such as T, E, etc, are labelled
                // generically as 'Undefined'. There may be instances where
                // the value itself is also undefined.
                !Object.keys(luc).includes(k) ||
                entry?.label === 'Undefined' ||
                entry?.label === undefined
              ) {
                return null
              }
              return (
                <ExportLegendItem
                  key={k}
                  fillColor={entry?.color}
                  borderColor={entry?.color}
                  label={entry?.label ?? ''}
                />
              )
            })}
          </ExportLegend>
        </div>
      </ExportSection>
      <ExportSection title="Land Use Classification Visualised">
        <ExportMap type="road" center={center} size="half">
          {anzUnion?.features?.map((feature, index) => {
            const color = legend.getColor(feature?.properties?.luc)
            return (
              <GeoJSON
                key={`luc-${index}-${feature?.id}-${color}-${excluded?.join(
                  '-'
                )}`}
                data={feature}
                style={{
                  color: color,
                  weight: 2,
                  fillOpacity: 0.75,
                }}
              />
            )
          })}
        </ExportMap>
      </ExportSection>
      <ExportAttributions>
        <ExportAttributionsItem type="lrisLuc" />
      </ExportAttributions>
    </React.Fragment>
  )
}
