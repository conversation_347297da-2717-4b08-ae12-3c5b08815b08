import { useCcraFormOptionsListQuery } from '@store/services/sdk'

const MultiOptionDisplay = ({
  selectedOptionIds,
  option,
}: { selectedOptionIds: number[]; option: string }) => {
  const { data } = useCcraFormOptionsListQuery({
    optionType: option,
  })

  const selectedOptions = data?.filter((item) =>
    selectedOptionIds?.includes(item.id)
  )

  return (
    <ul>
      {selectedOptions?.map((item) => (
        <li key={item.id}>- {item.value}</li>
      ))}
    </ul>
  )
}

export default MultiOptionDisplay
