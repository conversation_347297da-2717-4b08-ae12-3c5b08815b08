import { PlusOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Card, List } from 'antd'
import { useCallback } from 'react'
import { useSelector } from '@store'
import { getCustomerReportStateById } from '@store/features/customer'
import {
  useCustomerReportTemplateCreateMutation,
  useCustomerReportTemplateListQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import CustomerReportTemplatesItem from './CustomerReportTemplatesItem'
import useRouteCustomer, { useRouteCustomerId } from './useRouteCustomer'

export type CustomerReportTemplateData = {
  denominator: string
  selectedComponents: string[][]
  selectedMeasures: string[][]
  reportTitle: string
}

const CustomerReportTemplatesCard = ({ ...props }) => {
  const customerId = useRouteCustomerId()
  const { data: customer } = useRouteCustomer()

  const sessionReportSettings = useSelector((state) =>
    getCustomerReportStateById(state, customerId)
  )

  const { data: reportTemplates, isLoading } =
    useCustomerReportTemplateListQuery(
      skipArgObject({ segment: customer?.segment })
    )

  const [createReportTemplate, { isLoading: createLoading }] =
    useCustomerReportTemplateCreateMutation()

  const handleCreate = useCallback(() => {
    void createReportTemplate({
      customerReportTemplate: {
        data: sessionReportSettings,
        segment: customer?.segment ?? 'R',
        name: new Date().toISOString(),
      },
    })
  }, [createReportTemplate, customer?.segment, sessionReportSettings])

  return (
    <Card
      loading={isLoading}
      title={'Report Templates'}
      {...props}
      extra={
        <Button
          loading={createLoading}
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          Template
        </Button>
      }
    >
      <List
        size="small"
        rowKey={(record) => Number(record.id)}
        loading={isLoading}
        dataSource={reportTemplates}
        bordered={true}
        renderItem={(record) => <CustomerReportTemplatesItem record={record} />}
      />
    </Card>
  )
}

export default CustomerReportTemplatesCard
