import { Button, Collapse } from 'antd'
import React, { memo } from 'react'
import { useLocalStorage } from 'react-use'
import type {
  AgriUplift,
  AgriUpliftEmissionsSource,
} from '@store/services/finance/codegen'
import { equalsProperty, not } from '@util/helpers'
import Divider from './Divider'
import styles from './EmissionsSources.module.css'
import EmissionsSourceTitle from './EmissionsSourceTitle'
import EmissionsSource from './EmissionsSource'
import { INVALID_SOURCE_NAME } from '../const'

type EmissionsSourcesValue =
  | (Partial<AgriUpliftEmissionsSource> &
      Required<Pick<AgriUpliftEmissionsSource, 'pk'>>)[]
  | undefined

type Props = {
  tradingGroupId: AgriUplift['tradingGroupId']
  value?: EmissionsSourcesValue
  onChange?: (newValue: EmissionsSourcesValue) => void
  errorIndexes?: number[]
}

export const createEmissionsSource = () => ({ pk: Date.now() })

const EmissionsSources = ({
  tradingGroupId,
  value = [],
  onChange,
  errorIndexes = [],
}: Props) => {
  const [activeKeys, setActiveKeys] = useLocalStorage<Record<string, boolean>>(
    `agri-uplift-${tradingGroupId}-emission-sources`,
    {}
  )

  // Show new entries as active
  const activeKey = value
    .map((source) => (source.pk as number).toString())
    .filter((key) => {
      const keyMap = activeKeys || {}
      if (!(key in keyMap)) return true
      return keyMap[key]
    })

  return (
    <>
      <Divider style={{ marginTop: 'var(--space-1)', marginBottom: 0 }} />
      <Collapse
        ghost
        activeKey={activeKey}
        onChange={(keys) => {
          if (!Array.isArray(keys)) return
          // Clean up old entries
          const newKeyMap = Object.fromEntries(
            value.map((source) => {
              const key: string = (source.pk as number).toString()
              const entry: [string, boolean] = [key, keys.includes(key)]
              return entry
            })
          )
          setActiveKeys(newKeyMap)
        }}
      >
        {value.map((source, i) => {
          const path = ['emissionsSources', i]

          if (source.name === INVALID_SOURCE_NAME) {
            return (
              <EmissionsSource
                key={source.name}
                path={path}
                facilities={source.facilities}
                invalid
              />
            )
          }

          return (
            <Collapse.Panel
              key={source.pk || 'only'}
              header={
                <EmissionsSourceTitle
                  source={source}
                  style={{
                    ...(errorIndexes.includes(i) && {
                      color: 'var(--color-error)',
                    }),
                  }}
                />
              }
              extra={
                <Button
                  size="small"
                  type="link"
                  onClick={() =>
                    onChange?.(
                      value?.filter(not(equalsProperty('pk', source.pk)))
                    )
                  }
                >
                  Remove
                </Button>
              }
              className={styles.panel}
            >
              <EmissionsSource
                path={path}
                facilities={source.facilities}
                // Dirty and hopefully temporary fix:
                // There are some resolvable issues with the metric type orderings,
                // this is set to prevent the user from changing the report type,
                // which is what causes the bug;
                // (items neeed to be in order, but if it's saved with an overlapping metric type,
                // the updated order is incorrectly [...desiredOrder, ...overlappingItems])
                lockSource={!!source.key}
              />
            </Collapse.Panel>
          )
        })}
      </Collapse>
      <Button
        onClick={() => onChange?.([...value, createEmissionsSource()])}
        style={{ marginTop: 'var(--space-3)' }}
      >
        Add
      </Button>
    </>
  )
}

export default memo(EmissionsSources)
