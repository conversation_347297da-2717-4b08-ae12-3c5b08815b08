import { Card, Select, Table } from 'antd'
import { useMemo } from 'react'
import { REGION_OPTIONS } from '@components/sales/generic/SaleForm'
import {
  useAdminTerritorialAuthorityAssignmentCreateMutation,
  useAdminTerritorialAuthorityAssignmentDestroyMutation,
  useAdminTerritorialAuthorityAssignmentListQuery,
  useAdminTerritorialAuthorityAssignmentUpdateMutation,
} from '@store/services/sdk'
import { useGetValuersQuery } from '@store/services/valuations'
import styles from './SalesTerritoryAssignmentPanel.module.scss'

const columns = [
  {
    title: 'TLA',
    dataIndex: 'tla',
    key: 'tla',
  },
  {
    title: 'Assigned Valuers',
    dataIndex: 'assignedValuers',
    render: (_: unknown, { tla }: { tla: string }) => (
      <AssignedValuerList tla={tla} />
    ),
  },
]

const regionOptions = REGION_OPTIONS.map(({ value }) => ({
  key: value,
  tla: value,
}))

const SalesTerritoryAssignmentPanel = () => {
  return (
    <Card title="Sale Territory Assignment" style={{ maxWidth: '800px' }}>
      <Table
        dataSource={regionOptions}
        pagination={false}
        size="small"
        columns={columns}
      />
    </Card>
  )
}

const AssignedValuerList = ({ tla }: { tla: string }) => {
  const { data: territorialAssignments } =
    useAdminTerritorialAuthorityAssignmentListQuery(undefined, {
      selectFromResult: ({ data }) => ({
        data: Object.fromEntries(
          data
            ?.filter((territory) => territory.territorialAuthority === tla)
            ?.map((assignment) => [assignment.valuer, assignment]) ?? []
        ),
      }),
    })
  const { data: valuers } = useGetValuersQuery(undefined)

  const [createTerritorialAuthorityAssignment] =
    useAdminTerritorialAuthorityAssignmentCreateMutation()
  const [deleteTerritorialAuthorityAssignment] =
    useAdminTerritorialAuthorityAssignmentDestroyMutation()

  const options = useMemo(() => {
    return (
      valuers?.map((valuer) => ({
        label: valuer.username,
        value: valuer.id.toString(),
      })) ?? []
    )
  }, [valuers])

  const selectedOptions = useMemo(
    () => Object.keys(territorialAssignments ?? {}),
    [territorialAssignments]
  )

  const handleChange = async (values: string[]) => {
    const seen: Record<string, boolean> = {}
    for (const value of values) {
      if (!territorialAssignments[value]) {
        await createTerritorialAuthorityAssignment({
          adminTerroritorialAuthorityAssignment: {
            valuer: +value,
            territorialAuthority: tla,
          },
        })
      } else {
        seen[value] = true
      }
    }
    for (const key of selectedOptions) {
      const assignmentId = territorialAssignments[key].id
      if (!seen[key] && assignmentId) {
        await deleteTerritorialAuthorityAssignment({ pk: assignmentId })
      }
    }
  }

  return (
    <div className={styles.AssignedValuerList}>
      <Select
        mode="multiple"
        value={selectedOptions}
        options={options}
        onChange={handleChange}
      />
    </div>
  )
}

export default SalesTerritoryAssignmentPanel
