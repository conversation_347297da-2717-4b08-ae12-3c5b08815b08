import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons'
import { Button, type ButtonProps, Tooltip } from 'antd'
import { startCase } from 'lodash'
import { useCallback, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  type ExplorerSelectableLayer,
  actions,
  getShowSelectedTooltip,
} from '@store/features/explorer'

interface Props extends Omit<ButtonProps, 'onClick'> {
  layer: ExplorerSelectableLayer
}

const ToggleSelectedTooltip = ({ children, layer, ...props }: Props) => {
  const dispatch = useDispatch()

  const active = useSelector(getShowSelectedTooltip(layer))

  const tooltipText = useMemo(() => {
    return `${active ? 'Hide' : 'Show'} selected ${startCase(layer)} identifier`
  }, [active, layer])

  const handleClick = useCallback(() => {
    dispatch(actions.toggleSelectedTooltip(layer))
  }, [dispatch, layer])

  return (
    <Tooltip title={tooltipText}>
      <Button
        size="small"
        onClick={handleClick}
        {...props}
        icon={active ? <EyeOutlined /> : <EyeInvisibleOutlined />}
      >
        {children}
      </Button>
    </Tooltip>
  )
}

export default ToggleSelectedTooltip
