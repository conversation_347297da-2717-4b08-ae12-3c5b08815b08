import { StyleSheet, Text, View } from '@react-pdf/renderer'
import { chunk } from 'lodash'
import React from 'react'
import { styles } from '@components/pdf'
import { PDFMapImage } from '@components/pdf'
import PDFInlineTable from '@components/pdf/PDFInlineTable'
import type { AddressLine } from '@store/services/sdk'

// TODO: Type the codegen to match these fields exactly
const INTERNAL_FIELDS = ['mortgagee', 'owners', 'previous_owners']
const LONG_FIELDS = ['owners', 'land_use', 'title_nos']

type Fields = AddressLine['fields']
type SortedFields = [Fields, Fields]

type Props = {
  address: AddressLine
  last?: boolean
}

const notInternalField = (field: Fields[number]) =>
  !INTERNAL_FIELDS.includes(field.id)

function sortByValueLength(sorted: SortedFields, field: Fields[number]) {
  const sortIndex = LONG_FIELDS.includes(field.id) ? 1 : 0
  sorted[sortIndex].push(field)
  return sorted
}

const localStyles = StyleSheet.create({
  line: {
    display: 'flex',
    flexDirection: 'row',
    paddingBottom: 15,
  },
  lineSeparator: {
    borderBottom: '0.25 solid black',
    marginBottom: 30,
  },
})

const AddressLinePDFItem = ({ address, last = true }: Props) => {
  const [shortFields, longFields] = address.fields
    .filter(notInternalField)
    .reduce(sortByValueLength, [[], []])
  const columns = chunk(shortFields, Math.ceil(shortFields.length / 2))

  // biome-ignore lint: Type has trivial mismatch on FeatureCollection type
  const titles = address.titles as any

  return (
    <View key={address.id} wrap={false}>
      <View
        style={{
          ...localStyles.line,
          ...(!last && localStyles.lineSeparator),
        }}
      >
        <View style={styles.grow}>
          <Text style={styles.title}>{address.fullAddress}</Text>
          <View style={styles.row}>
            {columns.map((column, i) => (
              <View
                key={`address-line-pdf-column-${address.id}-${i}`}
                style={styles.grow}
              >
                <PDFInlineTable data={column} />
              </View>
            ))}
          </View>
          <View>
            <PDFInlineTable data={longFields} weighting={[1, 4]} />
          </View>
        </View>
        <View
          style={{
            width: '25%',
            marginTop: 9,
            marginLeft: 15,
            alignSelf: 'flex-start',
            border: '1 solid black',
          }}
        >
          <View>
            <PDFMapImage feature={titles} />
          </View>
        </View>
      </View>
    </View>
  )
}

export default AddressLinePDFItem
