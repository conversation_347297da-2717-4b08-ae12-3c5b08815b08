import { debounce } from 'lodash'
import { type ChangeEvent, memo, useCallback, useMemo, useState } from 'react'
import type { FieldNode } from '@store/services/fields/codegen'
import FieldContainer from './FieldContainer'
import type { FieldValue } from './util'

function TextArea({
  field,
  value: initialValue,
  onChange = () => undefined,
}: {
  field: FieldNode
  value?: FieldValue
  onChange?: (newValue: FieldValue | undefined) => void
}) {
  const [value, setValue] = useState(initialValue)

  const id = initialValue?.id || Date.now()

  const debouncedOnChange = useMemo(() => debounce(onChange, 400), [onChange])

  const handleChange = useCallback(
    (e: ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = {
        id,
        fieldNodeId: field.id,
        fieldKey: field.fieldKey,
        value: e.target.value,
      }
      setValue(newValue)
      debouncedOnChange(newValue)
    },
    [debouncedOnChange, field.id, field.fieldKey, id]
  )

  return (
    <FieldContainer
      tagName="label"
      title={field.description}
      message={field.metadata?.message}
      guidance={field.metadata?.guidance}
    >
      <textarea value={value?.value} onChange={handleChange} />
    </FieldContainer>
  )
}

export default memo(TextArea)
