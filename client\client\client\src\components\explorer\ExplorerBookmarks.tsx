import { List } from 'antd'
import { debounce } from 'lodash'
import type React from 'react'
import { useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { updateBookmark } from '@store/ui/actions'
import type { SelectionBookmark } from '@store/ui/types'
import { useBookmarks } from '@util/useBookmarks'
import styles from './ExplorerBookmarks.module.scss'

const ExplorerBookmarks = () => {
  const { bookmarks } = useBookmarks()

  const dispatch = useDispatch()

  const handleNameChange = debounce(
    useCallback(
      (name, bookmarkToUpdate) => {
        const bookmark = { ...bookmarkToUpdate, name }
        dispatch(updateBookmark({ bookmark }))
      },
      [dispatch]
    ),
    800
  )

  function onInput(
    e: React.FormEvent<HTMLSpanElement>,
    bookmark: SelectionBookmark
  ) {
    const target = e.target as HTMLSpanElement
    handleNameChange(target.textContent, bookmark)
  }

  return (
    <>
      {bookmarks?.length ? (
        <List size="small">
          {bookmarks.map((bookmark) => (
            <List.Item key={bookmark.id} className={styles.bookmark}>
              <span
                contentEditable
                suppressContentEditableWarning
                onInput={(e) => onInput(e, bookmark)}
              >
                {bookmark.name}
              </span>
            </List.Item>
          ))}
        </List>
      ) : (
        <div>No bookmarks yet. Save your selected items to a new bookmark.</div>
      )}
    </>
  )
}

export default ExplorerBookmarks
