import React from 'react'
import FormattedDateRangePicker, {
  type FormattedDateRangePickerProps,
} from './FormattedDateRangePicker'
import type { DateInputRangeValue } from './helpers'

type DateRangeObject = { lower: string; upper: string }

type Props = Omit<FormattedDateRangePickerProps, 'value' | 'onChange'> & {
  value?: DateRangeObject | null | undefined
  onChange?: (value: string | null | undefined) => void
  // onChange?: (value: DateRangeObject | null | undefined) => void
}

const DateRangeObjectPicker = ({ value, onChange }: Props) => {
  const parsedValue = typeof value === 'string' ? JSON.parse(value) : value
  const dateTuple = parsedValue
    ? ([parsedValue.lower, parsedValue.upper] as DateInputRangeValue)
    : undefined

  const handleChange = (range: DateInputRangeValue) => {
    let newRange = undefined
    if (range != null) {
      newRange = JSON.stringify({
        lower: range[0] as string,
        upper: range[1] as string,
      })
    } else {
      newRange = range
    }
    onChange?.(newRange)
  }

  return <FormattedDateRangePicker value={dateTuple} onChange={handleChange} />
}

export default DateRangeObjectPicker
