import { Form, type FormItemProps } from 'antd'
import type { NamePath, RuleObject } from 'rc-field-form/es/interface'
import React, { useMemo, useState } from 'react'
import {
  type CustomerEmissionsAttachmentType,
  type CustomerEmissionsReportAttachment,
  useFinanceAgriUpliftProductRetrieveQuery,
} from '@store/services/finance/codegen'
import { concatPaths, equalsProperty } from '@util/helpers'
import { AttachmentTypeInput } from './AttachmentInput'
import { getResourcePath } from './helpers'

type Props = {
  path?: NamePath
  reportTypeId?: number
}

const AttachmentType = ({
  attachmentType,
  reportTypeId,
  required,
  ...props
}: FormItemProps & {
  reportTypeId: number
  attachmentType: CustomerEmissionsAttachmentType
}) => {
  const [error, setError] = useState('')

  return (
    <Form.Item
      key={`${reportTypeId}-${attachmentType.pk}`}
      label={attachmentType.name}
      rules={
        required
          ? [
              {
                required: true,
                message: `${attachmentType.name} is required`,
                validator: (
                  _: RuleObject,
                  value: CustomerEmissionsReportAttachment[]
                ) => {
                  // Filthy hack because of the jank way this is structured;
                  // because they share the same path, when validation is done on submit,
                  // the validation for all items is given the error of the last offender.
                  if (!value?.length) {
                    setError(`${attachmentType.name} is required`)
                    return Promise.reject()
                  }
                  setError('')
                  return Promise.resolve()
                },
                transform: (value: CustomerEmissionsReportAttachment[]) =>
                  value
                    ?.filter(equalsProperty('reportTypeId', reportTypeId))
                    .filter(
                      equalsProperty('attachmentTypeId', attachmentType.pk)
                    ),
                type: 'array',
              },
            ]
          : undefined
      }
      help={error}
      validateStatus={error ? 'error' : undefined}
      {...props}
    >
      <AttachmentTypeInput
        reportTypeId={reportTypeId}
        attachmentTypeId={attachmentType.pk}
      />
    </Form.Item>
  )
}

const EmissionsAttachments = ({ path = [], reportTypeId }: Props) => {
  const getFieldPath = useMemo(() => getResourcePath(path), [path])

  const { data: product = { reportTypes: [] } } =
    useFinanceAgriUpliftProductRetrieveQuery()

  const reportType = product?.reportTypes?.find(({ pk }) => pk === reportTypeId)

  if (!reportTypeId) return null

  return (
    <>
      {reportType?.attachmentTypes.map((attachmentType) => (
        <AttachmentType
          key={`${reportTypeId}-${attachmentType.pk}`}
          reportTypeId={reportTypeId}
          attachmentType={attachmentType}
          name={concatPaths(getFieldPath('attachments'))}
          required={attachmentType.required}
          // can probably transform the value here instead of passing attachmentType down
        />
      ))}
    </>
  )
}

export default EmissionsAttachments
