import { useCcraFormOptionsListQuery } from '@store/services/sdk'
import { Checkbox } from 'antd'
import styles from './CheckboxGroup.module.css'
import type { CheckboxGroupProps } from 'antd/es/checkbox/Group'

const ClimateRiskManagement = (checkboxGroupProps: CheckboxGroupProps) => {
  const { data } = useCcraFormOptionsListQuery({
    optionType: 'climateRiskManagement',
  })

  const options = data?.map((item) => {
    return {
      label: item.value,
      value: item.id,
    }
  })

  return (
    <div className={styles.checkboxGroupWrapper}>
      <Checkbox.Group {...checkboxGroupProps} options={options} />
    </div>
  )
}

export default ClimateRiskManagement
