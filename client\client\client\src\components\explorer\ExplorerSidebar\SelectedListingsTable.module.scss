.container {
  flex: 1;

  :global(.ant-table-expanded-row-fixed) {
    overflow: visible !important;
  }
}

.overflow {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stickyRow {
  &,
  :global(.ant-table-cell-fix-left),
  :global(.ant-table-cell-fix-right) {
    position: sticky;
    top: 0;
    z-index: 10;
    background: white;
  }
}

.viewportRow {
  &,
  :global(.ant-table-cell-fix-left),
  :global(.ant-table-cell-fix-right) {
    background-color: #f0faff;
  }
}
