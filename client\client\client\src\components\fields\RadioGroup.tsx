import classNames from 'classnames'
import React, { forwardRef, memo, useState } from 'react'
import Markdown from 'react-markdown'
import type { FieldNode } from '@store/services/esst/codegen'
import FieldContainer from './FieldContainer'
import type { FieldValue } from './util'
import styles from './RadioGroup.module.css'

export type RadioGroupProps = {
  field: FieldNode
  value?: FieldValue
  onChange?: (value: FieldValue | undefined) => void
  children?: React.ReactNode
  column?: boolean
}

export function RadioContainer({
  column,
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { column?: boolean }) {
  return (
    <div
      className={classNames(
        styles.inner,
        {
          [styles.column]: column,
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export function RadioLabel({
  title,
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLLabelElement> & { title: React.ReactNode }) {
  return (
    <label className={classNames(styles.label, className)} {...props}>
      <span>
        <Markdown>{title}</Markdown>
      </span>
      {children}
    </label>
  )
}

export const RadioInput = forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement>
>(({ className, ...props }, ref) => {
  return (
    <input
      ref={ref}
      type="radio"
      // className={classNames(styles.label, className)}
      {...props}
    />
  )
})

function RadioGroup({
  field,
  value: selectedValue,
  onChange,
  children,
}: RadioGroupProps) {
  const [initialValue] = useState(selectedValue)

  return (
    <>
      <FieldContainer
        title={field.description}
        message={field.metadata?.message}
        guidance={field.metadata?.guidance}
        tagName="fieldset"
      >
        <RadioContainer>
          {field.descendants.map((descendant) => {
            const checked = selectedValue?.value === descendant.value
            const label = descendant.description || descendant.value

            return (
              <React.Fragment key={descendant.id}>
                <RadioLabel title={label}>
                  <RadioInput
                    name={field.fieldKey}
                    checked={checked}
                    defaultValue={undefined}
                    value={descendant.value}
                    onChange={(_: React.ChangeEvent<HTMLInputElement>) => {
                      onChange?.({
                        id: initialValue?.id || Date.now(),
                        fieldNodeId: field.id,
                        fieldKey: field.fieldKey,
                        value: descendant.value,
                      })
                    }}
                  />
                </RadioLabel>
              </React.Fragment>
            )
          })}
          {children}
        </RadioContainer>
      </FieldContainer>
    </>
  )
}

export default memo(RadioGroup)
