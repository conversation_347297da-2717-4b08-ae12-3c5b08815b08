import type { FieldNode } from '@store/services/fields/codegen'
import { equalsProperty } from '@util/helpers'

export type FieldType = FieldNode['fieldType']

export type FieldComponent = (field: FieldNode) => JSX.Element

export type FieldValue = {
  id: number
  fieldNodeId: number
  fieldKey: string
  value?: string
}

export const fieldOfType =
  (...fieldTypes: FieldType[]) =>
  (field: FieldNode) =>
    fieldTypes.includes(field.fieldType)

export const isSelectField = fieldOfType('SELECT')

export const fieldIdToName = (fieldId: string | number) => `field-id-${fieldId}`

export const fieldIdToFieldName = (fieldId: string | number) =>
  `values.${fieldIdToName(fieldId)}`

export const toFieldPropertyName = (
  fieldId: string | number,
  property: keyof FieldValue
) => `${fieldIdToFieldName(fieldId)}.${property}`

export const fieldIdToValueName = (fieldId: string | number) =>
  toFieldPropertyName(fieldId, 'value')

export const valueByFieldId = (fieldId: number) => (value: FieldValue) =>
  value.fieldNodeId === fieldId

export const valueByFieldKey = (fieldKey: string) => (value: FieldValue) =>
  value.fieldKey === fieldKey

export const findFieldByValue = (fields: FieldNode[] = [], value?: string) =>
  fields.find(equalsProperty('value', value))

export const findValueByKey = (values: FieldValue[], key: string) =>
  values.find(valueByFieldKey(key))?.value

export const updateValue = (
  values: FieldValue[],
  value: FieldValue
): FieldValue[] =>
  values.map((v: FieldValue) => {
    if (v.fieldNodeId !== value.fieldNodeId) return v
    return {
      ...v,
      ...value,
    }
  })
