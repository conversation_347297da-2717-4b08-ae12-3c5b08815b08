import React, { memo } from 'react'
import { Button } from 'antd'
import styles from './Uploads.module.css'
import fontStyles from '@styles/font.module.css'

export type UploadedFile = {
  pk: number
  fileName: string
  url: string
}

type Props = {
  uploads?: UploadedFile[]
  onRemove?: (uploadedFile: UploadedFile) => void
}

/**
 * List of uploaded files with urls and remove button
 */
const Uploads = ({ uploads = [], onRemove, ...props }: Props) => {
  return (
    <div className={styles.container} {...props}>
      <span className={fontStyles.headingSmall}>Existing Uploads</span>
      <ul className={styles.list}>
        {uploads.map((uploadedFile) => (
          <li key={uploadedFile.pk} className={styles.item}>
            <a
              href={uploadedFile.url}
              target="_blank"
              rel="noreferrer"
              className={styles.fileName}
            >
              {uploadedFile.fileName}
            </a>
            {Boolean(onRemove) && (
              <Button
                type="link"
                size="small"
                onClick={() => {
                  onRemove?.(uploadedFile)
                }}
              >
                Remove
              </Button>
            )}
          </li>
        ))}
      </ul>
    </div>
  )
}

export default memo(Uploads)
