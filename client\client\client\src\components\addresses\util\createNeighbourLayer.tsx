import type { Feature, MultiPolygon } from 'geojson'
import React from 'react'
import { GeoJSON, Tooltip } from 'react-leaflet'
import type { NavigateFunction } from 'react-router-dom'
import { DEFAULT_STYLES } from '@util/layers'
import type { AddressNeighbourProperties } from '../../../models/address/AddressNeighbour'
import { getRandomColor } from './getRandomColor'

export const ROAD_SUFFIX_RX =
  /(?<=ALLEY|ARCADE|AVENUE|BOULEVARD|CIRCLE|CLOSE|COURT|CRESCENT|DRIVE|ESPLANADE|GLADE|GREEN|GROVE|HIGHWAY|LANE|LOOP|MALL|MEWS|PARADE|PLACE|PROMENADE|QUAY|RISE|ROAD|SQUARE|HIGHWAY|STEPS|STREET|TERRACE|TRACK|WALK|WAY|WHARF)[ ]{1}/g

export const truncateName = (name: string) => {
  if (name.length > 30) {
    return `${name.slice(0, 30)}...`
  }
  return name
}

export function createNeighbourLayer(
  neighbour: Feature<MultiPolygon, AddressNeighbourProperties>,
  navigate: NavigateFunction,
  noVerbose?: boolean
) {
  return (
    <GeoJSON
      key={neighbour.properties.addressId}
      data={neighbour}
      style={{
        color: getRandomColor(),
        opacity: 1,
        weight: DEFAULT_STYLES.weight,
        fillOpacity: 0.35,
      }}
      onClick={() => {
        if (
          window.confirm(
            'Do you want to navigate away from the current address to the selected address?'
          )
        ) {
          navigate(`/address/${neighbour.properties.addressId}/`)
        }
      }}
    >
      <Tooltip
        direction={!neighbour.properties.fullAddress ? 'bottom' : 'center'}
        offset={[Math.random() * 5, Math.random() * 5]}
        opacity={1}
        permanent
      >
        {
          <div className="label-container">
            <>
              <span className="address">
                {neighbour.properties.fullAddress?.split(ROAD_SUFFIX_RX)[0]}
              </span>
              {!noVerbose ? (
                <span className="owners">
                  {truncateName(neighbour.properties.owners[0])}
                </span>
              ) : (
                <></>
              )}
            </>
          </div>
        }
      </Tooltip>
    </GeoJSON>
  )
}
