import React from 'react'
import classNames from 'classnames'
import EntitledLink from '@components/EntitledLink'
import Dropdown from './Dropdown'
import Label from './Label'
import type { MenuItem } from './helpers'
import styles from './Menu.module.css'
import { useSidebar } from './context'
import ChevronUp from '@components/icons/ChevronUp'
import ChevronDown from '@components/icons/ChevronDown'

type Props = {
  items: MenuItem[]
  isNested?: boolean
}

function Menu({ items, isNested, ...props }: Props) {
  return (
    <ul
      className={classNames(styles.container, {
        [styles.nested]: isNested,
      })}
      {...props}
    >
      {items.map((item) => (
        <Item key={item.name} {...item} />
      ))}
    </ul>
  )
}

function Item({ requiredEntitlements = [], ...item }: MenuItem) {
  const { customerId, disabled: sidebarDisabled } = useSidebar()

  const disabled = !customerId || sidebarDisabled || item.disabled

  const dropdown = 'children' in item

  return (
    <>
      <li
        key={item.name}
        className={classNames({
          [styles.dropdown]: dropdown,
        })}
      >
        {dropdown ? (
          <Dropdown
            title={({ open }) => (
              <Label {...item} icon={open ? <ChevronUp /> : <ChevronDown />} />
            )}
          >
            <Menu isNested items={item.children} />
          </Dropdown>
        ) : (
          <EntitledLink
            end
            to={
              item.name === '/'
                ? `/customer/${customerId}`
                : `/customer/${customerId}/${item.name}`
            }
            title={item.label}
            disabled={disabled}
            requiredEntitlements={requiredEntitlements}
          >
            {({ isActive, isDisabled }) => (
              <Label
                {...item}
                active={isActive}
                disabled={disabled || isDisabled}
              />
            )}
          </EntitledLink>
        )}
      </li>
    </>
  )
}

export default Menu
