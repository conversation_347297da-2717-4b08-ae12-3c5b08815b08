import {
  GlobalOutlined,
  LoadingOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { Button, Dropdown, Menu, type MenuProps } from 'antd'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from '@store'
import { getBoundsString, getCenterString } from '@store/features/explorer'
import sdk from '@store/services/sdk'
import { uiSlice } from '@store/ui/slice'
import type { SaleFeatureCollection } from '@models/sales/SaleFeatureCollection'
import { truthy } from '@util/guards'
import { useDebounce } from '@util/useDebounce'
import { SIDEBAR_DROPDOWN_PROPS } from '../const'

const loadingItem = [
  {
    key: 'loading',
    label: 'Loading...',
    disabled: true,
    icon: <LoadingOutlined />,
  },
]

const TlaMenu = ({
  tlasLoading,
  setTlasLoading,
  setTlaNamesLoading,
}: {
  tlasLoading: string[]
  setTlasLoading: React.Dispatch<React.SetStateAction<string[]>>
  setTlaNamesLoading: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const dispatch = useDispatch()

  const bounds = useSelector(getBoundsString)
  const center = useSelector(getCenterString)

  const debouncedBounds = useDebounce(bounds, 800)
  const debouncedCenter = useDebounce(center, 800)

  const {
    data: tlaNames = [],
    isFetching: tlaNamesFetching,
    isLoading: tlaNamesLoading,
  } = sdk.useExplorerTlasRetrieveQuery({
    bounds: debouncedBounds,
    center: debouncedCenter,
  })

  useEffect(() => {
    setTlaNamesLoading(tlaNamesFetching || tlaNamesLoading)
  }, [setTlaNamesLoading, tlaNamesFetching, tlaNamesLoading])

  const [fetchTlaListings] = sdk.useLazyExplorerTlaListingsRetrieveQuery()

  const getLoading = useCallback(
    (tla: string) => tlasLoading.includes(tla),
    [tlasLoading]
  )

  const tlaList: MenuProps['items'] = useMemo(
    () =>
      (tlaNames as string[]).map((name) => {
        const loading = getLoading(name)
        return {
          key: name,
          label: name,
          disabled: loading,
          icon: loading ? <LoadingOutlined /> : <PlusOutlined />,
        }
      }),
    [getLoading, tlaNames]
  )

  const handleClick: NonNullable<MenuProps['onClick']> = useCallback(
    ({ key: tla }) => {
      void (async () => {
        setTlasLoading((tlas) => [...new Set([...tlas, tla])])
        const { data = { features: [] } } = await fetchTlaListings({ tla })
        const listingIds = (data as SaleFeatureCollection).features
          .map((feature) => feature.id)
          .filter(truthy)
          .map(String)
        dispatch(uiSlice.actions.addSelectedListingIds(listingIds))
        setTlasLoading((tlas) => tlas.filter((name) => name !== tla))
      })()
    },
    [dispatch, fetchTlaListings, setTlasLoading]
  )

  return (
    <Menu
      items={tlaNamesLoading ? loadingItem : tlaList}
      onClick={handleClick}
      style={{ maxHeight: '40vh', overflow: 'auto' }}
    />
  )
}

const SelectFromTla = () => {
  const [tlasNamesLoading, setTlaNamesLoading] = useState(false)
  const [tlasLoading, setTlasLoading] = useState<string[]>([])

  const isLoading = useMemo(
    () => tlasNamesLoading || !!tlasLoading.length,
    [tlasNamesLoading, tlasLoading]
  )

  return (
    <>
      <Dropdown
        {...SIDEBAR_DROPDOWN_PROPS}
        overlay={
          <TlaMenu
            tlasLoading={tlasLoading}
            setTlasLoading={setTlasLoading}
            setTlaNamesLoading={setTlaNamesLoading}
          />
        }
      >
        <Button
          size="small"
          icon={isLoading ? <LoadingOutlined /> : <GlobalOutlined />}
        >
          Select TLA
        </Button>
      </Dropdown>
    </>
  )
}

export default SelectFromTla
