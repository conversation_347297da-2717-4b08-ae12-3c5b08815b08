import type { Feature, Geometry } from 'geojson'
import React from 'react'
import { GeoJSON, Tooltip } from 'react-leaflet'
import styles from './TitleTooltipAreaLayer.module.scss'

const TitleTooltipAreaLayer = ({
  feature,
  title,
  areaString,
  color,
}: {
  feature: Feature<Geometry | null>
  title: string | undefined
  areaString: string
  color: string
}) => {
  if (feature.geometry === null || feature.geometry === undefined) {
    return null
  }

  return (
    <GeoJSON
      key={feature.id}
      data={feature}
      className={styles.layerStyle}
      style={{ color: color, weight: 3 }}
    >
      <Tooltip
        className={styles.titleTooltip}
        direction={!title ? 'bottom' : 'center'}
        offset={[0, 0]}
        opacity={1}
        permanent
      >
        <div>
          <div className={styles.title}>{title}</div>
          <div className="area">{areaString}</div>
        </div>
      </Tooltip>
    </GeoJSON>
  )
}

export default TitleTooltipAreaLayer
