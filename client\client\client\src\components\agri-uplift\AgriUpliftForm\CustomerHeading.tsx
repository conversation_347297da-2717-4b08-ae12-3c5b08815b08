import { pick } from 'lodash'
import {
  useTradingGroupCustomersListQuery,
  useTradingGroupRetrieveQuery,
} from '@store/services/sdk'
import { CustomerTag } from '@components/customer/CustomerTags'
import font from '@styles/font.module.css'
import styles from './CustomerHeading.module.css'

function CustomerHeading({ pk }: { pk: string }) {
  const { data: group } = useTradingGroupRetrieveQuery({ pk })
  const { data: customers = [] } = useTradingGroupCustomersListQuery({ pk })

  if (!group) return null

  return (
    <div>
      <h1
        className={font.body}
        style={{
          marginBottom: 'var(--space-0)',
        }}
      >
        {group?.tradingGroupName}
      </h1>
      <ul className={styles.list}>
        {customers.map((customer) => (
          <li key={customer.pk} className={styles.listItem}>
            <CustomerTag
              {...pick(customer, [
                'entityName',
                'anzsic',
                'customerId',
                'customerNumber',
                'customerSetCode',
              ])}
              size="m"
              style={{ marginBottom: 0, border: 'none', padding: 0 }}
              className={styles.tag}
            />
          </li>
        ))}
      </ul>
    </div>
  )
}

export default CustomerHeading
