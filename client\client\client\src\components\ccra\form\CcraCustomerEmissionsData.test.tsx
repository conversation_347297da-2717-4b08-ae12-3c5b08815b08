import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Form } from 'antd'
import CustomerEmissionsData from './CcraCustomerEmissionsData'
import { getFieldName } from './helpers'
import { CCRA_CORPORATE_FORM_FIELD } from './const'
import type { CcraBusinessUnit } from './types'

// The components below are mocked to:
// 1. Focus on testing CustomerEmissionsData's logic in isolation
// 2. Avoid rendering complex component trees that would complicate tests
// 3. Improve test performance and reliability
// 4. Make tests more predictable by controlling child component behavior
jest.mock('./CustomFields/EditableTableFieldRevenueOrOutput', () => {
  return {
    __esModule: true,
    default: () => (
      <div data-testid="revenue-output-table">Mock Revenue Table</div>
    ),
  }
})

jest.mock('./CustomFields/CcraFonterraEmissionsData', () => {
  return {
    __esModule: true,
    default: () => (
      <div data-testid="fonterra-emissions">Mock Fonterra Emissions</div>
    ),
  }
})

jest.mock('./CustomFields/CcraBeefLambEmissionsData', () => {
  return {
    __esModule: true,
    default: () => (
      <div data-testid="beef-lamb-emissions">Mock Beef Lamb Emissions</div>
    ),
  }
})

jest.mock('./CustomFields/ProcessFuels', () => {
  return {
    __esModule: true,
    default: () => <div data-testid="process-fuels">Mock Process Fuels</div>,
  }
})

jest.mock('./ReportingPeriod', () => {
  return {
    __esModule: true,
    default: () => (
      <div data-testid="reporting-period">Mock Reporting Period</div>
    ),
  }
})

interface TestFormWrapperProps {
  initialValues?: Record<string, unknown>
  businessUnit?: CcraBusinessUnit
  children: React.ReactNode
}

const TestFormWrapper = ({
  initialValues = {},
  children,
}: TestFormWrapperProps) => {
  const [form] = Form.useForm()

  return (
    <Form form={form} initialValues={initialValues}>
      {children}
    </Form>
  )
}

const baseValues = { [getFieldName('knowsEmissions')[0]]: true }

describe('CustomerEmissionsData', () => {
  const renderComponent = (initialValues = {}) => {
    return render(
      <TestFormWrapper initialValues={initialValues}>
        <CustomerEmissionsData />
      </TestFormWrapper>
    )
  }

  describe('Initial rendering', () => {
    test('renders the knowsEmissions radio button group', () => {
      renderComponent()
      expect(
        screen.getByText('Does the customer know their emissions?')
      ).toBeInTheDocument()
    })

    test('does not render other fields before knowsEmissions is selected', () => {
      renderComponent()
      expect(
        screen.queryByText(/How were the emissions measured?/i)
      ).not.toBeInTheDocument()
      expect(
        screen.queryByText(/emissions data entry/i)
      ).not.toBeInTheDocument()
    })
  })

  describe('When "knowsEmissions" is false', () => {
    test('renders emissions measurement plan field', () => {
      renderComponent({ [getFieldName('knowsEmissions')[0]]: false })

      expect(
        screen.queryByLabelText('How were the emissions measured?')
      ).not.toBeInTheDocument()
    })

    test('does not render emissions data entry section', () => {
      renderComponent({ [getFieldName('knowsEmissions')[0]]: false })

      expect(screen.queryByText('Emissions Data Entry')).not.toBeInTheDocument()
    })

    test('renders does the customer plan to measure emissions', () => {
      renderComponent({ [getFieldName('knowsEmissions')[0]]: false })

      expect(
        screen.queryByLabelText(
          'Does the customer plan to measure their emissions?'
        )
      ).toBeInTheDocument()
    })
  })

  describe('When "knowsEmissions" is true', () => {
    test('renders emissions measurement method field', () => {
      renderComponent(baseValues)

      expect(
        screen.getByLabelText(/How were the emissions measured?/i)
      ).toBeInTheDocument()
      expect(
        screen.getByLabelText(/Emissions verified by/i)
      ).toBeInTheDocument()
    })

    test('renders emissions data entry section', () => {
      renderComponent(baseValues)

      expect(screen.getByText(/emissions data entry/i)).toBeInTheDocument()
      expect(screen.getByTestId('reporting-period')).toBeInTheDocument()
    })

    describe('Emissions Measurement Methods', () => {
      test('shows Fonterra emissions data component when measurement method is "Fonterra"', () => {
        renderComponent({
          ...baseValues,
          [getFieldName('emissionsMeasurementMethod')[0]]: 'Fonterra',
        })

        expect(screen.getByTestId('fonterra-emissions')).toBeInTheDocument()
      })

      test('shows Beef + Lamb emissions data component when measurement method is "Beef + Lamb"', () => {
        renderComponent({
          ...baseValues,
          [getFieldName('emissionsMeasurementMethod')[0]]: 'Beef + Lamb',
        })

        expect(screen.getByTestId('beef-lamb-emissions')).toBeInTheDocument()
      })

      test('shows Overseer specific fields when measurement method is "Overseer"', () => {
        renderComponent({
          ...baseValues,
          [getFieldName('emissionsMeasurementMethod')[0]]: 'Overseer',
        })

        expect(screen.getByLabelText('Methane')).toBeInTheDocument()
        expect(
          screen.getByLabelText('Nitrogen Oxide (N2O)')
        ).toBeInTheDocument()
        expect(screen.getByLabelText('Carbon (CO2e)')).toBeInTheDocument()
      })

      test('shows Synlait specific fields when measurement method is "Synlait"', () => {
        renderComponent({
          ...baseValues,
          [getFieldName('emissionsMeasurementMethod')[0]]: 'Synlait',
        })

        expect(screen.getByLabelText('CO2e (mT)')).toBeInTheDocument()
        expect(screen.getByLabelText('CO2e (kg)')).toBeInTheDocument()
        expect(screen.getByLabelText('CO₂e/KgMS')).toBeInTheDocument()
        expect(screen.getByLabelText('KgMS')).toBeInTheDocument()
      })

      test('shows default emission scope fields when measurement method is not specific', () => {
        renderComponent({
          ...baseValues,
          [getFieldName('emissionsMeasurementMethod')[0]]: 'Other Method',
        })

        expect(screen.getByLabelText('Scope 1')).toBeInTheDocument()
        expect(screen.getByLabelText('Scope 2')).toBeInTheDocument()
        expect(screen.getByLabelText('Scope 3')).toBeInTheDocument()
      })
    })

    test('does not render the production data section for non agri customer', () => {
      renderComponent(baseValues)

      expect(screen.queryByText('Production Data')).not.toBeInTheDocument()
      expect(
        screen.queryByTestId('revenue-output-table')
      ).not.toBeInTheDocument()
      expect(screen.queryByTestId('process-fuels')).not.toBeInTheDocument()
    })

    describe('Carbon Credits Section', () => {
      test('renders the carbon credits radio button group', () => {
        renderComponent(baseValues)

        expect(screen.getByText('Carbon Credits')).toBeInTheDocument()
        expect(
          screen.getByText(
            'Has the customer obtained carbon credits to reduce their carbon footprint?'
          )
        ).toBeInTheDocument()
      })

      test('details field is disabled when hasCarbonCredits is not set', () => {
        renderComponent(baseValues)

        const detailsField = screen.getByLabelText(
          /Please provide details of the amount and source of carbon credits/i
        )
        expect(detailsField).toBeDisabled()
      })

      test('details field is enabled when hasCarbonCredits is true', () => {
        renderComponent({
          ...baseValues,
          [getFieldName('hasCarbonCredits')[0]]: true,
        })

        const detailsField = screen.getByLabelText(
          /Please provide details of the amount and source of carbon credits/i
        )
        expect(detailsField).not.toBeDisabled()
      })
    })
  })

  describe('Business Unit specific rendering', () => {
    test('does not render items when business unit does not match', () => {
      renderComponent({
        ...baseValues,
        businessUnit: CCRA_CORPORATE_FORM_FIELD,
      })

      expect(screen.queryByText(/production data/i)).not.toBeInTheDocument()
      expect(
        screen.queryByTestId('revenue-output-table')
      ).not.toBeInTheDocument()
    })
  })

  describe('Field validation', () => {
    test('emissions scope validation requires at least one field to be filled', async () => {
      const { container } = renderComponent({
        ...baseValues,
        [getFieldName('emissionsMeasurementMethod')[0]]: 'Other Method',
      })

      const form = container.querySelector('form')
      if (form) {
        fireEvent.submit(form)
      }

      await waitFor(() => {
        expect(
          screen.getByText(
            /at least one of the emissions scope fields must be filled/i
          )
        ).toBeInTheDocument()
      })
    })

    test('emissions scope validation passes when at least one field is filled', async () => {
      const { container } = renderComponent({
        ...baseValues,
        [getFieldName('emissionsMeasurementMethod')[0]]: 'Other Method',
        [getFieldName('emissionsScope1')[0]]: 10,
      })

      const form = container.querySelector('form')
      if (form) {
        fireEvent.submit(form)
      }

      await waitFor(() => {
        expect(
          screen.queryByText(
            /at least one of the emissions scope fields must be filled/i
          )
        ).not.toBeInTheDocument()
      })
    })
  })

  describe('Calculated fields', () => {
    test('co2eKg updates when co2Mt changes', async () => {
      renderComponent({
        ...baseValues,
        [getFieldName('emissionsMeasurementMethod')[0]]: 'Synlait',
      })

      const co2MtInput = screen.getByLabelText('CO2e (mT)')
      fireEvent.change(co2MtInput, { target: { value: '10' } })

      await waitFor(() => {
        const co2eKgInput = screen.getByLabelText('CO2e (kg)')
        expect(co2eKgInput).toHaveValue('10000')
      })
    })

    test('synlaitKgms updates when both co2Mt and synlaitCo2eKgms change', async () => {
      renderComponent({
        ...baseValues,
        [getFieldName('emissionsMeasurementMethod')[0]]: 'Synlait',
      })

      const co2MtInput = screen.getByLabelText('CO2e (mT)')
      fireEvent.change(co2MtInput, { target: { value: '10' } })

      const synlaitCo2eKgmsInput = screen.getByLabelText('CO₂e/KgMS')
      fireEvent.change(synlaitCo2eKgmsInput, { target: { value: '2' } })

      await waitFor(() => {
        const synlaitKgmsInput = screen.getByLabelText('KgMS')
        expect(synlaitKgmsInput).toHaveValue('5000')
      })
    })
  })
})
