import React from 'react'
import type { DistrictValuationRoll } from '@models/dvr/DistrictValuationRoll'
import { formatArea } from '@util'
import { ExportBreakdownTable, ExportDetailTable } from '../components'
import { ExportSection } from '../components/ExportSection'

interface ExportDistrictValuationRollSectionProps {
  districtValuationRoll: DistrictValuationRoll | null
  isFetching?: boolean
}

export const ExportDistrictValuationRollSection = ({
  districtValuationRoll,
  isFetching,
}: ExportDistrictValuationRollSectionProps) => {
  return (
    <>
      {!isFetching && !districtValuationRoll ? (
        <ExportSection title={'No rating information available'} />
      ) : (
        <>
          <ExportSection
            title={'Rating Valuation Details'}
            isFetching={isFetching}
          >
            {districtValuationRoll && (
              <ExportDetailTable
                data={[
                  {
                    label: 'Valuation Reference',
                    value: districtValuationRoll.valRef,
                  },
                  {
                    label: 'Territorial Authority',
                    value: districtValuationRoll.tlaName,
                  },
                  {
                    label: 'Valuation Date',
                    value: districtValuationRoll.valDate,
                  },
                  {
                    label: 'Land Area',
                    value: formatArea(
                      districtValuationRoll.landArea / 1e4,
                      'ha'
                    ),
                  },
                  {
                    label: 'Floor Area',
                    value: formatArea(districtValuationRoll.floorArea, 'm2'),
                  },
                  {
                    label: 'Legal Description',
                    value: districtValuationRoll.legalDesc,
                  },
                  {
                    label: 'Land Use',
                    value: districtValuationRoll.landUseDesc,
                  },
                  {
                    label: 'Zoning',
                    value: districtValuationRoll.landZoneDesc,
                  },
                ]}
              />
            )}
          </ExportSection>
          <ExportSection title="Rating Valuation Breakdown">
            {districtValuationRoll && (
              <ExportBreakdownTable
                totalLabel="Capital Value"
                data={[
                  {
                    label: 'Land Value',
                    value: districtValuationRoll.lv,
                  },
                  {
                    label: 'Improvements Value',
                    value: districtValuationRoll.iv,
                  },
                ]}
              />
            )}
          </ExportSection>
        </>
      )}
    </>
  )
}
