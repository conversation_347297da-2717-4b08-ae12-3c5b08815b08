import { FilePdfOutlined } from '@ant-design/icons'
import { Button, type ButtonProps } from 'antd'
import { useDispatch } from 'react-redux'
import { actions } from '@store/features/customer'

type Props = ButtonProps

const CustomerReportDrawerButton = (props: Props) => {
  const dispatch = useDispatch()

  return (
    <Button
      icon={<FilePdfOutlined />}
      onClick={() => {
        dispatch(actions.openReportDrawer())
      }}
      {...props}
    >
      Report
    </Button>
  )
}

export default CustomerReportDrawerButton
