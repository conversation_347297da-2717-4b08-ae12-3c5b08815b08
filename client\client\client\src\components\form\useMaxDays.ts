import type { RangePickerProps } from 'antd/lib/date-picker'
import type { Moment } from 'moment'
import { useCallback, useState } from 'react'

function useMaxDays(maxDays = 365) {
  const [dates, setDates] = useState<RangePickerProps['value']>()

  const disabledDate = useCallback(
    (date: Moment) => {
      if (!dates) return false
      const [start, end] = dates
      const tooLate = start && date.diff(start, 'days') > maxDays
      const tooEarly = end && date.diff(end, 'days') > maxDays
      return Boolean(tooLate) || Boolean(tooEarly)
    },
    [maxDays, dates]
  )

  const onCalendarChange = useCallback((value) => {
    setDates(value)
  }, [])

  const onOpenChange = useCallback((open: boolean) => {
    if (open) {
      setDates([null, null])
    } else {
      setDates(null)
    }
  }, [])

  return { disabledDate, onCalendarChange, onOpenChange }
}

export default useMaxDays
