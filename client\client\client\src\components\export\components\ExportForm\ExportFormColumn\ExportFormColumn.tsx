import type React from 'react'

interface ExportFormColumnProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
}

const ExportFormColumn = (props: ExportFormColumnProps) => {
  const { children, ...divProps } = props
  return (
    <div className="ExportFormColumn" {...divProps}>
      {props?.children}
    </div>
  )
}

export default ExportFormColumn
