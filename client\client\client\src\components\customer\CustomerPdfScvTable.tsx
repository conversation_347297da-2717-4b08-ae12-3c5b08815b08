import React from 'react'
import { PdfTable, type PdfTableProps } from '@components/pdf'
import type { PdfTableRowProps } from '@components/pdf/PdfTable/PdfTableRow'
import type { PdfTableColumnOptions } from '@components/pdf/PdfTable/helpers'
import type { CustomerFinancialsType } from '@store/features/customer'
import { getDataYears } from '@util/data'
import { valueToProperty } from '@util/helpers'
import type { DataVisualisationItem } from '@util/types'
import { getMode } from '../customer/helpers'
import { groupByMeasure } from './scv/CustomerScvMetricTable'

type Props = Omit<PdfTableProps, 'rows'> & {
  data: DataVisualisationItem[] | undefined
  dataType?: CustomerFinancialsType
}

const CustomerPdfScvTable = ({ data = [], dataType, ...props }: Props) => {
  const columns: PdfTableColumnOptions[] = [
    { key: 'measure', weighting: 2 },
    ...getDataYears(data).map(valueToProperty('key')),
  ]

  const rows: PdfTableRowProps[] = groupByMeasure(data, getMode(dataType))

  if (!data.length) return null

  return <PdfTable columns={columns} rows={rows} striped {...props} />
}

export default CustomerPdfScvTable
