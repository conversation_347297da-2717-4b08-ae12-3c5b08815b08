import React, { useState } from 'react'
import { fireEvent, render } from '@testing-library/react'
import FormattedDateRangePicker from './FormattedDateRangePicker'
import type { DateInputValue } from './helpers'

const DELIMITER = '-'
const START_YEAR = '2023'
const START_MONTH = '07'
const START_DAY = '01'
const START_UPDATED_DAY = '02'

const START = [START_YEAR, START_MONTH, START_DAY]
const START_INPUT = joinDateValues(START)
const START_OUTPUT = joinDateValues([...START].reverse())

const START_UPDATED = [START_YEAR, START_MONTH, START_UPDATED_DAY]
const START_UPDATED_INPUT = joinDateValues(START_UPDATED)
const START_UPDATED_OUTPUT = joinDateValues([...START_UPDATED].reverse())

const END = [(+START_YEAR + 1).toString(), START_MONTH, START_DAY]
const END_INPUT = joinDateValues(END)
const END_OUTPUT = joinDateValues([...END].reverse())

function joinDateValues(values: string[]) {
  return values.join(DELIMITER)
}

describe('Selecting a date', () => {
  test('Displays correct format in input', () => {
    const view = render(<TestApp />)
    const start = view.queryByPlaceholderText('Start date')
    expect(start).toHaveValue(START_OUTPUT)
  })

  test('Displays correct formats after change', async () => {
    const view = render(<TestApp />)
    const startInput = view.getByPlaceholderText('Start date')
    const endInput = view.getByPlaceholderText('End date')

    fireEvent.mouseDown(startInput)
    const day = await view.findByTitle(START_UPDATED_INPUT)
    fireEvent.click(day)

    expect(startInput).toHaveValue(START_UPDATED_OUTPUT)
    expect(endInput).toHaveValue(END_OUTPUT)
    expect(view.queryByTestId('start')).toBeInTheDocument()
    expect(view.queryByTestId('start')).toHaveTextContent(START_UPDATED_INPUT)
    expect(view.queryByTestId('end')).toHaveTextContent(END_INPUT)
  })
})

function TestApp() {
  const [value, setValue] = useState<[DateInputValue, DateInputValue]>([
    START_INPUT,
    END_INPUT,
  ])

  return (
    <div>
      <FormattedDateRangePicker
        value={value}
        onChange={(v) => {
          setValue(v as [DateInputValue, DateInputValue])
        }}
      />
      <div data-testid="start">{value[0]}</div>
      <div data-testid="end">{value[1]}</div>
    </div>
  )
}
