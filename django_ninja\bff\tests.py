import json
import jwt
from unittest.mock import patch, MagicMock
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings

from .views import (
    validate_jwt_token,
    extract_user_from_token,
    get_client_ip,
    create_rate_limit_key,
    PingFederateAuthenticationError,
    RateLimitExceededError
)


class BFFAuthenticationTests(TestCase):
    """Test cases for BFF authentication functionality"""

    def setUp(self):
        self.client = Client()
        self.valid_payload = {
            'sub': 'user123',
            'preferred_username': 'testuser',
            'email': '<EMAIL>',
            'given_name': 'Test',
            'family_name': 'User',
            'groups': ['users'],
            'roles': ['user'],
            'permissions': ['read'],
            'exp': 9999999999,  # Far future
            'iat': **********,
            'aud': 'esgis-api',
            'iss': 'https://pingfederate.example.com'
        }

    def test_extract_user_from_token(self):
        """Test user information extraction from JWT payload"""
        user_info = extract_user_from_token(self.valid_payload)

        self.assertEqual(user_info['user_id'], 'user123')
        self.assertEqual(user_info['username'], 'testuser')
        self.assertEqual(user_info['email'], '<EMAIL>')
        self.assertEqual(user_info['first_name'], 'Test')
        self.assertEqual(user_info['last_name'], 'User')
        self.assertEqual(user_info['groups'], ['users'])
        self.assertEqual(user_info['roles'], ['user'])
        self.assertEqual(user_info['permissions'], ['read'])

    def test_get_client_ip_with_forwarded_header(self):
        """Test IP extraction with X-Forwarded-For header"""
        request = MagicMock()
        request.META = {
            'HTTP_X_FORWARDED_FOR': '***********, ********',
            'REMOTE_ADDR': '127.0.0.1'
        }

        ip = get_client_ip(request)
        self.assertEqual(ip, '***********')

    def test_get_client_ip_without_forwarded_header(self):
        """Test IP extraction without X-Forwarded-For header"""
        request = MagicMock()
        request.META = {
            'REMOTE_ADDR': '*************'
        }

        ip = get_client_ip(request)
        self.assertEqual(ip, '*************')

    def test_create_rate_limit_key(self):
        """Test rate limit key creation"""
        with patch('time.time', return_value=**********):
            key = create_rate_limit_key('user:123', 'api', 'default')
            # Should include timestamp bucket
            self.assertIn('rate_limit:default:user:123:api:', key)


class BFFHealthCheckTests(TestCase):
    """Test cases for BFF health check functionality"""

    def setUp(self):
        self.client = Client()

    def test_health_check_endpoint(self):
        """Test health check endpoint accessibility"""
        response = self.client.get('/bff/health/')

        # Should return JSON response
        self.assertEqual(response['Content-Type'], 'application/json')

        # Parse response
        data = json.loads(response.content)
        self.assertIn('status', data)
        self.assertIn('timestamp', data)
        self.assertIn('components', data)


class BFFConfigurationTests(TestCase):
    """Test cases for BFF configuration"""

    def test_backend_api_configuration(self):
        """Test that backend APIs are properly configured"""
        from .views import BACKEND_APIS

        # Check that required APIs are configured
        required_apis = ['riskradar', 'green', 'finance', 'ccra', 'propertyflow']

        for api_name in required_apis:
            self.assertIn(api_name, BACKEND_APIS)
            self.assertIn('base_url', BACKEND_APIS[api_name])
            self.assertIn('timeout', BACKEND_APIS[api_name])

    def test_rate_limit_configuration(self):
        """Test rate limiting configuration"""
        from .views import RATE_LIMIT_CONFIG

        self.assertIn('DEFAULT_LIMIT', RATE_LIMIT_CONFIG)
        self.assertIn('DEFAULT_WINDOW', RATE_LIMIT_CONFIG)
        self.assertIn('BURST_LIMIT', RATE_LIMIT_CONFIG)
        self.assertIn('BURST_WINDOW', RATE_LIMIT_CONFIG)

        # Check that limits are positive
        self.assertGreater(RATE_LIMIT_CONFIG['DEFAULT_LIMIT'], 0)
        self.assertGreater(RATE_LIMIT_CONFIG['DEFAULT_WINDOW'], 0)
        self.assertGreater(RATE_LIMIT_CONFIG['BURST_LIMIT'], 0)
        self.assertGreater(RATE_LIMIT_CONFIG['BURST_WINDOW'], 0)


class BFFIntegrationTests(TestCase):
    """Integration tests for BFF functionality"""

    def setUp(self):
        self.client = Client()

    @patch('bff.views.authenticate_request')
    @patch('bff.views.route_request')
    def test_bff_proxy_endpoint_success(self, mock_route, mock_auth):
        """Test successful BFF proxy request"""
        # Mock authentication
        mock_auth.return_value = {
            'user_id': 'user123',
            'username': 'testuser',
            'permissions': ['read']
        }

        # Mock backend response
        mock_route.return_value = {
            'status_code': 200,
            'data': {'message': 'success'},
            'success': True
        }

        response = self.client.get('/bff/proxy/riskradar/health/')

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('data', data)
        self.assertIn('user', data)

    def test_bff_proxy_endpoint_no_auth(self):
        """Test BFF proxy request without authentication"""
        response = self.client.get('/bff/proxy/riskradar/health/')

        self.assertEqual(response.status_code, 401)
        data = json.loads(response.content)
        self.assertEqual(data['error'], 'Authentication failed')


class BFFErrorHandlingTests(TestCase):
    """Test cases for BFF error handling"""

    def setUp(self):
        self.client = Client()

    def test_invalid_json_in_aggregate_endpoint(self):
        """Test aggregate endpoint with invalid JSON"""
        response = self.client.post(
            '/bff/aggregate/',
            data='invalid json',
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 401)  # Will fail auth first

    @patch('bff.views.authenticate_request')
    def test_aggregate_endpoint_no_apis(self, mock_auth):
        """Test aggregate endpoint with no APIs specified"""
        mock_auth.return_value = {
            'user_id': 'user123',
            'username': 'testuser',
            'permissions': ['read']
        }

        response = self.client.post(
            '/bff/aggregate/',
            data=json.dumps({}),
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 400)
        data = json.loads(response.content)
        self.assertEqual(data['error'], 'No APIs specified')
