import React from 'react'
import LayerStyleSelection from '@components/map/components/LayerStyleSelection'
import useLayerStyle from '@hooks/useLayerStyle'
import type { LayerContext, LayerStyles } from '@store/features/map/types'

interface Props {
  context: LayerContext
  id: number
}

const ExplorerLayerStyleSelection = ({ context, id }: Props) => {
  const { layerStyle, update } = useLayerStyle(context, id)

  function updateStyles(updates: LayerStyles) {
    update({ ...updates, fillColor: updates.color })
  }

  return <LayerStyleSelection handler={updateStyles} styles={layerStyle} />
}

export default ExplorerLayerStyleSelection
