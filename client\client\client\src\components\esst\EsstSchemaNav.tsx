import type React from 'react'
import classNames from 'classnames'
import { NavLink } from 'react-router-dom'
import Link from '@components/anz/Link'
import SubmitAndNavButton from './EsstReportItemForm/SubmitAndNavButton'
import type { SchemaRoute } from './useSchemaRoutes'
import font from '@styles/new/font.module.css'
import styles from './EsstSchemaNav.module.css'
import { LoadingOutlined } from '@ant-design/icons'

type Props = {
  routes: SchemaRoute[]
  children?: React.ReactNode
  pathPrefix?: string
  forForm?: boolean
}

export default function EsstSchemaNav({
  routes,
  pathPrefix = '',
  forForm = false,
  children,
}: Props) {
  const LinkComponent = forForm ? SubmitAndNavButton : NavLink

  return (
    <div className={styles.container}>
      {children}
      <ol className={styles.list}>
        {routes.map((route) => {
          const to = `${pathPrefix}${route.to}`

          return (
            <li key={to} className={styles.item}>
              <LinkComponent
                to={to}
                className={classNames(font.medium, styles.sectionLink)}
              >
                <span className={styles.title}>
                  {route.title} <LoadingOutlined className={styles.loading} />
                </span>
              </LinkComponent>
            </li>
          )
        })}
      </ol>
      <div className={styles.footer}>
        <Link
          to={`${pathPrefix}summary`}
          className={classNames(font.medium, styles.link)}
          arrow
        >
          <span>Summary</span>
        </Link>
      </div>
    </div>
  )
}
