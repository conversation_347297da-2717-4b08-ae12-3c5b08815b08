import { SaveOutlined } from '@ant-design/icons'
import { <PERSON>ton, Card, type CardProps, Segmented, Table } from 'antd'
import type { ColumnType } from 'antd/lib/table'
import classnames from 'classnames'
import { useMemo, useState } from 'react'
import { CSVLink } from 'react-csv'
import ErrorBounds from '@components/ErrorBounds'
import useKpiStatement, {
  type StructuredKpi,
} from '@store/hooks/useKpiStatement'
import { useCustomerRetrieveQuery } from '@store/services/sdk'
import { formatDollarValue } from '@util'
import { skipArgObject } from '@util/helpers'
import { useCustomer } from '../context'
import styles from './CustomerMetricsCard.module.scss'
import CustomerKpiStatementSelect from '../CustomerKpiStatementSelect'
import useCustomerKpis from '../useCustomerKpis'

type MetricsView = 'Detailed View' | 'Simplified View'

export const CustomerMetricsCard = ({ ...props }: CardProps) => {
  const { pk } = useCustomer()

  const { kpis: filteredKpis, isFetching, isLoading, error } = useCustomerKpis()

  const { data: customer } = useCustomerRetrieveQuery(skipArgObject({ pk }))

  const { pivot, years } = useKpiStatement(
    filteredKpis ?? [],
    customer?.segment ?? 'W'
  )

  const [metricsView, setMetricsView] = useState<MetricsView>('Detailed View')

  const columns = [
    {
      dataIndex: 'measure',
      render: (
        value: string,
        { isHeading, isSubtotal, isTotal }: StructuredKpi
      ) => {
        return (
          <span
            className={classnames(
              {
                [styles.heading]: isHeading,
                [styles.subtotal]: isSubtotal,
                [styles.total]: isTotal,
              },
              styles.row
            )}
          >
            {value}
          </span>
        )
      },
    },
    ...(years ?? []).map((year) => ({
      dataIndex: year,
      title: year,
      render: (value: number, { isHeading, isNegative }: StructuredKpi) =>
        isHeading
          ? ''
          : isNegative || Number(value) < 0
            ? `(${formatDollarValue(value).replace('-', '')})`
            : formatDollarValue(value),
      align: 'right' as ColumnType<StructuredKpi>['align'],
    })),
  ]

  const simplifiedPivot = useMemo(() => {
    return pivot.filter((row) => row.isSubtotal || row.isHeading || row.isTotal)
  }, [pivot])

  const segmentedOptions: MetricsView[] = ['Detailed View', 'Simplified View']

  const CSVHeaders = [{ label: '', key: 'measure' }].concat(
    years.map((year) => {
      return { label: year, key: year }
    })
  )

  return (
    <Card
      title="Financial Metrics"
      loading={isFetching || isLoading}
      className={styles.card}
      extra={
        <div className={styles.extras}>
          <CSVLink
            data={metricsView === 'Simplified View' ? simplifiedPivot : pivot}
            headers={CSVHeaders}
            filename={`financial_metrics_${
              customer?.customerNumber ? customer.customerNumber : ''
            }`}
          >
            <Button title="Export CSV" icon={<SaveOutlined />} />
          </CSVLink>
          <CustomerKpiStatementSelect />
          <Segmented
            options={segmentedOptions}
            onChange={(value) => setMetricsView(value as MetricsView)}
          />
        </div>
      }
      {...props}
    >
      <ErrorBounds error={error}>
        <div className={styles.tableContainer}>
          <Table
            className={'unstyle'}
            rowClassName={(row) => (row.isHeading ? styles.headingRow : '')}
            columns={columns}
            dataSource={
              metricsView === 'Simplified View' ? simplifiedPivot : pivot
            }
            scroll={{ x: true }}
            rowKey={(row, idx) => {
              return `${row.measure}-${idx?.toString() ?? ''}`
            }}
            size="small"
            pagination={false}
          />
        </div>
      </ErrorBounds>
    </Card>
  )
}
