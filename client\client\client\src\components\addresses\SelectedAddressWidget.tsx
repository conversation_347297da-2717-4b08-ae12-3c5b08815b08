import { BlockOutlined, PrinterOutlined } from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Button, Descriptions, Table } from 'antd'
import React from 'react'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { ButtonWidget, Widget } from '@components/generic'
import { TitleNumberLabel } from '@components/generic/TitleNumberLabel'
import { formatDescription } from '@components/sales/dashboard/SelectedSaleDetails'
import {
  useGetAddressDvrQuery,
  useGetAddressMemorialsQuery,
  useGetAddressQuery,
  useGetAddressTitlesQuery,
} from '@store/services/address'
import { setRecentEntry } from '@store/ui/actions'
import type { TitleFeatureCollection } from '@models/title/TitleFeatureCollection'
import type { TitleMemorial } from '@types'
import { formatArea } from '@util'
import { AddressNearbySalesWidget } from './AddressesNearbySalesWidget'
import { SelectedAddressPreviousSalesWidget } from './SelectedAddressPreviousSales'

interface SelectedAddressWidgetProps {
  addressId?: string
  isFetching?: boolean
}

export const SelectedAddressTitlesTable = (props: {
  disablePagination?: boolean
  titles: TitleFeatureCollection | undefined
  isFetching?: boolean
}) => {
  const { titles, isFetching } = props
  return (
    <Widget title="Titles" icon={<BlockOutlined />} isFetching={isFetching}>
      <div className="agrigis-table">
        <Table
          rowKey={(row) => row.id?.toString() || ''}
          size="small"
          pagination={props?.disablePagination ? false : undefined}
          dataSource={titles?.features}
          columns={[
            {
              dataIndex: ['properties', 'titleNo'],
              title: 'Title',
              render: (_: string, row) => {
                return <TitleNumberLabel titleProperties={row.properties} />
              },
            },
            {
              dataIndex: ['properties', 'owners'],
              title: 'Owners',
              render: (text: string) => {
                if (text && text.length > 200) {
                  return `${text.slice(0, 200)}...`
                }
                return text
              },
            },
            {
              dataIndex: ['properties', 'mortgagee'],
              title: 'Mortgagees',
            },
            {
              dataIndex: ['properties', 'memorials'],
              title: 'Instrument Numbers',
              render: (value: TitleMemorial[] | undefined) => {
                if (value === undefined) {
                  return '-'
                }

                const mortgageMemorials = value.filter(
                  (memorial) =>
                    memorial.current === 'T' &&
                    memorial.mortgageInstrumentNumber
                )
                return (
                  <div className="instrument-numbers">
                    {mortgageMemorials
                      .map(
                        ({ mortgageInstrumentNumber }) =>
                          mortgageInstrumentNumber
                      )
                      .join(', ')}
                  </div>
                )
              },
            },
            {
              dataIndex: ['properties', 'surveyArea'],
              title: 'Survey Area',
              render: (_, record) => {
                return formatArea(
                  (record.properties?.surveyArea ?? 0) / 1e4,
                  'ha'
                )
              },
            },
          ]}
        />
      </div>
    </Widget>
  )
}

interface AddressMemorialsTableProps {
  addressId: string
}

export const AddressMemorialsTable = (props: AddressMemorialsTableProps) => {
  const { addressId } = props
  const [page, setPage] = useState<number>(1)
  const { data: memorials, isFetching } = useGetAddressMemorialsQuery(
    { addressId, page },
    { skip: addressId === undefined }
  )

  // biome-ignore lint/correctness/useExhaustiveDependencies:
  useEffect(() => {
    setPage(1)
  }, [addressId])

  const sortedMemorials = memorials?.results.slice().sort((a, b) => {
    const dateA = new Date(a.instrumentLodgedDatetime)
    const dateB = new Date(b.instrumentLodgedDatetime)
    if (dateA > dateB) {
      return -1
    }
    if (dateA < dateB) {
      return 1
    }
    return Number(a.instrumentNumber) - Number(b.instrumentNumber)
  })

  return (
    <Widget title="Memorials" isFetching={isFetching}>
      <div className="agrigis-table">
        <Table
          size="small"
          rowKey={(row) => `${row.instrumentNumber}-${row.id}`}
          dataSource={sortedMemorials}
          columns={[
            { dataIndex: 'instrumentNumber', title: 'Instrument' },
            { dataIndex: 'titleNo', title: 'Title' },
            { dataIndex: 'memorialText', title: 'Description' },
            { dataIndex: 'instrumentType', title: 'Instrument Type' },
          ]}
          pagination={false}
        />
        <div className="agrigis-table-pagination">
          <Button
            onClick={() => setPage(page - 1)}
            disabled={!memorials?.previous}
          >
            Previous
          </Button>
          <div className="agrigis-table-pagination-current-page">
            {memorials?.currentPage}
          </div>
          <Button onClick={() => setPage(page + 1)} disabled={!memorials?.next}>
            Next
          </Button>
        </div>
      </div>
    </Widget>
  )
}

export const SelectedAddressWidget = (props: SelectedAddressWidgetProps) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { addressId: _addressId = '' } = useParams()

  let addressId: string
  if (props?.addressId) {
    addressId = props?.addressId
  } else {
    addressId = _addressId
  }

  const { data: address } = useGetAddressQuery(addressId ?? skipToken)
  const { data: dvr, isFetching: dvrIsFetching } = useGetAddressDvrQuery(
    addressId || '',
    {
      skip: addressId === undefined,
    }
  )
  const { data: titles, isFetching: titlesIsFetching } =
    useGetAddressTitlesQuery(addressId ?? skipToken)

  useEffect(() => {
    dispatch(
      setRecentEntry({
        categoryName: 'addresses',
        value: { id: addressId, ...address },
      })
    )
  }, [addressId, address, dispatch])

  return (
    <>
      {!props?.addressId ? (
        <Widget
          title={address?.fullAddress || 'No Address Selected'}
          isFetching={dvrIsFetching}
          type="page-header"
          extra={
            <ButtonWidget>
              <Button
                onClick={() =>
                  navigate(
                    `/map/?addressId=${addressId}&lat=${address?.lat}&lng=${address?.lng}`
                  )
                }
                disabled={!addressId}
              >
                View in Explorer
              </Button>
              <Button
                icon={<PrinterOutlined />}
                onClick={() => {
                  window.open(
                    `/exportAddress/${addressId}/`,
                    '_blank',
                    'toolbar=0,location=0,menubar=0,width=960,height=800,resizable=no,scrollbars=0'
                  )
                }}
                disabled={!addressId}
              >
                Print
              </Button>
            </ButtonWidget>
          }
        >
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Valuation Reference">
              {formatDescription(dvr?.valRef)}
            </Descriptions.Item>
            <Descriptions.Item label="Region">
              {formatDescription(dvr?.tlaName)}
            </Descriptions.Item>
            <Descriptions.Item label="Valuation Date">
              {formatDescription(dvr?.valDate)}
            </Descriptions.Item>
            <Descriptions.Item label="Improvements">
              {formatDescription(dvr?.improvement)}
            </Descriptions.Item>
            <Descriptions.Item label="Legal Description">
              {formatDescription(dvr?.legalDesc)}
            </Descriptions.Item>
            <Descriptions.Item label="Land Use">
              {formatDescription(dvr?.landUseDesc)}
            </Descriptions.Item>
            <Descriptions.Item label="Land Zones">
              {formatDescription(dvr?.landZoneDesc)}
            </Descriptions.Item>
          </Descriptions>
          <Descriptions bordered column={2}>
            <Descriptions.Item label="CV">
              {formatDescription(dvr?.cv)}
            </Descriptions.Item>
            <Descriptions.Item label="Land Value">
              {formatDescription(dvr?.lv)}
            </Descriptions.Item>
            <Descriptions.Item label="Improvements Value">
              {formatDescription(dvr?.iv)}
            </Descriptions.Item>
            <Descriptions.Item label="Land Area (Ha)">
              {(Number(dvr?.landArea ?? '0') / 1e4).toFixed(2)}
            </Descriptions.Item>
            <Descriptions.Item label="Floor Area (m2)">
              {formatDescription(dvr?.floorArea)}
            </Descriptions.Item>
          </Descriptions>
        </Widget>
      ) : (
        <></>
      )}
      <SelectedAddressTitlesTable
        titles={titles}
        isFetching={titlesIsFetching}
      />
      <AddressMemorialsTable addressId={addressId} />
      <SelectedAddressPreviousSalesWidget addressId={addressId} />
      <AddressNearbySalesWidget addressId={addressId} />
    </>
  )
}
