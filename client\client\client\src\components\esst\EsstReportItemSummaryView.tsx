import Link from '@components/anz/Link'
import { useEsstReportItemsFormDataRetrieveQuery } from '@store/services/esst/codegen'
import EsstReportItemErrors from './EsstReportItemErrors'
import EsstReportItemSummary from './EsstReportItemSummary'
import EsstReportItemSectionHeading from './EsstReportItemSectionHeading'
import NavBar from './NavBar'
import EsstCompleteForm from './EsstReportCompleteForm'
import LastSaved from './LastSaved'

type Props = {
  esstReportItemId: number
}

export default function EsstSummaryView({ esstReportItemId }: Props) {
  const { data } = useEsstReportItemsFormDataRetrieveQuery({
    pk: esstReportItemId,
  })

  const isGroupEsst = data?.reportGroupType === 'GROUP'

  return (
    <div>
      <EsstReportItemSectionHeading esstReportItemId={esstReportItemId} />
      <EsstReportItemErrors id={esstReportItemId} pathPrefix="../" />
      <EsstReportItemSummary esstReportItemId={esstReportItemId} />
      {!!data && (
        <NavBar
          actions={
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                rowGap: '.5rem',
              }}
            >
              {isGroupEsst && (
                <EsstCompleteForm
                  reportId={data.esstReportId}
                  reportGroupType={data.reportGroupType}
                  relativePath="../../../"
                />
              )}
              <LastSaved timestamp={data.updatedAt} />
            </div>
          }
        >
          {!isGroupEsst && (
            <div>
              <Link to="../../../" arrow>
                Continue
              </Link>
            </div>
          )}
        </NavBar>
      )}
    </div>
  )
}
