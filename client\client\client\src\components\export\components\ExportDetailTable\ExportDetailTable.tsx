import type React from 'react'
import { ExportDetailTableItem } from './ExportDetailTableItem'

interface ExportDetailTableProps extends React.HTMLAttributes<HTMLDivElement> {
  data?: Array<{
    label: string | React.ReactNode | React.ReactNode[] | boolean | undefined
    value?: string | React.ReactNode | React.ReactNode[] | boolean | undefined
    secondaryLabel?: string
    secondaryValue?:
      | string
      | React.ReactNode
      | React.ReactNode[]
      | boolean
      | undefined
    tertiaryValue?: string
    key?: string
  }>
  totalLabel?: string
}

const ExportDetailTable = (props: ExportDetailTableProps) => {
  const { data, totalLabel, ...divProps } = props
  return (
    <div className="ExportDetailTable" {...divProps}>
      {data?.map((item) => {
        return (
          <ExportDetailTableItem
            key={item?.key ?? item?.label?.toString()}
            value={item?.value}
            secondaryValue={item.secondaryValue}
            tertiaryValue={item.tertiaryValue}
            label={item?.label}
          />
        )
      })}
    </div>
  )
}

export default ExportDetailTable
