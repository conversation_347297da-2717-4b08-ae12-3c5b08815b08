import { LoadingOutlined } from '@ant-design/icons'
import { Button, Form, type FormProps, Space } from 'antd'
import React from 'react'
import ReviewStatusFormItems from './ReviewStatusFormItems'

interface Props extends FormProps {
  prefix: string
  loading?: boolean
}

const FormReviewStatus = ({ children, loading, prefix, ...props }: Props) => {
  const [form] = Form.useForm()

  return (
    <Form {...props} layout="vertical" form={form}>
      <ReviewStatusFormItems />
      {children}
      <Form.Item shouldUpdate>
        {() => {
          const disabled = !form.isFieldsTouched()
          return (
            <Space>
              <Button disabled={disabled} onClick={() => form.resetFields()}>
                Reset
              </Button>
              <Button type="primary" htmlType="submit" disabled={disabled}>
                Save
              </Button>
              {loading && <LoadingOutlined />}
            </Space>
          )
        }}
      </Form.Item>
    </Form>
  )
}

export default FormReviewStatus
