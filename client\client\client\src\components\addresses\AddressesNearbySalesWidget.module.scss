.container {
  --row-height: 55px; // row with two lines of text
  --table-rows: 15;
  // Hard-coding these values for now: table contents + pagination controls
  --table-min: calc(var(--row-height) * var(--table-rows) + 32px + 16px);
}

.heading {
  display: grid;
  grid-template-columns: auto 1fr 1em;
  align-items: baseline;
  column-gap: 30px;
  margin-bottom: 16px;

  > h2 {
    color: var(--primary);
    font-size: 15px;
    font-family: Gotham;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  > :last-child {
    align-self: flex-start;
  }
}

.content {
  min-height: var(--table-min);
}
