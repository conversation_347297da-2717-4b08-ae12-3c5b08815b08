import { Col, Form, Radio, Row, Select, Checkbox } from 'antd'
import FormItems from './FormItems'
import { getFieldName, getLabelForField, getTooltipForField } from './helpers'
import {
  COLUMN_PROPS,
  DISCLOSURE_OPTIONS,
  REQUIRES_REPORTING_PERIOD,
  ROW_PROPS,
  CCRA_CORPORATE_FORM_FIELD,
} from './const'
import ReportingPeriod from './ReportingPeriod'
import type { CcraFormItem } from './types'

const CustomerCompliance = () => {
  const form = Form.useFormInstance()
  const disclosuresValue: string = Form.useWatch(
    getFieldName('disclosures'),
    form
  )

  const complianceFormItems: CcraFormItem[] = [
    {
      name: getFieldName('disclosures'),
      label: getLabelForField('disclosures'),
      tooltip: getTooltipForField('disclosures'),
      children: (
        <Select
          style={{ width: 300 }}
          placeholder="Select a value..."
          options={DISCLOSURE_OPTIONS}
        />
      ),
    },
    {
      name: getFieldName('reportingPeriod'),
      label: getLabelForField('reportingPeriod'),
      tooltip: getTooltipForField('reportingPeriod'),
      children: (
        <ReportingPeriod
          disabled={!REQUIRES_REPORTING_PERIOD.includes(disclosuresValue)}
        />
      ),
      required: REQUIRES_REPORTING_PERIOD.includes(disclosuresValue),
    },
    {
      name: getFieldName('participantOfEts'),
      label: getLabelForField('participantOfEts'),
      tooltip: getTooltipForField('participantOfEts'),
      children: (
        <Radio.Group buttonStyle="solid">
          <Radio.Button value={true}>Yes</Radio.Button>
          <Radio.Button value={false}>No</Radio.Button>
        </Radio.Group>
      ),
    },
    {
      name: getFieldName('isLenzCustomer'),
      label: getLabelForField('isLenzCustomer'),
      tooltip: getTooltipForField('isLenzCustomer'),
      children: (
        <Radio.Group buttonStyle="solid">
          <Radio.Button value={true}>Yes</Radio.Button>
          <Radio.Button value={false}>No</Radio.Button>
        </Radio.Group>
      ),
      businessUnit: CCRA_CORPORATE_FORM_FIELD,
    },
  ]

  return (
    <Row {...ROW_PROPS}>
      <Col {...COLUMN_PROPS}>
        <FormItems items={complianceFormItems} />
      </Col>
    </Row>
  )
}

export default CustomerCompliance
