.container {
  --border-width: 2px;

  display: grid;
  grid-template-columns: var(--icon-width) 1fr;
  align-items: baseline;
  gap: var(--space-2);
  padding: 0 var(--menu-inset);
  color: var(--color-interactive);
}

.collapsed.active {
  box-shadow: inset var(--border-width) 0 0 0 currentColor;
}

.active {
  color: var(--color-primary);
}

.disabled {
  color: var(--color-grey-4);
  user-select: none;
  cursor: not-allowed;
}

.text {
  border-bottom: var(--border-width) solid transparent;
}

.container:hover:not(.disabled) .text,
.active .text {
  border-bottom-color: currentColor;
}

.collapsed .text {
  display: none;
}
