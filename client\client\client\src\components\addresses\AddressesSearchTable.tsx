import { InfoCircleOutlined, SearchOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Button, Table } from 'antd'
import React, { useCallback, useMemo } from 'react'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { OwnersLabel } from '@components/generic'
import { useSelector } from '@store'
import { setFilterValue } from '@store/ui/actions'
import { getFilterState } from '@store/ui/selectors'
import type { AddressFeature } from '@models/address/AddressFeatureCollection'
// import { formatDescription } from '../sales/dashboard/SelectedSaleDetails';
import { formatArea } from '@util'
import type { PaginatedResponse } from '../../models/generic/PaginatedResponse'
import { Widget } from '../generic/Widget'

// import { formatOwners } from '@components/map/components/ViewportTitleGeoJSON/ViewportTitleGeoJSON';

interface AddressesSearchTableProps {
  addresses?: PaginatedResponse<AddressFeature[]>
  isLoading: boolean
}

export const AddressesSearchTable = ({
  addresses,
  isLoading,
}: AddressesSearchTableProps) => {
  const navigate = useNavigate()

  const filterState = useSelector((state) =>
    getFilterState(state, 'addressDashboard')
  )

  const dispatch = useDispatch()

  const filterDispatch = useCallback(
    (payload: { value: unknown }) => {
      dispatch(
        setFilterValue({
          pageName: 'addressDashboard',
          filterKey: 'page',
          filterValue: payload?.value,
        })
      )
    },
    [dispatch]
  )

  const elem = useMemo(() => {
    const columns = [
      {
        dataIndex: ['properties', 'fullAddress'],
        title: 'Details',
        render: (text: string, record: AddressFeature) => {
          return (
            <button
              type="button"
              className="action-text"
              onClick={() => navigate(`/address/${record?.id?.toString()}/`)}
            >
              {text}
            </button>
          )
        },
      },
      {
        dataIndex: ['properties', 'cv'],
        title: 'CV',
        render: (text: string) =>
          text ? Number(text).toLocaleString('en-NZ') : text,
      },
      {
        dataIndex: ['properties', 'owners'],
        title: 'Owners',
        render: (text: string) => <OwnersLabel owners={text} />,
      },
      {
        dataIndex: ['properties', 'mortgagee'],
        title: 'Mortgagee',
      },
      {
        dataIndex: ['properties', 'surveyArea'],
        title: 'Area',
        render: (text: string) => formatArea(Number(text) / 1e4, 'ha'),
      },
    ]
    return (
      <>
        <Widget title="Search Results" icon={<SearchOutlined />}>
          {addresses && addresses?.count < 15 ? (
            <Alert
              className="agrigis-alert"
              message="Missing Results?"
              showIcon={true}
              icon={<InfoCircleOutlined />}
              description={
                <>
                  Results may be limited based on your current filter or
                  dashboard settings, check the following to increase search
                  results:
                  <ol>
                    <li>
                      Remove filters such as TLA name, owner name, address, etc.
                    </li>
                    <li>
                      Disable interactive mode. By default, interactive mode
                      will only include results within the viewable map.
                    </li>
                    <li>
                      Try alternative street names that the property may be
                      located under.
                    </li>
                  </ol>
                </>
              }
            />
          ) : (
            <></>
          )}
          <div className="agrigis-table">
            <Table
              rowKey={(row) => row.id?.toString() || ''}
              size="small"
              columns={columns}
              rowClassName={(row) => {
                return `${
                  row?.properties?.tlaName === undefined ? 'linz' : 'dvr'
                } not-selected`
              }}
              pagination={false}
              loading={isLoading}
              dataSource={addresses?.results || []}
            />
            <div className="agrigis-table-pagination">
              <Button
                onClick={() => {
                  filterDispatch({
                    value: filterState?.page - 1,
                  })
                }}
                disabled={!addresses?.previous}
              >
                Previous
              </Button>
              <div className="agrigis-table-pagination-current-page">
                {addresses?.currentPage}
              </div>
              <Button
                onClick={() => {
                  filterDispatch({
                    value: filterState?.page + 1,
                  })
                }}
                disabled={!addresses?.next}
              >
                Next
              </Button>
            </div>
          </div>
        </Widget>
      </>
    )
  }, [addresses, navigate, filterState?.page, isLoading, filterDispatch])

  return elem
}
