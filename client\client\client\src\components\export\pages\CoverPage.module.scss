.ExportCoverPageContent {
  text-align: right;
  margin-bottom: 0.5cm;
}

.ExportCoverPageDate {
  margin-bottom: 2cm;
  font-size: 20px;
  font-weight: 300;
}

.ExportCoverPageTitle {
  margin-bottom: 1cm;
  letter-spacing: 10%;
  font-weight: 800;
  font-size: 32px;
  font-family: Gotham;
  text-transform: uppercase;
}

.ExportCoverPageContent {
  font-family: MyriadPro;
  font-weight: 100;
  margin-bottom: 1cm;
  font-weight: 100;
  font-size: 32px;
  text-transform: capitalize;
}

.ExportCoverPageWrapper {
  // background-image: url('../../../../img/anz-cover-corner-logo.png');
  background-repeat: no-repeat;
  background-position: 100% 100%;
  padding: 5mm 15mm 15mm 5mm;
  height: calc(var(--export-a4-height) + 93mm);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-self: center;
  align-items: flex-end;
  flex-grow: 1;
}

.ExportCoverPageClassification {
  margin-top: 1cm;
  font-size: 18px;
  font-weight: 100;
  text-transform: capitalize;
}
