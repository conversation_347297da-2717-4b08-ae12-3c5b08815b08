import { useCustomerSearchListQuery } from '@store/services/sdk'

type CustomerSearchListParameters = Parameters<
  typeof useCustomerSearchListQuery
>

function useCustomerSearchResults(
  args: CustomerSearchListParameters[0],
  options?: Omit<CustomerSearchListParameters[1], 'selectFromResult'>
) {
  return useCustomerSearchListQuery(args, {
    ...options,
    selectFromResult: (result) => ({
      ...result,
      results: result.data?.results || [],
    }),
  })
}

export default useCustomerSearchResults
