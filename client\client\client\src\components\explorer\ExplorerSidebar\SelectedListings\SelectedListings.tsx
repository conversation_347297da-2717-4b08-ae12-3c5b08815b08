import { FullscreenOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { Button, Tooltip } from 'antd'
import classNames from 'classnames'
import type L from 'leaflet'
import type React from 'react'
import { useState } from 'react'
import { useLeaflet } from 'react-leaflet'
import SelectFromTla from '../SelectFromTla'
import styles from '../SelectedAddresses/SelectedAddresses.module.scss'
import SelectedListingsTable from '../SelectedListingsTable'
import SelectedTabButtons from '../SelectedTabButtons'

interface SelectedSalesAndListingsProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
  listingIds?: string[]
}

const SelectedListings = ({
  listingIds,
  ...divProps
}: SelectedSalesAndListingsProps) => {
  const { map } = useLeaflet()

  const [selectionBounds, setSelectionBounds] = useState<L.LatLngBounds>()

  return (
    <div
      className={classNames('SelectedAddresses', styles.container)}
      {...divProps}
    >
      <SelectedTabButtons layer="listing">
        <SelectFromTla />
        <Button
          icon={<FullscreenOutlined />}
          size="small"
          onClick={() => {
            if (!(map && selectionBounds)) return
            map.fitBounds(selectionBounds, { padding: [10, 10] })
          }}
        >
          View Selection
        </Button>
        <Tooltip title="Beta! Not yet accounting for filtering options.">
          <QuestionCircleOutlined />
        </Tooltip>
      </SelectedTabButtons>
      <SelectedListingsTable setBounds={setSelectionBounds} />
    </div>
  )
}

export default SelectedListings
