import classNames from 'classnames'
import type React from 'react'
import styles from './FormItemGroupContainer.module.scss'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

function FormItemGroupContainer({ children, className, ...props }: Props) {
  return (
    <div className={classNames(styles.container, className)} {...props}>
      {children}
    </div>
  )
}

export default FormItemGroupContainer
