import React from 'react'
import { TitleApportionmentTable } from '@components/assets/TitleApportionmentTable'
import { ExportPage, ExportSection } from '@components/export/components'
import type { Valuation } from '@types'

interface ExportMortgageApportionmentTablePageProps {
  valuation: Valuation
}

export const ExportMortgageApportionmentTablePage = ({
  valuation,
}: ExportMortgageApportionmentTablePageProps) => {
  return (
    <ExportPage className="ExportLandBestUseSummaryPage">
      <ExportSection title={valuation.fullAddress}>
        <TitleApportionmentTable
          valuationId={
            valuation.rvrValuation?.internalValuationId?.toString() ??
            valuation.valuationId
          }
        />
      </ExportSection>
    </ExportPage>
  )
}
