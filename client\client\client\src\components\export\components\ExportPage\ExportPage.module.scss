@media screen {
  .ExportPage {
    width: calc(var(--export-a4-width) + 60mm);
    height: calc(var(--export-a4-height) + 80mm);
    margin: 0 auto 0.5cm auto;
    padding: 40mm;
    box-shadow: 0 0 0.5cm rgba(0, 0, 0, 0.5);
    background: white;

    &[data-variant='map'] {
      display: flex;
      flex-direction: column;
      align-content: flex-start;
    }

    overflow-y: hidden;
  }
}

@media print {
  .ExportPage {
    margin: 0;
    padding: 25mm;
    width: var(--a4-width);
    height: var(--a4-height);

    &[data-variant='legal-disclaimers'] {
      padding: 25mm;
      page-break-after: always;
    }

    &[data-variant='form'] {
      padding: 25mm 12.5mm;
    }

    &[data-variant='cover'] {
      height: 100%;
      width: 100%;
      margin: 0;
      padding: 0;
    }
  }
}

.ExportPage {
  font-size: var(--export-primary-font-size);
  font-family: MyriadPro;
  font-weight: 100;
  page-break-inside: avoid;

  &[data-variant='cover'] {
    background-image: var(--anz-gradient);
    display: flex;
    justify-content: flex-end;
    align-self: center;
    padding: 0;
  }

  &[data-variant='legal-disclaimers'] {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .title {
    font-family: Gotham;
    font-weight: bold;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: var(--export-blue);
    font-size: 24px;
    border-bottom: 1px solid var(--export-blue);
    margin-bottom: 0.5cm;
    padding-bottom: 0.5cm;
  }

  .subtitle {
    font-family: MyriadPro;
    font-weight: 100;
    font-size: 20px;
    color: var(--export-blue);
    margin-bottom: 0.5cm;
  }

  .exportDisclaimerText {
    font-size: var(--export-primary-font-size);
    margin-bottom: 0.5cm;
    color: #444;
  }
}

:global(.ExportRVRPage) .ExportPage {
  padding: 20mm;
}
