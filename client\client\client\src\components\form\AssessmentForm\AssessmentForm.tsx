import { LoadingOutlined } from '@ant-design/icons'
import { Button, Form, type FormProps, Space } from 'antd'
import React from 'react'
import AssessmentFormItems from './AssessmentFormItems'

interface Props extends FormProps {
  dirty?: boolean
  path?: string | string[]
  initialValue?: {
    status?: 0 | 1 | 2
    comments?: string
  } // value for the assessment object
  loading?: boolean
  status?: boolean
}

const FormReviewStatus = ({
  children,
  dirty,
  loading,
  initialValue,
  status,
  path,
  form,
  ...props
}: Props) => {
  if (!form) return null
  return (
    <Form layout="vertical" form={form} {...props}>
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <AssessmentFormItems
          initialValue={initialValue}
          path={path}
          status={status}
        />
        {children}
        <Form.Item noStyle shouldUpdate>
          {() => {
            const disabled = !dirty && !form.isFieldsTouched()
            return (
              <Space>
                <Button disabled={disabled} onClick={() => form.resetFields()}>
                  Reset
                </Button>
                <Button type="primary" htmlType="submit" disabled={disabled}>
                  Save
                </Button>
                {loading && <LoadingOutlined />}
              </Space>
            )
          }}
        </Form.Item>
      </Space>
    </Form>
  )
}

export default FormReviewStatus
