import { BorderOutlined } from '@ant-design/icons'
import { Button, Popover } from 'antd'
import type { ButtonProps } from 'antd/es/button'
import { useEffect, useState } from 'react'
import { type ColorResult, SketchPicker } from 'react-color'

interface Props extends Omit<ButtonProps, 'onChange'> {
  value?: string
  onChange?: (value: string) => void
}

const OPTIONS = [
  '#004167',
  '#007DBA',
  '#C6DFEA',
  '#DF7A00',
  '#FDC82F',
  '#FF4D4F',
  '#52C41A',
  '#CCCCCC',
  'transparent',
]

const DEFAULT = '#CCCCCC'

const ColorInput = ({ value, onChange, ...props }: Props) => {
  const [color, setColor] = useState(DEFAULT)
  // const debounced = useDebounce(color, 500)
  // const [updateStep] = useLossModelStepPartialUpdateMutation()
  //
  useEffect(() => {
    setColor(value || DEFAULT)
  }, [value])

  const handleChange = (color: ColorResult) => {
    setColor(color.hex)
  }

  const handleChangeComplete = (color: ColorResult) => {
    onChange?.(color.hex)
  }

  return (
    <Popover
      content={
        <SketchPicker
          onChangeComplete={handleChangeComplete}
          onChange={handleChange}
          color={color}
          presetColors={OPTIONS}
        />
      }
    >
      <Button
        icon={<BorderOutlined style={{ background: value }} />}
        {...props}
      />
    </Popover>
  )
}

export default ColorInput
