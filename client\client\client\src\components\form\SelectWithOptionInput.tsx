import { PlusOutlined } from '@ant-design/icons'
import { Button, Divider, Input, Select, Space } from 'antd'
import { type Dispatch, type SetStateAction, useState } from 'react'

interface DropdownRenderProps {
  customOption: string
  onCustomOptionChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  addOption: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const DropdownRender = ({
  customOption,
  onCustomOptionChange,
  addOption,
}: DropdownRenderProps) => (
  <>
    <Divider style={{ margin: '8px 0' }} />
    <Space style={{ padding: '0 8px 4px' }}>
      <Input
        placeholder="Other"
        value={customOption}
        onChange={onCustomOptionChange}
      />
      <Button type="text" icon={<PlusOutlined />} onClick={addOption} />
    </Space>
  </>
)

export const SelectWithOptionInput = ({
  options,
  setOptions,
  ...selectProps
}: {
  options: { value: string; label: string }[]
  setOptions: Dispatch<SetStateAction<{ value: string; label: string }[]>>
}) => {
  const [customOption, setCustomOption] = useState('')
  const onCustomOptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setCustomOption(event.target.value)
  }

  const addOption = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!customOption) return
    e.preventDefault()
    setOptions([...options, { label: customOption, value: customOption }])
    setCustomOption('')
  }
  return (
    <Select
      {...selectProps}
      options={options}
      style={{ width: '130px' }}
      placeholder="Select option"
      dropdownRender={(menu) => (
        <>
          {menu}
          <DropdownRender
            customOption={customOption}
            onCustomOptionChange={onCustomOptionChange}
            addOption={addOption}
          />
        </>
      )}
    />
  )
}
