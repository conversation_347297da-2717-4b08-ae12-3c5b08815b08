import type React from 'react'
import { memo, useMemo, useState } from 'react'
import type { FieldNode } from '@store/services/esst/codegen'
import FieldContainer from './FieldContainer'
import { RadioContainer, RadioLabel } from './RadioGroup'
import type { FieldValue } from './util'

export type CheckboxGroupProps = {
  field: FieldNode
  value?: FieldValue
  onChange?: (value: FieldValue | undefined) => void
  children?: React.ReactNode
}

// TODO:
// - Retain field order
export function useSelectedValues(selectedValue: FieldValue | undefined) {
  return new Set(selectedValue?.value?.split(', ').filter((v) => !!v) || [])
}

export function useSelectedDescendants(
  field: FieldNode,
  selectedValues: Set<string>
) {
  const selectedDescendants = useMemo(
    () => field.descendants.filter((d) => selectedValues.has(d.value)),
    [field.descendants, selectedValues]
  )

  return selectedDescendants
}

function CheckboxGroup({
  field,
  value: selectedValue,
  onChange,
  children,
}: CheckboxGroupProps) {
  const [initialValue] = useState(selectedValue)

  const selectedValues = useSelectedValues(selectedValue)

  return (
    <>
      <FieldContainer
        title={field.description}
        message={field.metadata?.message}
        guidance={field.metadata?.guidance}
        tagName="fieldset"
      >
        <RadioContainer>
          {field.descendants.map((descendant) => (
            <RadioLabel
              key={descendant.id}
              title={descendant.description || descendant.value}
            >
              <input
                type="checkbox"
                name={field.fieldKey}
                checked={selectedValues?.has(descendant.value)}
                defaultValue={undefined}
                value={descendant.value}
                onChange={(e) => {
                  const updatedValues = new Set(selectedValues)

                  if (e.target.checked) {
                    if (descendant.value === 'None') {
                      updatedValues.clear()
                    } else {
                      updatedValues.delete('None')
                    }
                    updatedValues.add(descendant.value)
                  } else {
                    updatedValues.delete(descendant.value)
                  }

                  const newValues = updatedValues.size
                    ? {
                        id: initialValue?.id || Date.now(),
                        fieldNodeId: field.id,
                        fieldKey: field.fieldKey,
                        value: Array.from(updatedValues).join(', '),
                      }
                    : undefined

                  onChange?.(newValues)
                }}
              />
            </RadioLabel>
          ))}
        </RadioContainer>
        {children}
      </FieldContainer>
    </>
  )
}

export default memo(CheckboxGroup)
