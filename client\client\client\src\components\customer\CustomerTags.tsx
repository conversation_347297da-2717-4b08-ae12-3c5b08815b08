import type { Customer } from '@store/services/sdk'
import classNames from 'classnames'
import React, { type Key } from 'react'
import { Link, type LinkProps } from 'react-router-dom'
import styles from './CustomerTags.module.css'
import font from '@styles/font.module.css'
import util from '@styles/util.module.css'

type CustomerTag = Pick<
  Customer,
  'entityName' | 'anzsic' | 'customerId' | 'customerNumber' | 'customerSetCode'
> &
  Omit<LinkProps, 'to'> & {
    key?: Key
    size?: 's' | 'm'
  }

export const CustomerTag = ({
  entityName,
  anzsic,
  customerId,
  customerNumber,
  customerSetCode,
  className,
  size = 's',
  ...props
}: CustomerTag) => (
  <Link
    to={`/customer/${customerId}`}
    className={classNames(
      'CustomerTag',
      font.anchor,
      styles.container,
      className,
      {
        [font.bodySmall]: size === 's',
        [font.body]: size === 'm',
      }
    )}
    {...props}
  >
    <dt className={font.anchor}>{entityName}</dt>
    <dd>
      <span title="Customer Number" className={util.primary}>
        {customerNumber}
      </span>
      <span title="ANZSIC" className={util.muted}>
        {anzsic}
      </span>
      <span title="Set Code" className={util.muted}>
        {customerSetCode}
      </span>
    </dd>
  </Link>
)

const CustomerTags = ({ items = [] }: { items: CustomerTag[] }) => {
  return <dl>{items.map(CustomerTag)}</dl>
}

export default CustomerTags
