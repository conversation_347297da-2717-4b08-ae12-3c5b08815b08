import React, { useMemo } from 'react'

interface ExportLegendItemProps {
  label: string
  fillColor?: string
  borderColor?: string
  weight?: number
}

const ExportLegendItem = (props: ExportLegendItemProps) => {
  const styles = useMemo(() => {
    return {
      backgroundColor: props?.fillColor ?? 'transparent',
      outline: `${props?.weight ?? 2}px solid ${props?.borderColor ?? 'grey'}`,
    }
  }, [props])

  return (
    <div className="ExportLegendItem">
      <div className="export-legend-icon" style={styles} />
      <div className="export-legend-label">{props?.label}</div>
    </div>
  )
}

export default ExportLegendItem
