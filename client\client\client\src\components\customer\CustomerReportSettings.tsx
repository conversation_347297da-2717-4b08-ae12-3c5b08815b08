import { Card, Cascader, type FormItemProps, Input, Select } from 'antd'
import { Form } from 'antd'
import { useCallback } from 'react'
import type { ReactNode } from 'react'
import { useDispatch } from 'react-redux'
import { useEffectOnce } from 'react-use'
import { useSelector } from '@store'
import {
  type CustomerReportState,
  actions,
  getCustomerReportStateById,
} from '@store/features/customer'
import CustomerReportTemplatesCard from './CustomerReportTemplatesCard'
import {
  DENOMINATORS,
  MEASURES,
  SELECTABLE_COMPONENTS,
  SINGLE_CUSTOMER_SEGMENT_OPTIONS,
} from './static'
import useRouteCustomer from './useRouteCustomer'

const ReportCascader = ({ ...props }) => (
  <Cascader
    options={SELECTABLE_COMPONENTS}
    style={{
      width: '100%',
    }}
    multiple
    maxTagCount={'responsive'}
    showSearch
    showCheckedStrategy={'SHOW_CHILD'}
    {...props}
  />
)

const ReportItem = ({
  children,
  ...props
}: FormItemProps & { name: keyof CustomerReportState }) => (
  <Form.Item {...props}>{children}</Form.Item>
)

const CustomerReportSettings = () => {
  const { data: customer, originalArgs } = useRouteCustomer()

  const segment = customer?.segment

  if (!(originalArgs && segment)) return null

  return (
    <>
      <Card title="Report Parameters">
        <CustomerReportForm customerId={originalArgs.pk}>
          <ReportItem label="Report Title" name="reportTitle">
            <Input />
          </ReportItem>
          <ReportItem label="Customer Name Override" name="customerName">
            <Input />
          </ReportItem>
          <ReportItem label="Chart Components" name="selectedComponents">
            <ReportCascader options={SELECTABLE_COMPONENTS} />
          </ReportItem>
          <ReportItem label="Benchmarking Measures" name="selectedMeasures">
            <ReportCascader options={MEASURES[customer?.segment ?? 'W']} />
          </ReportItem>
          <ReportItem label="Financials" name="selectedFinancials">
            <Select
              options={
                SINGLE_CUSTOMER_SEGMENT_OPTIONS[
                  segment as keyof typeof SINGLE_CUSTOMER_SEGMENT_OPTIONS
                ]
              }
              maxTagCount="responsive"
              mode="multiple"
            />
          </ReportItem>
          <ReportItem label="Scaling/Normalisation" name="denominator">
            <Select options={DENOMINATORS[customer.segment]} />
          </ReportItem>
        </CustomerReportForm>
      </Card>
      <CustomerReportTemplatesCard segment={customer?.segment} />
    </>
  )
  /* eslint-enable @typescript-eslint/no-non-null-assertion */
}

function CustomerReportForm({
  children,
  customerId,
}: {
  children: ReactNode
  customerId: number
}) {
  const [form] = Form.useForm()

  const dispatch = useDispatch()

  const savedValues = useSelector((state) =>
    getCustomerReportStateById(state, customerId)
  )

  useEffectOnce(() => {
    form.setFieldsValue(savedValues)
  })

  const setState = useCallback(
    (newState: Partial<CustomerReportState>) => {
      dispatch(
        actions.setReportState({
          ...newState,
          customerId: Number(customerId),
        })
      )
    },
    [dispatch, customerId]
  )

  return (
    <Form form={form} onValuesChange={setState}>
      {children}
    </Form>
  )
}

export default CustomerReportSettings
