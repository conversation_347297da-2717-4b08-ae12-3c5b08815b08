export default {
  id: 89,
  disclosures: 'None',
  reportingPeriod: null,
  participantOfEts: false,
  knowsEmissions: true,
  emissionsMeasurementPlan: null,
  emissionsMeasurementMethod: 'Fonterra',
  emissionsVerificationMethod: 'AsureQuality',
  periodStartEndDate: ['2022-09-12', '2024-09-12'],
  emissionsScope1: null,
  emissionsScope2: null,
  emissionsScope3: null,
  emissionsScope3SupplyChainAspects: null,
  kgms: '3.00',
  biologicalIntensity: '2.00',
  nonBiologicalIntensity: '2.00',
  methane: null,
  nitrogen: null,
  carbon: null,
  co2Mt: null,
  co2eKg: null,
  synlaitCo2eKgms: null,
  synlaitKgms: null,
  tradingGroupRevenueReportingPercentage: '2.00',
  topContributors: [
    {
      id: 22,
      ccraId: 89,
      contributor: 'custom yoza contributor',
      helpText: null,
      index: 1,
      isCustomContributor: true,
    },
    {
      id: 23,
      ccraId: 89,
      contributor: 'Agricultural processes - enteric',
      helpText: 'Methane (CH4) produced in the gut of animals',
      index: 2,
      isCustomContributor: false,
    },
    {
      id: 24,
      ccraId: 89,
      contributor: 'Agricultural processes - other',
      helpText:
        'Includes gases from urine and dung, effluent and fertiliser, and feed',
      index: 3,
      isCustomContributor: false,
    },
    {
      id: 25,
      ccraId: 89,
      contributor: 'shmiggity bigguty',
      helpText: null,
      index: 4,
      isCustomContributor: true,
    },
    {
      id: 26,
      ccraId: 89,
      contributor: 'Purchased electricity (Scope 2)',
      helpText: 'Electricity sourced from a network provider',
      index: 5,
      isCustomContributor: false,
    },
    {
      id: 27,
      ccraId: 89,
      contributor: 'Fuel use - plant/processes',
      helpText:
        'Where a company is using fossil fuel for heating/cooling or manufacturing\n                 processes eg. using natural gas / LPG / diesel / coal',
      index: 6,
      isCustomContributor: false,
    },
    {
      id: 28,
      ccraId: 89,
      contributor: 'Fuel use - vehicles',
      helpText: 'Fossil fuel use in vehicles typically petrol/ diesel /LPG',
      index: 7,
      isCustomContributor: false,
    },
  ],
  revenueOrOutput: [
    {
      id: 20,
      ccraId: 89,
      yearEnd: '2026',
      quantity: '3.00',
      unit: 'milkSolids',
      tag: 'fonterraKgms',
    },
    {
      id: 21,
      ccraId: 89,
      yearEnd: '2026',
      quantity: '4.00',
      unit: 'kgFibre',
      tag: null,
    },
  ],
  emissionsReductionPlan: 'Partly formed plan',
  emissionsReductionPlanTargetMeasureExplanation: null,
  transitionRisks: [
    {
      id: 6,
      ccraId: 89,
      risk: 'New technology',
      helpText: 'Technological improvements or innovations',
      measureTaken: '',
      materialToCustomer: 'Yes',
    },
    {
      id: 7,
      ccraId: 89,
      risk: 'Change in regulation',
      helpText:
        'Sudden regulatory change aimed at reducing actions that contribute to climate change or promoting adaption',
      measureTaken: 'measure',
      materialToCustomer: 'No',
    },
    {
      id: 8,
      ccraId: 89,
      risk: 'Legal action',
      helpText:
        'As the value of loss and damage from climate change grows litigation risk grows.\n        Examples include liability for climate change, failure of organizations to mitigate impacts,\n         failure to adapt to climate change, and the insufficiency of disclosure around material financial risks',
      measureTaken: '',
      materialToCustomer: 'Yes',
    },
    {
      id: 9,
      ccraId: 89,
      risk: 'Market change',
      helpText:
        'Shifts in supply and demand for certain commodities, products and services. This may be an opportunity for some customers or a risk for others',
      measureTaken: 'measure 2',
      materialToCustomer: 'No',
    },
    {
      id: 10,
      ccraId: 89,
      risk: 'Social impact',
      helpText:
        'Climate change may cause uneven impacts, disproportionality affecting communities and groups',
      measureTaken: '',
      materialToCustomer: 'N/A',
    },
  ],
  physicalRisks: [
    {
      id: 6,
      ccraId: 89,
      risk: 'Coastal flood & sea level rises',
      helpText:
        'Caused by storm surges which will be worsened by sea level rise',
      measureTaken: 'measure 3',
      materialToCustomer: 'N/A',
      isInsured: false,
    },
    {
      id: 7,
      ccraId: 89,
      risk: 'Extreme heat (incl. fire)',
      helpText:
        'Includes heat stress from consecutive days of very hot temperatures that will impact people, animals and plants.\n        It also increases risk of wildfires either of which may damage assets or impact operations/production',
      measureTaken: 'measure 4',
      materialToCustomer: 'N/A',
      isInsured: true,
    },
    {
      id: 8,
      ccraId: 89,
      risk: 'Extreme weather (incl. inland flood)',
      helpText: 'Such as storms and cyclones cause flooding and wind damage',
      measureTaken: '',
      materialToCustomer: 'Unsure',
      isInsured: true,
    },
    {
      id: 9,
      ccraId: 89,
      risk: 'Chronic change in climatic conditions (incl. drought)',
      helpText:
        'Refers to both the increasing volatility in the climate as the planet slowly warms and the general\n        trend for the customer in their particular geography. This may include increased severity/frequency\n        of droughts and loss of species/arrival of new pests and diseases',
      measureTaken: 'measure 5',
      materialToCustomer: 'Unsure',
      isInsured: true,
    },
    {
      id: 10,
      ccraId: 89,
      risk: 'Loss of natural resources',
      helpText:
        'Refers to loss or damage to ecosystems and environments including agricultural areas due to weather events',
      measureTaken: '',
      materialToCustomer: 'N/A',
      isInsured: false,
    },
  ],
  emissionsStrategyFurtherCommentary: null,
  hasCarbonCredits: false,
  carbonCreditsDetail: null,
  hasAssessedClimateTransitionRisks: true,
  plansToAssessClimateTransitionRisks: false,
  hasAssessedPhysicalRisks: true,
  plansToAssessPhysicalRisks: false,
  customerIds: [2895403],
  customerNames: 'ALLIOTT NZ LTD',
  emissionsTargets: [
    {
      id: 21,
      ccraId: 89,
      emissionsType: 'Scope 1 and 2',
      emissionsTargetYear: '2026-09-02T19:07:43.134000+12:00',
      emissionsTargetType: 'absoluteReduction',
      emissionsTargetPercentage: null,
      emissionsTargetAbsoluteFrom: '1.00',
      emissionsTargetAbsoluteTo: '1.00',
      emissionsTargetAbsoluteUnits: 'tCO2',
      emissionsTargetIntensityFrom: null,
      emissionsTargetIntensityTo: null,
      emissionsTargetIntensityUnits: null,
      emissionsTargetIntensityPerUnitOf: null,
      emissionsBaselineYear: '2025-09-02T19:07:50.700000+12:00',
      emissionsBaselineValue: '2.00',
      emissionsBaselineUnits: 'tCO2',
      emissionsBaselineTargetSetYear: '2026-09-02T19:07:54.783000+12:00',
    },
    {
      id: 22,
      ccraId: 89,
      emissionsType: 'Scope 2',
      emissionsTargetYear: '2026-09-02T19:08:06.329000+12:00',
      emissionsTargetType: 'percentageReduction',
      emissionsTargetPercentage: '4.00',
      emissionsTargetAbsoluteFrom: null,
      emissionsTargetAbsoluteTo: null,
      emissionsTargetAbsoluteUnits: null,
      emissionsTargetIntensityFrom: null,
      emissionsTargetIntensityTo: null,
      emissionsTargetIntensityUnits: null,
      emissionsTargetIntensityPerUnitOf: null,
      emissionsBaselineYear: '2020-09-02T19:08:03.869000+12:00',
      emissionsBaselineValue: '2.00',
      emissionsBaselineUnits: 'yozzas',
      emissionsBaselineTargetSetYear: '2020-09-02T19:08:26.033000+12:00',
    },
  ],
  createdDatetime: '2024-09-03T17:01:57.179289+12:00',
}
