import classNames from 'classnames'
import type React from 'react'
import type { ReactNode } from 'react'

interface ExportWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  children: ReactNode
}

const ExportWrapper = ({
  children,
  className,
  ...props
}: ExportWrapperProps) => {
  return (
    <div {...props} className={classNames('ExportWrapper', className)}>
      {children}
    </div>
  )
}

export default ExportWrapper
