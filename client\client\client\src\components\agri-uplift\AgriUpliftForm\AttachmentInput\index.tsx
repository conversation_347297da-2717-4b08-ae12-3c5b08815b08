import classNames from 'classnames'
import {
  type ChangeEvent,
  type HTMLAttributes,
  useCallback,
  useMemo,
} from 'react'
import type { CustomerEmissionsReportAttachment } from '@store/services/finance/codegen'
import { equalsProperty, insertIf, not } from '@util/helpers'
import styles from './index.module.css'
import Uploads, { type UploadedFile } from './Uploads'

// Receives and provides the field type ids so we don't have to
// reset fields elsewhere in the form

type CustomerEmissionsReportAttachmentInputFields = Pick<
  CustomerEmissionsReportAttachment,
  'attachmentTypeId' | 'reportTypeId'
>

type CustomerEmissionsReportAttachmentItem = (
  | CustomerEmissionsReportAttachment
  | { pk: number; attachment: File }
) &
  CustomerEmissionsReportAttachmentInputFields

type Props = Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> & {
  multiple?: boolean
  value?: CustomerEmissionsReportAttachmentItem[]
  onChange?: (value: CustomerEmissionsReportAttachmentItem[]) => void
} & CustomerEmissionsReportAttachmentInputFields

// Should abstract this to have an 'other values' prop or something,
// instead of attachmentTypeId
const AttachmentInput = ({
  value = [],
  onChange,
  multiple = true,
  attachmentTypeId,
  reportTypeId,
  className,
  ...props
}: Props) => {
  const uploads = useMemo(
    () =>
      value.filter(
        (item): item is CustomerEmissionsReportAttachment =>
          !(item.attachment instanceof File)
      ),
    [value]
  )

  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files || []
      const newValue: FileList | File[] = multiple ? files : [files[0]]
      const updatedValue = [
        ...insertIf(multiple, ...uploads),
        ...[...newValue].map((attachment) => ({
          pk: Date.now(),
          attachment,
          attachmentTypeId,
          reportTypeId,
        })),
      ]
      onChange?.(updatedValue)
    },
    [reportTypeId, attachmentTypeId, multiple, onChange, uploads]
  )

  const handleRemove = useCallback(
    (file: UploadedFile) => {
      const updatedValue = value.filter(not(equalsProperty('pk', file.pk)))
      onChange?.(updatedValue)
    },
    [onChange, value]
  )

  return (
    <div {...props} className={classNames(styles.container, className)}>
      <input
        multiple={multiple}
        onChange={handleChange}
        {...props}
        type="file"
        className={styles.input}
      />
      {Boolean(uploads?.length) && (
        <Uploads uploads={uploads} onRemove={handleRemove} />
      )}
    </div>
  )
}

export const AttachmentTypeInput = ({
  value = [],
  onChange,
  ...props
}: Props) => {
  const filteredValue = value
    .filter(equalsProperty('reportTypeId', props.reportTypeId))
    .filter(equalsProperty('attachmentTypeId', props.attachmentTypeId))

  const handleChange: typeof onChange = (newValue) => {
    const otherValues = value.filter(
      not(equalsProperty('attachmentTypeId', props.attachmentTypeId))
    )
    onChange?.(otherValues.concat(newValue))
  }

  return (
    <AttachmentInput value={filteredValue} onChange={handleChange} {...props} />
  )
}

export default AttachmentInput
