import { Form, Table } from 'antd'
import type React from 'react'
import { useEffect, useState } from 'react'
import { getFieldName } from '../../helpers'

interface RankedTransitionRiskItem {
  key: React.Key
  index: number
  text: string
  helpText?: string
  measure: string
}
const StaticTableFieldTransitionRisks = ({
  providedValues,
}: { providedValues: RankedTransitionRiskItem[] }) => {
  const form = Form.useFormInstance()
  const transitionRisksValue = Form.useWatch(
    getFieldName('transitionRisks'),
    form
  )

  const [data, setData] = useState<RankedTransitionRiskItem[]>([])

  useEffect(() => {
    if (transitionRisksValue) {
      setData(transitionRisksValue)
    }
  }, [transitionRisksValue])

  useEffect(() => {
    if (providedValues) {
      setData(providedValues)
    }
  }, [providedValues])

  const columns = [
    {
      title: 'Risks',
      dataIndex: 'risk',
      key: 'risk',
      render: (risk: string, _record: RankedTransitionRiskItem) => (
        <span>{risk}</span>
      ),
    },
    {
      title: 'Material to customer?',
      dataIndex: 'materialToCustomer',
      key: 'materialToCustomer',
      width: '150px',
      render: (
        materialToCustomer: string,
        _record: RankedTransitionRiskItem
      ) => <span>{materialToCustomer || '-'}</span>,
    },
    {
      title: 'Measures taken to mitigate, if any',
      dataIndex: 'measureTaken',
      key: 'measureTaken',
      render: (measureTaken: string, _record: RankedTransitionRiskItem) => (
        <span>{measureTaken || '-'}</span>
      ),
    },
  ]

  return (
    <Table
      dataSource={data}
      columns={columns}
      rowKey="index"
      pagination={false}
      size="small"
      style={{ marginBottom: 16 }}
    />
  )
}

export default StaticTableFieldTransitionRisks
