import { Form, Input } from 'antd'
import type { NamePath } from 'rc-field-form/es/interface'
import React, { memo, useMemo } from 'react'
import FormattedDateRangePicker from '@components/form/FormattedDateRangePicker'
import { getResourcePath } from './helpers'
import EmissionsReportTypeSelect from './EmissionsReportTypeSelect'
import EmissionsMetrics from './EmissionsMetrics'
import EmissionsAttachments from './EmissionsAttachments'
import styles from './Emissions.module.css'

type Props = {
  path: NamePath
  lockSource?: boolean
}

const Emissions = ({ path, lockSource }: Props) => {
  const getFieldPath = useMemo(() => getResourcePath(path), [path])

  return (
    <div className={styles.container}>
      <Form.Item name={getFieldPath('pk')} hidden>
        <Input />
      </Form.Item>
      <div className={styles.grid}>
        <Form.Item
          name={getFieldPath('reportType')}
          label="Eligibility Pathway"
          rules={[{ required: true }]}
          style={{ gridColumn: 'span 2' }}
        >
          <EmissionsReportTypeSelect disabled={lockSource} />
        </Form.Item>
        <Form.Item
          name={getFieldPath('reportingPeriod')}
          label="Reporting Period For Emissions Data"
          rules={[{ message: 'Reporting Period is required', required: true }]}
          style={{ gridColumn: 'span 2' }}
        >
          <FormattedDateRangePicker />
        </Form.Item>
        <Form.Item
          name={getFieldPath('reportType')}
          valuePropName="reportTypeId"
          noStyle
        >
          <EmissionsMetrics path={path} />
        </Form.Item>
      </div>
      <div className={styles.grid}>
        <Form.Item
          name={getFieldPath('reportType')}
          valuePropName="reportTypeId"
          noStyle
        >
          <EmissionsAttachments path={path} />
        </Form.Item>
      </div>
    </div>
  )
}

export default memo(Emissions)
