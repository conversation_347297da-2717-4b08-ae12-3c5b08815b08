.container {
  margin-top: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  :global(.ant-card-body) {
    height: 0;
    overflow-y: hidden;
    padding: 0;
    transition: height 200ms ease;
  }
}

.open {
  flex: 2;

  :global(.ant-card-body) {
    height: auto;
    flex-grow: 1;
    overflow-y: auto;

    @media (min-height: 800px) {
      height: auto;
    }
  }
}

.content {
  padding: 12px;
}

.details {
  columns: 2;
  column-gap: 16px;
}

.detail {
  margin-bottom: 1px;
  break-inside: avoid;

  dd {
    margin: 0;
  }
}
