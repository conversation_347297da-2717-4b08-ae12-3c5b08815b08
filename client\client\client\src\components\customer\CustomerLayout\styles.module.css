.container {
  --header-height: 50px;

  height: 100%;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: minmax(0, 1fr);
  align-items: stretch;
  overflow: hidden;
  border-top: 1px solid var(--color-border);
}

.inner {
  display: flex;
  flex-direction: column;
  container-type: inline-size;
}

.content {
  --columns: repeat(2, minmax(0, 1fr));

  flex: 1;
  padding: var(--space-2);
  overflow-y: auto;
  background: var(--color-background--alt-0);
}

.empty {
  height: 100%;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
