import { Skeleton } from 'antd'
import classNames from 'classnames'
import React, { type ReactNode } from 'react'

interface ExportSectionProps {
  className?: string
  title?: string
  children?: ReactNode | ReactNode[]
  flex?: boolean
  isFetching?: boolean
}

const ExportSection = (props: ExportSectionProps) => {
  if (props.isFetching) {
    return <Skeleton />
  }

  return (
    <div {...props} className={classNames('ExportSection', props.className)}>
      <div className="export-section-title">{props?.title}</div>
      <div className={`export-section-content ${props?.flex ? ' flex' : ''}`}>
        {props?.children}
      </div>
    </div>
  )
}

export default ExportSection
