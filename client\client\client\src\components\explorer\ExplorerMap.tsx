import classNames from 'classnames'
import type {
  LayersControlEvent,
  LeafletEvent,
  LeafletMouseEvent,
} from 'leaflet'
import { debounce } from 'lodash'
import React, { useCallback, useMemo } from 'react'
import { Map as LeafletMap, type MapProps } from 'react-leaflet'
import { useDispatch } from 'react-redux'
import { useMeasure } from 'react-use'
import { MapControl, MapLoading, MapScale } from '@components/map'
import Geolocate from '@components/map/Geolocate'
import { MemoizedMeasurementLayer as MeasurementLayer } from '@components/map/layers/MeasurementLayer'
import { type RootState, useSelector } from '@store'
import {
  type ExplorerBaseLayer,
  actions,
  getLoading,
} from '@store/features/explorer'
import { isMapInstance } from '@util/guards'
import { useDebounce } from '@util/useDebounce'
import ExplorerLayers from './ExplorerLayers'
import styles from './ExplorerMap.module.scss'
import ExplorerMapFeatureCount from './ExplorerMapFeatureCount'
import ExplorerMapState from './ExplorerMapState'
import ExplorerTileLayers from './ExplorerTileLayers'
import UnionLegend from './UnionLegend'
import { mapClassName } from './helpers'

type Props = MapProps

const selector = (state: RootState) => ({
  loading: getLoading(state),
  center: state.explorer.center,
  zoom: state.explorer.zoom,
})

const controlsClicked = (e: LeafletMouseEvent): boolean => {
  const controlElements = document.querySelectorAll('.leaflet-control')
  return [...controlElements].some((element) =>
    element.contains(e.originalEvent.target as Element)
  )
}

const ExplorerMap = ({ className, children, ...props }: Props) => {
  const dispatch = useDispatch()

  const { loading, center, zoom } = useSelector(selector)
  const isDrawing = useSelector((state) => state.explorer.isDrawing)

  const [measureRef, dimensions] = useMeasure<HTMLDivElement>()
  const debouncedDimensions = useDebounce(dimensions, 400)

  const handleBaseLayerChange = useCallback(
    ({ name }: LayersControlEvent) => {
      dispatch(actions.setBaseLayer(name as ExplorerBaseLayer))
    },
    [dispatch]
  )

  const handleClick = useCallback(
    (e: LeafletMouseEvent) => {
      const { containerPoint: position } = e
      // Can't stop bubbling on the components themselves because of the way they're added to the map
      if (controlsClicked(e) || isDrawing) return
      dispatch(actions.setMapMenuOpen({ position }))
    },
    [dispatch, isDrawing]
  )

  const handleZoom = useCallback(
    (e: LeafletEvent) => {
      const zoom = Number(e.target?._zoom)
      if (Number.isNaN(zoom)) return
      dispatch(actions.setZoom(zoom))
    },
    [dispatch]
  )

  const handleMove = useCallback(
    (e: LeafletEvent) => {
      const instance = e.target
      if (!isMapInstance(instance)) return
      const center = instance.getCenter()
      const bounds = instance.getBounds()
      const zoom = instance.getZoom()
      dispatch(actions.setBounds(bounds))
      dispatch(actions.setCenter(center))
      dispatch(actions.setZoom(zoom))
      dispatch(actions.setTitleMinSqM(undefined))
    },
    [dispatch]
  )

  const debouncedMoveHandler = useMemo(
    () => debounce(handleMove, 400),
    [handleMove]
  )

  return (
    <div
      ref={measureRef}
      className={classNames(mapClassName, styles.container)}
    >
      <LeafletMap
        center={center}
        // disabling this because it interferes with scale controls
        // could reimplement manually on the dblclick event
        doubleClickZoom={false}
        zoom={zoom}
        zoomDelta={0.5}
        zoomSnap={0.5}
        onclick={handleClick}
        onbaselayerchange={handleBaseLayerChange}
        onmove={debouncedMoveHandler}
        onzoom={handleZoom}
        {...props}
        zoomControl
        className={className}
        style={{ height: '100%' }}
      >
        <ExplorerMapState key={JSON.stringify(debouncedDimensions)} />
        <ExplorerLayers />
        <ExplorerTileLayers />
        <Geolocate />
        <MapControl position="bottomleft">
          <ExplorerMapFeatureCount />
          <MapLoading loading={loading} />
          <MapScale />
        </MapControl>
        <UnionLegend />
        <MeasurementLayer markerOutside terse />
        {children}
      </LeafletMap>
    </div>
  )
}

export default ExplorerMap
