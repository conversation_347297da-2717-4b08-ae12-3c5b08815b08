import type React from 'react'

interface ExportControlWidgetListItemProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
}

const ExportControlWidgetListItem = (
  props: ExportControlWidgetListItemProps
) => {
  const { children, ...divProps } = props
  return (
    <div className="ExportControlWidgetListItem" {...divProps}>
      {props?.children}
    </div>
  )
}

export default ExportControlWidgetListItem
