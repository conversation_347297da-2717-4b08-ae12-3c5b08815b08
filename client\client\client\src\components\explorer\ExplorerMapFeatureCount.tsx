import { FilterTwoTone } from '@ant-design/icons'
import { Tooltip } from 'antd'
import { useSelector } from '@store'
import { getExplorersFilterDirty } from '@store/ui/selectors'

const ExplorerMapFeatureCount = () => {
  const filtered = useSelector(getExplorersFilterDirty)

  return (
    <Tooltip
      placement="topRight"
      title="Filters are currently applied to the map."
      trigger={['click']}
    >
      <FilterTwoTone
        twoToneColor="#F36D00"
        style={{ fontSize: '1rem', ...(!filtered && { opacity: 0 }) }}
      />
    </Tooltip>
  )
}

export default ExplorerMapFeatureCount
