import { Line } from '@ant-design/charts'
import { CoffeeOutlined, DownloadOutlined } from '@ant-design/icons'
import {
  Button,
  DatePicker,
  Dropdown,
  Menu,
  Table,
  type TableProps,
} from 'antd'
import moment, { type Moment } from 'moment'
import React, { useEffect, useMemo, useState } from 'react'
import { Helmet } from 'react-helmet'
import { ButtonWidget, Layout } from '@components/generic'
import {
  type ByRegionAggregate,
  type ByUserAggregate,
  type PageSummary,
  type TimeSummary,
  type UserSummary,
  useAdminUsageRetrieveQuery,
  useAdminUsersListQuery,
} from '@store/services/sdk'
import { useSelectInput } from '../../store/hooks/useSelectInput'
import { toURL } from '../../util'
import { AdminGraph, type AdminGraphProps } from '../generic/AdminGraph'
import { Widget } from '../generic/Widget'
import styles from './AdminView.module.scss'
import UserSelect from './UserSelect'

const exportClick = (endpoint: string) => {
  window.open(toURL(`/api/export/${endpoint}/`), 'name')
}

const { RangePicker } = DatePicker

const aggregateDataSetByKey = <
  T extends {
    count: number
    date: string
    [key: string]: string | number | boolean
  },
>(
  dataset: T[],
  dataKey: keyof T
): { index: string; count: number; lastActive?: string }[] => {
  const summary: { [key: string]: number } = {}
  for (const row of dataset) {
    if (!summary[row[dataKey] as string]) {
      summary[row[dataKey] as string] = 0
    }
    summary[row[dataKey] as string] += row.count
  }
  return Object.keys(summary)
    .map((k) => {
      const dates = dataset
        .filter((x: T) => {
          return x[dataKey] === k
        })
        .map((x: T) => {
          return Number(new Date(x.date))
        })
      try {
        const lastActive = new Date(Math.max.apply(null, dates))
          .toISOString()
          .slice(0, 10)
        return {
          index: k,
          count: summary[k],
          lastActive,
        }
      } catch (_) {
        return {
          index: k,
          count: summary[k],
        }
      }
    })
    .sort((x) => x.count)
}

const aggregateDataSetByDate = <
  T extends {
    count: number
    date: string
    [key: string]: string | number | boolean
  },
>(
  dataset: T[],
  filter: (value: T) => boolean = () => true
): TimeSummary[] => {
  const dataMap: Record<string, number> = {}
  for (const data of dataset) {
    if (dataMap[data.date] === undefined) {
      dataMap[data.date] = 0
    }
    if (filter(data)) {
      dataMap[data.date] += data.count
    }
  }
  return Object.entries(dataMap).map(([date, count]) => ({ date, count }))
}

const AdminView = () => {
  const [dateRange, setDateRange] = useState<[Moment, Moment]>([
    moment().add(-30, 'days'),
    moment(),
  ])
  const [timePresetLabel, setTimePresetLabel] = useState<string>('30 Days')
  const [timeUnit, setTimeUnit] = useState<string>('week')

  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([])

  const {
    data = {
      valuationsCreatedByRegion: [],
      valuationsCompletedByRegion: [],
      valuationsCompletedByUser: [],
      valuationsCreatedByUser: [],
      valuationsCompletedYoy: [],
      users: [],
      time: [],
      rvrsCompletedByUser: [],
      rvrsCreatedByUser: [],
      pages: [],
      salesVettedByRegion: [],
      salesCreatedByRegion: [],
      salesCreatedByUser: [],
      valocitySalesCreatedByRegion: [],
      valocitySalesProcessedByRegion: [],
      valocitySalesProcessedByUser: [],
    },
    refetch,
  } = useAdminUsageRetrieveQuery({
    startDate: dateRange[0].toDate().toISOString(),
    endDate: dateRange[1].toDate().toISOString(),
    timeUnit,
    users: selectedUserIds,
  })

  useEffect(() => {
    if (dateRange) {
      refetch()
    }
  }, [dateRange, refetch])

  useEffect(() => {
    if (timeUnit) {
      refetch()
    }
  }, [timeUnit, refetch])

  const [selectUsageElem, selectedUsageValue] = useSelectInput(
    [
      { label: 'Usage Over Time', value: 'TIME' },
      { label: 'Top 10 Users', value: 'USERS' },
      { label: 'Top 10 Pages', value: 'PAGES' },
    ],
    'TIME'
  )

  const selecteUsagedGraph = useMemo(() => {
    switch (selectedUsageValue) {
      case 'TIME':
        return (
          <AdminGraph<TimeSummary>
            data={data?.time}
            dataKey="date"
            type="LINE"
          />
        )
      case 'USERS':
        return (
          <AdminGraph<UserSummary>
            data={data?.users}
            dataKey="username"
            type="BAR"
          />
        )
      case 'PAGES':
        return (
          <AdminGraph<PageSummary>
            data={data?.pages}
            dataKey="pageUrl"
            type="BAR"
          />
        )
      default:
        return <></>
    }
  }, [data, selectedUsageValue])

  const rvrsCompletedDatasource = aggregateDataSetByKey<ByUserAggregate>(
    data?.rvrsCompletedByUser,
    'user' as keyof ByUserAggregate
  )
  const rvrsCreatedDatasource = aggregateDataSetByKey<ByUserAggregate>(
    data?.rvrsCreatedByUser,
    'user' as keyof ByUserAggregate
  )
  const salesVettedDataSource = aggregateDataSetByKey<ByRegionAggregate>(
    data?.salesVettedByRegion,
    'region' as keyof ByRegionAggregate
  )
  const usersDataSource = aggregateDataSetByKey<UserSummary>(
    data?.users,
    'username' as keyof UserSummary
  )
  const pagesDataSource = aggregateDataSetByKey<PageSummary>(
    data?.pages,
    'pageUrl' as keyof PageSummary
  )

  const valocitySalesCreatedByRegion = aggregateDataSetByKey<ByRegionAggregate>(
    data?.valocitySalesCreatedByRegion,
    'region' as keyof ByRegionAggregate
  )

  return (
    <Layout className={styles.AdminView} data-testid="admin-page">
      <Helmet>
        <title>ESGIS Admin Dashboard</title>
      </Helmet>
      <h1>
        <CoffeeOutlined />
        ESGIS Admin Dashboard
      </h1>
      <div className={styles.filterButtons}>
        <ButtonWidget>
          <RangePicker
            onChange={(e) => setDateRange(e as [Moment, Moment])}
            value={dateRange}
          />
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item
                  onClick={() => {
                    setDateRange([moment().add(-30, 'days'), moment()])
                    setTimePresetLabel('30 Days')
                  }}
                >
                  30 Days
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setDateRange([moment().add(-90, 'days'), moment()])
                    setTimePresetLabel('90 Days')
                  }}
                >
                  90 Days
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setDateRange([moment().add(-6, 'months'), moment()])
                    setTimePresetLabel('6 Months')
                  }}
                >
                  6 Months
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setDateRange([moment().add(-1, 'years'), moment()])
                    setTimePresetLabel('1 Year')
                  }}
                >
                  1 Year
                </Menu.Item>
              </Menu>
            }
          >
            <Button>{timePresetLabel}</Button>
          </Dropdown>
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item onClick={() => setTimeUnit('day')}>Day</Menu.Item>
                <Menu.Item onClick={() => setTimeUnit('week')}>Week</Menu.Item>
                <Menu.Item onClick={() => setTimeUnit('month')}>
                  Month
                </Menu.Item>
              </Menu>
            }
          >
            <Button>
              {timeUnit.slice(0, 1).toUpperCase() + timeUnit.slice(1)}
            </Button>
          </Dropdown>
          <UserSelect value={selectedUserIds} onChange={setSelectedUserIds} />
        </ButtonWidget>
        <ButtonWidget>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              exportClick('sales')
            }}
          >
            Sales
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              exportClick('sale_titles')
            }}
          >
            Sale Titles
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              exportClick('valuations')
            }}
          >
            Valuations
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              exportClick('valuation_titles')
            }}
          >
            Valuation Titles
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              exportClick('users')
            }}
          >
            Users
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={() => {
              exportClick('events')
            }}
          >
            Events
          </Button>
        </ButtonWidget>
      </div>
      <div className={styles.paneContainer}>
        <div className={styles.pane} id="graph">
          <Widget
            title="Usage Statistics"
            extra={<ButtonWidget>{selectUsageElem}</ButtonWidget>}
          >
            {selecteUsagedGraph}
          </Widget>
          <AdminGraphByUserOrRegion
            title={'Valuations Created'}
            byRegion={data?.valuationsCreatedByRegion}
            byUser={data?.valuationsCreatedByUser}
            type="BAR"
          />
          <AdminGraphByUserOrRegion
            title={'Valuations Completed'}
            byRegion={data?.valuationsCompletedByRegion}
            byUser={data?.valuationsCompletedByUser}
            type="BAR"
          />
          <YearOnYearByUserGraphWidget
            title={'Valuations Completed'}
            yearOnYearByUserAggregate={data?.valuationsCompletedYoy}
          />
          <Widget title="RVRs Created by User">
            <AdminGraph<ByUserAggregate>
              data={data?.rvrsCreatedByUser}
              dataKey="user"
              type="BAR"
            />
          </Widget>
          <Widget title="RVRs Completed by User">
            <AdminGraph<ByUserAggregate>
              data={data?.rvrsCompletedByUser}
              dataKey="user"
              type="BAR"
            />
          </Widget>
          <AdminGraphByUserOrRegion
            title={'Sales Created'}
            byRegion={data?.salesCreatedByRegion}
            byUser={data?.salesCreatedByUser}
            type="BAR"
          />
          <Widget title={'Sales Vetted by Region'}>
            <AdminGraph<ByRegionAggregate>
              data={data?.salesVettedByRegion}
              dataKey="region"
              type="BAR"
            />
          </Widget>
          <Widget title="Valocity Sales Created By Region">
            <AdminGraph<ByRegionAggregate>
              data={data?.valocitySalesCreatedByRegion}
              dataKey="region"
              type="BAR"
            />
          </Widget>
          <AdminGraphByUserOrRegion
            title="Valocity Sales Processed"
            byRegion={data?.valocitySalesProcessedByRegion}
            byUser={data?.valocitySalesProcessedByUser}
            type={'BAR'}
          />
        </div>
        <div className={styles.pane} id="table">
          <Widget title="Users Usage Summary">
            <AdminTable
              data={usersDataSource}
              indexTitle="Username"
              additionalColumns={[
                {
                  dataIndex: 'lastActive',
                  title: 'Last Active',
                },
              ]}
            />
          </Widget>
          <Widget title="Pages Usage Summary">
            <AdminTable data={pagesDataSource} indexTitle="Page URL" />
          </Widget>
          <AdminTableByUserOrRegion
            title="Valuations Created"
            byUser={data?.valuationsCreatedByUser}
            byRegion={data?.valuationsCreatedByRegion}
          />
          <AdminTableByUserOrRegion
            title="Valuations Completed"
            byUser={data?.valuationsCompletedByUser}
            byRegion={data?.valuationsCompletedByRegion}
          />
          <Widget title="RVRs Created by User">
            <AdminTable data={rvrsCreatedDatasource} indexTitle="User" />
          </Widget>
          <Widget title="RVRs Completed by User">
            <AdminTable data={rvrsCompletedDatasource} indexTitle="User" />
          </Widget>
          <AdminTableByUserOrRegion
            title="Sales Created"
            byUser={data?.salesCreatedByUser}
            byRegion={data?.salesCreatedByRegion}
          />
          <Widget title="Sales Vetted by Region">
            <AdminTable data={salesVettedDataSource} indexTitle="Region" />
          </Widget>
          <AdminTableByUserOrRegion
            title="Valocity Sales Processed"
            byUser={data?.valocitySalesProcessedByUser}
            byRegion={data?.valocitySalesProcessedByRegion}
          />
          <Widget title="Valocity Sales Created by Region">
            <AdminTable
              data={valocitySalesCreatedByRegion}
              indexTitle="Region"
            />
          </Widget>
        </div>
      </div>
    </Layout>
  )
}

export interface AdminGraphByUserOrRegionProps {
  title: string
  byRegion: ByRegionAggregate[]
  byUser: ByUserAggregate[]
  type: AdminGraphProps<unknown>['type']
}

const AdminGraphByUserOrRegion = ({
  title,
  byRegion = [],
  byUser = [],
  type,
}: AdminGraphByUserOrRegionProps) => {
  const [selectElem, selectValue] = useSelectInput(
    [
      { label: 'User', value: 'USER' },
      { label: 'Region', value: 'REGION' },
    ],
    'REGION'
  )
  return (
    <Widget
      title={`${title} by ${selectValue?.toString()?.toLowerCase() ?? ''}`}
      extra={selectElem}
    >
      {selectValue === 'USER' ? (
        <AdminGraph<ByUserAggregate>
          data={byUser}
          dataKey={'user'}
          type={type}
        />
      ) : (
        <AdminGraph<ByRegionAggregate>
          data={byRegion}
          dataKey={'region'}
          type={type}
        />
      )}
    </Widget>
  )
}

export interface AdminTableByUserOrRegionProps {
  title: string
  byRegion: ByRegionAggregate[]
  byUser: ByUserAggregate[]
}

const AdminTableByUserOrRegion = ({
  title,
  byRegion = [],
  byUser = [],
}: AdminTableByUserOrRegionProps) => {
  const [selectElem, selectValue] = useSelectInput(
    [
      { label: 'User', value: 'USER' },
      { label: 'Region', value: 'REGION' },
    ],
    'REGION'
  )

  const byRegionData = useMemo(
    () =>
      aggregateDataSetByKey<ByRegionAggregate>(
        byRegion,
        'region' as keyof ByRegionAggregate
      ),
    [byRegion]
  )

  const byUserData = useMemo(
    () =>
      aggregateDataSetByKey<ByUserAggregate>(
        byUser,
        'user' as keyof ByUserAggregate
      ),
    [byUser]
  )

  return (
    <Widget
      title={`${title} by ${selectValue?.toString()?.toLowerCase() ?? ''}`}
      extra={selectElem}
    >
      {selectValue === 'USER' ? (
        <AdminTable data={byUserData} indexTitle={'user'} />
      ) : (
        <AdminTable data={byRegionData} indexTitle={'region'} />
      )}
    </Widget>
  )
}

type AdminTableData = { index: string; count: number }

interface AdminTableProps<T extends AdminTableData> {
  data: readonly T[]
  indexTitle: string
  additionalColumns?: TableProps<T>['columns']
}

const AdminTable = <T extends AdminTableData>({
  data = [],
  indexTitle,
  additionalColumns,
}: AdminTableProps<T>) => {
  return (
    <div className="agrigis-table">
      <Table
        dataSource={data}
        rowKey={(row) => row.index}
        size="small"
        columns={[
          {
            dataIndex: 'index',
            title: indexTitle,
          },
          {
            dataIndex: 'count',
            title: 'Count',
            sorter: (a, b) => a.count - b.count,
          },
          ...(additionalColumns ?? []),
        ]}
      />
    </div>
  )
}

const YearOnYearByUserGraphWidget = ({
  yearOnYearByUserAggregate,
  title,
}: {
  yearOnYearByUserAggregate: ByUserAggregate[]
  title: string
}) => {
  const { data: valuers = [] } = useAdminUsersListQuery(undefined, {
    selectFromResult: ({ data }) => {
      return {
        data: data?.filter((user) => user.isValuer),
      }
    },
  })

  const selectOptions = [
    { label: 'All', value: 'ALL' },
    ...valuers.map((user) => ({
      label: user.username,
      value: user.username,
    })),
  ]

  const [selectElem, selectValue] = useSelectInput(selectOptions, 'ALL')

  const data = useMemo(
    () =>
      aggregateDataSetByDate(yearOnYearByUserAggregate, (value) =>
        selectValue === 'ALL' ? true : value.user === selectValue
      ),
    [selectValue, yearOnYearByUserAggregate]
  )

  return (
    <Widget
      title={`${title} by ${selectValue?.toString()?.toLowerCase() ?? ''}`}
      extra={selectElem}
    >
      <YearOnYearLineGraph data={data} />
    </Widget>
  )
}

export const YearOnYearLineGraph = ({ data }: { data: TimeSummary[] }) => {
  const dataSource = useMemo(() => {
    const unsorted = data.map((value) => ({
      ...value,
      monthNumber: moment(value.date).month(),
      month: moment(value.date).format('MMMM'),
      year: moment(value.date).format('YYYY'),
    }))
    unsorted.sort((a, b) => Number(new Date(a.date)) - Number(new Date(b.date)))
    return unsorted
  }, [data])
  return (
    <Line
      data={dataSource}
      yField={'count'}
      xField={'month'}
      connectNulls={true}
      seriesField={'year'}
      color={['#aaaaaa', '#5c10e5']}
      legend={{ position: 'right' }}
    />
  )
}

export default AdminView
