import { DownOutlined, LoadingOutlined, UpOutlined } from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Button, Card, Empty, Skeleton } from 'antd'
import classNames from 'classnames'
import type React from 'react'
import { useState } from 'react'
import { useGetValuationTypesQuery } from '@store/services/assets'
import { useGetSaleQuery } from '@store/services/sale'
import { formatAddress } from '@util/labels'
import styles from './AddressDetailCard.module.scss'

interface Props {
  id: number | undefined
}

// add ability to add extra markers to title mode (e.g. sold)
// layer menu changes

const ExplorerSelectedSaleDetail = ({ id }: Props) => {
  const [open, setOpen] = useState(true)

  const { data: sale, isFetching } = useGetSaleQuery(
    id?.toString() ?? skipToken
  )

  const { data: valuationTypes } = useGetValuationTypesQuery()

  let title: React.ReactNode = 'No Property Selected'
  if (sale) title = formatAddress(sale.properties.fullAddress ?? 'Unknown')
  if (isFetching) title = <LoadingOutlined />

  return (
    <Card
      size="small"
      title={title}
      extra={
        <Button
          type="link"
          size="small"
          onClick={() => setOpen((v) => !v)}
          title={open ? 'Close' : 'Open'}
        >
          {open ? <DownOutlined /> : <UpOutlined />}
        </Button>
      }
      className={classNames(styles.container, {
        [styles.open]: open,
      })}
    >
      <div className={classNames(styles.content)}>
        {(sale && (
          <dl className={styles.details}>
            <div key={sale.id} className={styles.detail}>
              <dt>Best Use</dt>
              <dd>
                {sale?.properties?.highestAndBestUseType
                  ? valuationTypes?.[sale?.properties?.highestAndBestUseType]
                      ?.highestAndBestUse
                  : sale?.properties?.bestUse || '–'}
              </dd>
              <dt>Sale Date</dt>
              <dd>
                {sale?.properties?.saleDate?.toString().slice(0, 10) || '-'}
              </dd>
              <dt>Area (ha)</dt>
              <dd>{sale?.properties?.totalHa || '-'}</dd>
              <dt>AEP</dt>
              <dd>{sale?.properties?.averageEfficientProduction || '-'}</dd>
              <dt>FMV</dt>
              <dd>{sale?.properties?.grossSalesPrice || '-'}</dd>
            </div>
          </dl>
        )) ||
          (isFetching && <Skeleton />) || <Empty />}
      </div>
    </Card>
  )
}

export default ExplorerSelectedSaleDetail
