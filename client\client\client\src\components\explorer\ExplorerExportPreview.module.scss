.content {
  height: 100%;
  font-family: 'MyriadPro';
  font-size: 14px;

  h2 {
    margin-bottom: 1.5em;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary);
  }

  label {
    width: 100%;
    margin-bottom: 1.5em;
  }
}

.imageContainer {
  aspect-ratio: 1.4142 / 1;
  position: relative;
  margin-bottom: 1.5em;
  background-color: lightgray;
}

.imageControls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: flex-start;
  padding: 8px;

  > :first-child:not(:only-child) {
    font-size: 24px;
  }

  > :last-child {
    margin-left: auto;
  }
}
