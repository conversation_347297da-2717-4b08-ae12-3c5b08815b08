import { memo, useCallback, useEffect, useState } from 'react'
import { useLeaflet } from 'react-leaflet'
import { useDispatch } from 'react-redux'
import { actions } from '@store/features/explorer'

interface Props {
  children: React.ReactNode | React.ReactNode[]
}

const ExplorerMapDrawingState = ({ children }: Props) => {
  const dispatch = useDispatch()
  const leaflet = useLeaflet()

  const [isDrawing, setIsDrawing] = useState(false)

  const drawStartHandler = useCallback(() => {
    setIsDrawing(true)
    dispatch(actions.setIsDrawing(true))
  }, [dispatch])

  const drawStopHandler = useCallback(() => {
    setTimeout(() => {
      setIsDrawing(false)
      dispatch(actions.setIsDrawing(false))
    }, 0)
  }, [dispatch])

  useEffect(() => {
    if (leaflet?.map) {
      leaflet.map
        .on('draw:drawstart', drawStartHandler)
        .on('draw:drawstop', drawStopHandler)
    }
    return () => {
      if (leaflet?.map) {
        leaflet.map
          .off('draw:drawstart', drawStartHandler)
          .off('draw:drawstop', drawStopHandler)
      }
    }
  }, [drawStartHandler, drawStopHandler, leaflet.map])

  return <>{!isDrawing && children}</>
}

export default memo(ExplorerMapDrawingState)
