.stepsContent {
  padding: 20px;
  min-height: 200px;
  margin-top: 16px;
  background-color: #fafafa;
  border: 1px #e9e9e9;
  border-radius: 3px;

  h2,
  h3 {
    font-family: MyriadPro;
    color: var(--primary);
    margin-bottom: 0.4em;
    margin-top: 0.25em;
    font-size: 24px;
  }
  h3 {
    font-size: 18px;
  }
  p {
    color: rgba(0, 0, 0, 0.6);
  }
}

.stepsActionWrapper {
  position: sticky;
  bottom: -16px;
  z-index: 2;
}

.stepsAction {
  display: grid;
  grid-template-columns: 1fr;
  grid-auto-flow: column;
  justify-items: start;
  padding: 20px;
  font-size: 14px;
  background-color: #fafafa;
  border-top: 1px solid #f0f2f5;
}

.buttonGroup {
  display: flex;
}

.buttonGroup button {
  margin-right: 12px;
}

.buttonGroup button:last-child {
  margin-right: 0;
}

.stepContent {
  display: none;
}

.stepContent.active {
  display: block;
}

.container {
  width: 100%;
  padding: 14px;
}
