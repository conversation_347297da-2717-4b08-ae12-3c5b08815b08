import { DatePicker } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import { memo, useCallback } from 'react'
import {
  type DateInputValue,
  fromFormattedString,
  toFormattedString,
} from './helpers'

export type RangeValue = RangePickerProps['value']

const { RangePicker } = DatePicker

export type FormattedDateRangePickerProps = Omit<
  RangePickerProps,
  'value' | 'onChange' | 'format'
> & {
  format?: string
  inputFormat?: string
  value?: [DateInputValue, DateInputValue]
  onChange?: (date: [DateInputValue, DateInputValue] | undefined) => void
}

const FormattedDateRangePicker = ({
  value,
  onChange,
  format = 'DD-MM-YYYY',
  inputFormat = 'YYYY-MM-DD',
  ...props
}: FormattedDateRangePickerProps) => {
  const pickerValue =
    value === undefined
      ? undefined
      : (value?.map(fromFormattedString(inputFormat)) as RangeValue)

  const handleChange = useCallback(
    (range: RangeValue) => {
      const newRange = range?.map(
        // Basically ISO without the time, for the sake of DRF DateField
        toFormattedString(inputFormat)
      ) as NonNullable<RangeValue>
      onChange?.(newRange)
    },
    [inputFormat, onChange]
  )

  return (
    <RangePicker
      value={pickerValue}
      onChange={handleChange}
      format={format}
      {...props}
    />
  )
}

export default memo(FormattedDateRangePicker)
