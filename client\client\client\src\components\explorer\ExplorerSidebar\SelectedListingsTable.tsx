import { farmTypeOptions } from '@/variables/definitions'
import { CloseOutlined, MoreOutlined, SearchOutlined } from '@ant-design/icons'
import { distance, feature, featureCollection, point } from '@turf/turf'
import {
  Button,
  Checkbox,
  Dropdown,
  Input,
  Popconfirm,
  Table,
  Tooltip,
} from 'antd'
import type { ColumnsType } from 'antd/lib/table'
import classNames from 'classnames'
import type { Point } from 'geojson'
import L, { type LatLngTuple } from 'leaflet'
import { startCase, uniqBy } from 'lodash'
import type React from 'react'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useLeaflet } from 'react-leaflet'
import { useDispatch } from 'react-redux'
import { useIntersection, useMeasure } from 'react-use'
import ExplorerSelectedListingMenu from '@components/explorer/ExplorerSelectedListingMenu'
import FrontlineSaleForm from '@components/sales/FrontlineSaleForm'
import { useSelector } from '@store'
import sdk, {
  type FrontlineSale,
  useFrontlineSalesListQuery,
} from '@store/services/sdk'
import { toggleSelectedListingId } from '@store/ui/actions'
import { getSelectedIdsByType } from '@store/ui/selectors'
import { isLatLngTuple, isString, truthy } from '@util/guards'
import { access, equals } from '@util/helpers'
import { formatAddress } from '@util/labels'
import { sortDateTextTimeProperty, sortTextProperty } from '@util/sort'
import { SIDEBAR_DROPDOWN_PROPS } from '../const'
import styles from './SelectedListingsTable.module.scss'

type FrontlineSaleWithPosition = FrontlineSale & { position: number }

interface Props {
  setBounds?: React.Dispatch<React.SetStateAction<L.LatLngBounds | undefined>>
}

const StaleAction = ({ record }: { record: FrontlineSale }) => {
  const [save, status] = sdk.useFrontlineSalesPartialUpdateMutation()

  return (
    <Checkbox
      checked={record.stale}
      disabled={status.isLoading}
      onClick={(e) => {
        e.stopPropagation()
      }}
      onChange={() => {
        void save({
          pk: record.pk,
          patchedFrontlineSale: { stale: !record.stale },
        })
      }}
    />
  )
}

const SelectedListingsTable = ({ setBounds }: Props) => {
  const dispatch = useDispatch()

  const { map } = useLeaflet()
  const [ref, { height }] = useMeasure<HTMLDivElement>()

  const [formDirty, setFormDirty] = useState(false)
  const [selectedId, setSelectedId] = useState<string>()
  const [visibleFormId, setVisibleFormId] = useState<string>()

  const listingIds = useSelector(getSelectedIdsByType('listingIds'))
  const ids = listingIds.join(',')

  const [searchText, setSearchText] = useState('')

  const searchFilter = (listing: FrontlineSale) => {
    return searchText.split(' ').every((searchTerm) => {
      return listing.fullAddress
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase())
    })
  }

  const {
    data: listings = [],
    isFetching,
    isLoading,
  } = useFrontlineSalesListQuery({ ids })

  const listingsWithSelectedPosition: FrontlineSaleWithPosition[] = useMemo(
    () =>
      listings.map((listing) => ({
        ...listing,
        position: listingIds.findIndex(equals(listing.pk.toString())) + 1,
      })),
    [listings, listingIds]
  )

  const boundingbox = useMemo(
    () =>
      L.geoJSON(
        featureCollection(listings.map((listing) => feature(listing.geometry)))
      ).getBounds(),
    [listings]
  )

  useEffect(() => {
    setBounds?.(boundingbox)
  }, [boundingbox, setBounds])

  const loading = isFetching || isLoading

  const handleRemove = useCallback(
    (id: string) => {
      dispatch(toggleSelectedListingId(id))
    },
    [dispatch]
  )

  const columns: ColumnsType<FrontlineSaleWithPosition> = [
    {
      key: 'number',
      title: '#',
      dataIndex: 'position',
      width: '6ch',
      sorter: {
        compare: (a, b) => a.position - b.position,
      },
    },
    {
      key: 'address',
      title: (
        <span>
          Address{' '}
          {searchText ? (
            <span style={{ color: 'grey' }}>
              (Search results:{' '}
              {listingsWithSelectedPosition.filter(searchFilter).length})
            </span>
          ) : (
            ''
          )}
        </span>
      ),
      dataIndex: 'fullAddress',
      className: styles.overflow,
      render: (value: string) => (
        <Tooltip title={formatAddress(value)}>{formatAddress(value)}</Tooltip>
      ),
      filterDropdown: (
        <div style={{ padding: '8px' }}>
          <Input
            placeholder="Search Addresses"
            value={searchText || ''}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </div>
      ),
      filterIcon: <SearchOutlined />,
    },
    {
      key: 'date',
      title: 'Listing Date',
      dataIndex: 'saleDateString',
      sorter: sortDateTextTimeProperty('saleDateString'),
      width: 120,
    },
    {
      key: 'highestAndBestUse',
      title: 'HBU',
      dataIndex: 'highestAndBestUse',
      width: 170,
      render: (value: string) => {
        const name = startCase(value?.toLowerCase())
        return name
        // return <Tag color={bestUseToColorMap[name]}>{name}</Tag>
      },
      sorter: sortTextProperty('highestAndBestUse'),
      filters: farmTypeOptions.map(({ label, value }) => ({
        text: label,
        value,
      })),
      onFilter: (value, record) => {
        return record.highestAndBestUse === value
      },
    },
    {
      key: 'rm',
      title: 'RM',
      width: 130,
      className: styles.overflow,
      render: (_, record) => {
        const { firstName, lastName, username } = record.assigned || {}
        return isString(firstName) && isString(lastName)
          ? `${firstName} ${lastName}`
          : username
      },
      filters: uniqBy(
        listings.map(access('assigned')).filter(truthy),
        'pk'
      ).map(({ pk, username, firstName, lastName }) => ({
        text: firstName && lastName ? `${firstName} ${lastName}` : username,
        value: pk,
      })),
      onFilter: (value, record) => record.assigned?.pk === value,
      sorter: (a, b) =>
        (b.assigned?.username ?? 'z').localeCompare(
          a.assigned?.username ?? 'z'
        ),
    },
    {
      key: 'stale',
      title: 'Stale',
      dataIndex: 'stale',
      filters: [
        { text: 'Stale', value: true },
        { text: 'Current', value: false },
      ],
      onFilter: (value, record) => value === record.stale,
      render: (_, record) => <StaleAction record={record} />,
      width: 72,
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record) => (
        <div style={{ display: 'flex' }}>
          <Dropdown
            {...SIDEBAR_DROPDOWN_PROPS}
            overlay={<ExplorerSelectedListingMenu id={record.pk} />}
          >
            <Button size="small" type="text" icon={<MoreOutlined />} />
          </Dropdown>
          <Button
            size="small"
            type="text"
            icon={<CloseOutlined />}
            onClick={() => handleRemove(record.key)}
          />
        </div>
      ),
      fixed: 'right',
      width: 60,
    },
  ]

  return (
    <div ref={ref} className={styles.container}>
      <Table
        columns={columns}
        dataSource={listingsWithSelectedPosition.filter(searchFilter)}
        locale={{
          emptyText: searchText ? 'No results matching search filter' : null,
        }}
        expandable={{
          expandIcon: ({ expanded, onExpand, ...args }) => {
            return (
              <Popconfirm
                disabled={!formDirty}
                title="Discard unsaved changes?"
                onConfirm={(e) => {
                  onExpand(args.record, e as NonNullable<typeof e>)
                  setFormDirty(false)
                }}
              >
                <button
                  type="button"
                  aria-expanded={expanded}
                  aria-label={expanded ? 'Collapse Row' : 'Expand Row'}
                  className={classNames('ant-table-row-expand-icon', {
                    'ant-table-row-expand-icon-collapsed': !expanded,
                    'ant-table-row-expand-icon-expanded': expanded,
                  })}
                  onClick={(e) => {
                    if (expanded && formDirty) return
                    onExpand(args.record, e)
                  }}
                />
              </Popconfirm>
            )
          },
          expandedRowKeys: selectedId ? [selectedId] : [],
          expandedRowRender: (record) => {
            if (record.id !== selectedId) return null
            return (
              <RowForm
                pk={record.pk}
                setDirty={setFormDirty}
                setIntersectingId={setVisibleFormId}
              />
            )
          },
          fixed: 'left',
          onExpand: (expanded, record) => {
            setSelectedId(expanded ? record.key : undefined)
          },
        }}
        loading={loading}
        onRow={(record: FrontlineSaleWithPosition) => {
          return {
            onClick: () => {
              const coordinates = [
                ...(record.geometry as Point).coordinates,
              ].reverse()
              if (!isLatLngTuple(coordinates)) return
              map?.setView(coordinates, 13)
            },
          }
        }}
        rowClassName={(record: FrontlineSaleWithPosition) => {
          if (!map) return ''
          const center = map.getCenter()
          const centerPoint = point([center.lng, center.lat])
          const recordPoint = point(
            (record.geometry as Point).coordinates as LatLngTuple
          )
          const centerDistance = distance(centerPoint, recordPoint, {
            units: 'meters',
          })
          return classNames({
            [styles.stickyRow]:
              selectedId === record.key && visibleFormId === record.key,
            [styles.viewportRow]: centerDistance < 500,
          })
        }}
        pagination={false}
        scroll={{ x: 800, y: Math.floor(height) - 44 }} // 44 is the approx. height of the header
        showSorterTooltip={false}
        size="middle"
        tableLayout="fixed"
        className={classNames('unstyle')}
      />
    </div>
  )
}

function RowForm({
  pk,
  setDirty,
  setIntersectingId,
}: {
  pk: number
  setDirty?: React.Dispatch<React.SetStateAction<boolean>>
  setIntersectingId?: React.Dispatch<React.SetStateAction<string | undefined>>
}) {
  const ref = useRef(null)

  const entry = useIntersection(ref, {
    threshold: 0.05,
  })

  useEffect(() => {
    setIntersectingId?.(entry?.isIntersecting ? String(pk) : undefined)
    return () => setIntersectingId?.(undefined)
  }, [pk, setIntersectingId, entry?.isIntersecting])

  return (
    <div ref={ref}>
      <FrontlineSaleForm pk={pk} parentSetDirty={setDirty} />
    </div>
  )
}

export default memo(SelectedListingsTable)
