import { Text } from '@react-pdf/renderer'
import {
  COLORS,
  PdfHeading,
  PdfPage,
  PdfSection,
  PdfTable,
  stylesheet,
} from '@components/pdf'
import { skipToken } from '@components/pdf/PdfTable/helpers'
import useKpiStatement, {
  type StructuredKpi,
} from '@store/hooks/useKpiStatement'
import { formatDollarValue } from '@util'
import type { CustomerPdfProps } from './CustomerPdf'

type Props = { components: string[] } & Pick<
  CustomerPdfProps,
  'kpis' | 'segment'
>

const CustomerPdfFinancialAnalysisPage = ({
  kpis,
  segment,
  components,
}: Props) => {
  const { pivot, years } = useKpiStatement(kpis ?? [], segment ?? 'W')

  if (!components.includes('HA')) return null

  return (
    <PdfPage>
      <PdfSection key={'historical-analysis'}>
        <Text style={stylesheet.headingL}>Historical Analysis Report</Text>
        <PdfTable
          striped
          rowStyle={(row) => {
            if (row.isHeading)
              return {
                borderBottom: `2px solid ${COLORS.grey3}`,
              }
            return {}
          }}
          columns={[
            { key: 'measure', title: '' },
            ...years.map((year) => ({
              key: year.toString(),
              title: year.toString(),
            })),
          ]}
          rows={pivot.map(
            ({
              measure,
              isHeading,
              isSubtotal,
              isTotal,
              isNegative,
              ...rest
            }) => {
              const refactored: Record<
                string,
                string | boolean | undefined | number
              > &
                StructuredKpi = {
                measure: (isHeading ? (
                  <PdfHeading style={{ paddingTop: 0, paddingBottom: 0 }}>
                    {measure}
                  </PdfHeading>
                ) : (
                  measure
                  // biome-ignore lint:
                )) as any, // Sidestepping busted explicit type above
                isHeading,
                isSubtotal,
                isTotal,
              }
              for (const year of years) {
                if (isHeading) {
                  refactored[year.toString()] = skipToken
                  continue
                }

                const value = rest[year]
                let formatted = formatDollarValue(Math.abs(Number(value)))
                if (Number(value) < 0 || isNegative) {
                  formatted = `(${formatted})`
                }
                refactored[year.toString()] = formatted
              }
              return refactored
            }
          )}
        />
      </PdfSection>
    </PdfPage>
  )
}

export default CustomerPdfFinancialAnalysisPage
