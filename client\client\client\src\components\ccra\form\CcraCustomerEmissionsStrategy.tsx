import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Form, Modal, Radio, Row, Select } from 'antd'
import TextArea from 'antd/lib/input/TextArea'
import { useMemo, useState } from 'react'
import FormItems from './FormItems'
import {
  getFieldName,
  getLabelFor<PERSON>ield,
  getTooltipForField,
  isEmissionsReductionPlanTargetMeasureRequired,
} from './helpers'
import {
  EMISSIONS_REDUCTION_PLAN,
  EMISSIONS_REDUCTION_PLAN_HELP_TEXT,
  ROW_PROPS,
  CCRA_CORPORATE_FORM_FIELD,
} from './const'
import EmissionsTargets from './CustomFields/EmissionsTarget'
import { InfoCircleOutlined } from '@ant-design/icons'
import ClimateRiskManagement from './CustomFields/ClimateRiskManagement'
import type { CcraFormItem } from './types'

const StrategyNotes = () => {
  return (
    <div>
      {EMISSIONS_REDUCTION_PLAN_HELP_TEXT.map((item, _index) => (
        <div key={item.id}>
          <h3>{item.value}</h3>
          <ul style={{ listStyleType: 'disc' }}>
            {item.helpText.map((helpText, _textIndex) => (
              <li key={helpText.id}>{helpText.text}</li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  )
}

const CustomerEmissionsStrategy = () => {
  const form = Form.useFormInstance()
  const emissionsReductionPlanValue = Form.useWatch(
    getFieldName('emissionsReductionPlan'),
    form
  )
  const hasCustomerBeenAskedAboutStrategyValue = Form.useWatch(
    getFieldName('hasCustomerBeenAskedAboutStrategy'),
    form
  )
  const hasStrategyProvidedCompetitiveAdvantageValue = Form.useWatch(
    getFieldName('hasStrategyProvidedCompetitiveAdvantage'),
    form
  )
  const [isModalOpen, setIsModalOpen] = useState(false)
  const showModal = () => {
    setIsModalOpen(true)
  }
  const handleOk = () => {
    setIsModalOpen(false)
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const emissionsStrategyFormItems: CcraFormItem[] = useMemo(
    () => [
      {
        name: getFieldName('emissionsReductionPlan'),
        label: getLabelForField('emissionsReductionPlan'),
        children: (
          <Select
            style={{ maxWidth: 500 }}
            placeholder="Select a value..."
            options={EMISSIONS_REDUCTION_PLAN}
          />
        ),
        tooltip: getTooltipForField('emissionsReductionPlan'),
      },
      {
        children: (
          <Button
            style={{ marginTop: 6, marginBottom: 20 }}
            icon={<InfoCircleOutlined />}
            onClick={showModal}
          >
            View emissions plan information
          </Button>
        ),
        customField: true,
      },
      {
        disabled: !isEmissionsReductionPlanTargetMeasureRequired(
          emissionsReductionPlanValue as string
        ),
        children: <EmissionsTargets />,
        customField: true,
      },
      {
        name: getFieldName('emissionsStrategyFurtherCommentary'),
        label: getLabelForField('emissionsStrategyFurtherCommentary'),
        children: <TextArea style={{ width: 500 }} />,
        required: false,
      },
      {
        name: getFieldName('hasCustomerBeenAskedAboutStrategy'),
        label: getLabelForField('hasCustomerBeenAskedAboutStrategy'),
        tooltip: getTooltipForField('hasCustomerBeenAskedAboutStrategy'),
        children: (
          <Radio.Group buttonStyle="solid">
            <Radio.Button value={true}>Yes</Radio.Button>
            <Radio.Button value={false}>No</Radio.Button>
          </Radio.Group>
        ),
        businessUnit: CCRA_CORPORATE_FORM_FIELD,
      },
      {
        name: getFieldName('customerBeenAskedAboutStrategyExplanation'),
        label: getLabelForField('customerBeenAskedAboutStrategyExplanation'),
        tooltip: getTooltipForField(
          'customerBeenAskedAboutStrategyExplanation'
        ),
        disabled: !hasCustomerBeenAskedAboutStrategyValue,
        children: <TextArea style={{ width: 500 }} />,
        businessUnit: CCRA_CORPORATE_FORM_FIELD,
        required: false,
      },
      {
        name: getFieldName('hasStrategyProvidedCompetitiveAdvantage'),
        label: getLabelForField('hasStrategyProvidedCompetitiveAdvantage'),
        tooltip: getTooltipForField('hasStrategyProvidedCompetitiveAdvantage'),
        children: (
          <Radio.Group buttonStyle="solid">
            <Radio.Button value={true}>Yes</Radio.Button>
            <Radio.Button value={false}>No</Radio.Button>
          </Radio.Group>
        ),
        businessUnit: CCRA_CORPORATE_FORM_FIELD,
      },
      {
        name: getFieldName('strategyProvidedCompetitiveAdvantageExplanation'),
        label: getLabelForField(
          'strategyProvidedCompetitiveAdvantageExplanation'
        ),
        tooltip: getTooltipForField(
          'strategyProvidedCompetitiveAdvantageExplanation'
        ),
        disabled: !hasStrategyProvidedCompetitiveAdvantageValue,
        children: <TextArea style={{ width: 500 }} />,
        businessUnit: CCRA_CORPORATE_FORM_FIELD,
        required: false,
      },
      {
        name: getFieldName('climateRiskManagement'),
        label: getLabelForField('climateRiskManagement'),
        children: <ClimateRiskManagement />,
        businessUnit: CCRA_CORPORATE_FORM_FIELD,
      },
    ],
    [
      emissionsReductionPlanValue,
      hasCustomerBeenAskedAboutStrategyValue,
      hasStrategyProvidedCompetitiveAdvantageValue,
      // biome-ignore lint/correctness/useExhaustiveDependencies: Can't change with confidence
      showModal,
    ]
  )

  return (
    <Row {...ROW_PROPS}>
      <Col span={24}>
        <FormItems items={emissionsStrategyFormItems} />
        <Modal
          visible={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          width={800}
          cancelButtonProps={{ style: { display: 'none' } }}
        >
          <Alert
            description={<StrategyNotes />}
            type="info"
            showIcon
            style={{ marginTop: 20 }}
          />
        </Modal>
      </Col>
    </Row>
  )
}

export default CustomerEmissionsStrategy
