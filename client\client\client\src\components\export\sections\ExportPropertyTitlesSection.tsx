import React, { useMemo } from 'react'
import type { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { ExportDetailTable } from '../components/ExportDetailTable'
import { ExportSection } from '../components/ExportSection'

interface ExportPropertyTitlesSectionProps {
  titles: TitleFeatureCollection | undefined
  isFetching: boolean
}

export const ExportPropertyTitlesSection = (
  props: ExportPropertyTitlesSectionProps
) => {
  const { titles, isFetching } = props

  const tableData = useMemo(() => {
    return titles?.features.map((feature) => {
      return {
        label: feature?.properties?.titleNo,
        value: feature?.properties?.estateDescription,
      }
    })
  }, [titles?.features])

  if (!titles) {
    return null
  }

  return (
    <ExportSection isFetching={isFetching} title="Legal Titles">
      <ExportDetailTable data={tableData} />
    </ExportSection>
  )
}
