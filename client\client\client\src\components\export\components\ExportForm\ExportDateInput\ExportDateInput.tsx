import type React from 'react'

interface ExportDateInputProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
}

const ExportDateInput = (props: ExportDateInputProps) => {
  const { children, ...divProps } = props
  return (
    <div className="ExportDateInput" {...divProps}>
      <div className="input-label">Date </div>
      <div className="input-day">
        <div className="input-square">D</div>
        <div className="input-square">D</div>
      </div>
      <div className="input-month">
        <div className="input-square">M</div>
        <div className="input-square">M</div>
      </div>
      <div className="input-year">
        <div className="input-square">2</div>
        <div className="input-square">0</div>
        <div className="input-square">Y</div>
        <div className="input-square">Y</div>
      </div>
    </div>
  )
}

export default ExportDateInput
