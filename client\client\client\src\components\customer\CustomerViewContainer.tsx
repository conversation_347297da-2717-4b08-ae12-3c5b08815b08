import type { RowProps } from 'antd'
import classNames from 'classnames'
import CustomerRow from './CustomerRow'
import styles from './CustomerViewContainer.module.css'

type Props = RowProps

/**
 * Customer View Container
 * @description Container for customer view fragments that relied on a wrapping Ant Row.
 * Adding this so the Row can be swapped out for an easier to maintain grid styled
 * element with a container query or the like.
 */
const CustomerViewContainer = ({ children, className, ...props }: Props) => (
  <CustomerRow className={classNames(styles.container, className)} {...props}>
    {children}
  </CustomerRow>
)

export default CustomerViewContainer
