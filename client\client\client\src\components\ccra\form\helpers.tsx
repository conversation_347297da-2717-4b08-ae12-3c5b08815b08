import type {
  CcraFormField,
  RankedTransitionRiskItem,
  RankedContributorItem,
  EnhancedMetricType,
} from './types'
import moment, { type Moment } from 'moment'
import { fields } from './const'
import { message } from 'antd'
import { MultiOptionDisplay } from './CustomFields'
import { formatNumber } from '@util/labels'
import type { Rule } from 'antd/lib/form'
import type { CcraEmissionsReportMetricMetadata } from '@store/services/sdk/codegen'
import { isNumberRepresentation } from '@util/helpers'

export const getFieldsForStep = (step: number) => {
  switch (step) {
    case 0:
      return fields.compliance.map((field) => field.name)
    case 1:
      return fields.emissionsData.map((field) => field.name)
    case 2:
      return fields.emissionsStrategy.map((field) => field.name)
    case 3:
      return fields.climateRisks.map((field) => field.name)
    default:
      return []
  }
}

const getAllFields = (): CcraFormField[] => Object.values(fields).flat()

export const getField = (field: string): CcraFormField | null =>
  getAllFields().find((fieldData) => fieldData.name.includes(field)) || null

export const getFieldName = (field: string): string[] => {
  const foundField = getField(field)
  return foundField?.name || []
}

export const getLabelForField = (
  field: string,
  summaryMode?: boolean
): string => {
  const foundField = getField(field)
  if (summaryMode && foundField?.summaryLabel) {
    return foundField.summaryLabel
  }
  return foundField?.label || ''
}

export const getTooltipForField = (field: string): string => {
  const foundField = getField(field)
  return foundField?.tooltip || ''
}

export const getUnitForField = (field: string): string => {
  const foundField = getField(field)
  return foundField?.unit || ''
}

export const getFieldNamesByCategory = (
  category:
    | 'compliance'
    | 'emissionsData'
    | 'emissionsStrategy'
    | 'climateRisks'
): string[] => {
  const categoryFields = fields[category]
  if (!categoryFields) {
    throw new Error(`No fields found for category: ${category}`)
  }
  return categoryFields.flatMap((field) => field.name)
}

export const isEmissionsReductionPlanTargetMeasureRequired = (
  value: string
): boolean => {
  return [
    'Partly formed plan',
    'Fully formed plan',
    'Fully formed and science aligned plan',
  ].includes(String(value))
}

export const validateTransitionRisksValue = (
  value: RankedTransitionRiskItem[]
) => {
  const data = value || []
  return data.every((item: RankedTransitionRiskItem) => item.materialToCustomer)
}

export const validateTopContributorsValue = (
  value: RankedContributorItem[]
) => {
  const data =
    value ||
    [].filter((item: RankedContributorItem) => item.isCustomContributor)
  return data.every((item: RankedContributorItem) => item.contributor)
}

export const formatDate = (value: string | Moment): string => {
  const date = typeof value === 'string' ? moment(value, 'YYYY-MM-DD') : value
  return moment.isMoment(date) && date.isValid()
    ? date.format('DD/MM/YYYY')
    : '-'
}

export const formatDateRange = (value: string[] | Moment[]): string => {
  const dateRange = typeof value === 'string' ? JSON.parse(value) : value
  if (Array.isArray(dateRange) && dateRange.length === 2) {
    const [startDate, endDate] = dateRange.map(formatDate)
    return startDate !== '-' && endDate !== '-'
      ? `${startDate} - ${endDate}`
      : '-'
  }
  return '-'
}

export const formatValue = (field: string, value: unknown): string => {
  const fieldConfig = getField(field)
  const unit = getUnitForField(field)

  if (fieldConfig?.isDate) return formatDate(value as string | Moment)

  if (fieldConfig?.isDateRange)
    return formatDateRange(value as string[] | Moment[])

  if (typeof value === 'boolean') return value ? 'Yes' : 'No'

  if (typeof value === 'number' || typeof value === 'string') {
    if (!isNumberRepresentation(value)) return value.toString()

    let formattedValue = integerOrTwoDecimals(value)
    if (unit) formattedValue += ` ${unit}`
    return formattedValue
  }

  return '-'
}

export const showTargets = (
  emissionsTargets: [] | undefined,
  emissionsReductionPlan: string
) => {
  return (
    emissionsTargets &&
    emissionsTargets.length > 0 &&
    isEmissionsReductionPlanTargetMeasureRequired(emissionsReductionPlan)
  )
}

interface RiskData {
  risk: string
  materialToCustomer?: string
  measureTaken?: string
  isInsured?: boolean
}

export const handleCopyRisksToClipboard = async (
  hasAssessedRisksValue: boolean | undefined,
  plansToAssessRisksValue: boolean | undefined,
  risksValue: RiskData[] | undefined,
  getLabelForField: (field: string) => string,
  type: 'Transition' | 'Physical',
  includeIsInsured?: boolean
) => {
  try {
    let formattedText = ''

    if (typeof hasAssessedRisksValue !== 'undefined') {
      formattedText += `${getLabelForField(
        type === 'Transition'
          ? 'hasAssessedClimateTransitionRisks'
          : 'hasAssessedPhysicalRisks'
      )}\n`
      formattedText += hasAssessedRisksValue ? 'Yes' : 'No'
      formattedText += '\n\n'
    }

    if (
      hasAssessedRisksValue === false &&
      typeof plansToAssessRisksValue !== 'undefined'
    ) {
      formattedText += `${getLabelForField(
        type === 'Transition'
          ? 'plansToAssessClimateTransitionRisks'
          : 'plansToAssessPhysicalRisks'
      )}\n`
      formattedText += plansToAssessRisksValue ? 'Yes' : 'No'
      formattedText += '\n\n'
    }

    if (risksValue) {
      formattedText += `${type} Risks\n\n`

      for (const row of risksValue) {
        formattedText += `Risk: ${row.risk}\n`
        if (row.materialToCustomer) {
          formattedText += `Material to Customer: ${row.materialToCustomer}\n`
        }
        if (row.measureTaken) {
          formattedText += `Measures taken to mitigate: ${row.measureTaken}\n`
        }
        if (includeIsInsured && typeof row.isInsured !== 'undefined') {
          formattedText += `Insured: ${row.isInsured ? 'Yes' : 'No'}\n\n`
        } else {
          formattedText += '\n'
        }
      }
    }

    await navigator.clipboard.writeText(formattedText)
    message.success(`${type} Risks copied to clipboard`)
  } catch (e) {
    message.error('Failed to copy')
    console.error(e)
  }
}

export const integerOrTwoDecimals = (value: string | number): string => {
  return formatNumber(value, {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
    trailingZeroDisplay: 'stripIfInteger',
  })
}

export const defaultFormItemRules: Rule[] = [
  {
    required: true,
    message: 'This field is required',
  },
]

export const extractReportDetails = (
  data: CcraEmissionsReportMetricMetadata[],
  reportName: string
): { fields: CcraEmissionsReportMetricMetadata[]; pk?: number } => {
  const fieldsWithMetadata = data.filter(
    (report: CcraEmissionsReportMetricMetadata) =>
      report.reportType.name === reportName
  )
  return {
    fields: fieldsWithMetadata,
    pk: fieldsWithMetadata[0]?.reportType?.pk || undefined,
  }
}

export const filterAndSortMetrics = (
  metrics: CcraEmissionsReportMetricMetadata[],
  category: string
): CcraEmissionsReportMetricMetadata[] => {
  return metrics
    .filter((metric) => metric.category === category)
    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
}

export const getEndYear = (
  dateArray?: (string | null | undefined)[]
): number | null => {
  if (!Array.isArray(dateArray) || !dateArray[1]) return null

  const date = new Date(dateArray[1])
  if (Number.isNaN(date.getTime())) return null

  return date.getFullYear()
}
