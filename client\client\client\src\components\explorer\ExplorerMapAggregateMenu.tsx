import { Menu, type MenuProps } from 'antd'
import React from 'react'
import { useSelector } from '@store'
import { centreOnMap, close, zoomToCluster } from './menuItems'

const ExplorerMapAggregateMenu = ({ ...props }: MenuProps) => {
  const latLng = useSelector((state) => state.explorer.menuLatLng)

  const items = [centreOnMap(latLng), zoomToCluster(latLng), close]

  return <Menu items={items} {...props} />
}

export default ExplorerMapAggregateMenu
