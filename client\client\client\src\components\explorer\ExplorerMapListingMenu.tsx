import { Menu, type MenuProps } from 'antd'
import {
  close,
  editListing,
  toggleSelectedFeature,
  viewRecordInDashboard,
} from './menuItems'
import useSelectedItem from './useSelectedItem'

interface Props extends MenuProps {
  id: string
}

const ExplorerMapListingMenu = ({ id, ...props }: Props) => {
  const { selected, toggle } = useSelectedItem({ id, type: 'listing' })

  const items = [
    toggleSelectedFeature(selected, toggle, 'Listing'),
    viewRecordInDashboard(id, 'listing'),
    editListing(id),
    close,
  ]

  return <Menu items={items} {...props} />
}

export default ExplorerMapListingMenu
