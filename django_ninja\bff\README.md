# Backend for Frontend (BFF) Implementation

This directory contains a comprehensive Backend for Frontend (BFF) implementation for the ESGIS Django Ninja application. The BFF serves as an intermediary layer between the frontend and the RiskRadar backend API, providing authentication, global rate limiting, request routing, and response aggregation.

## Features

### 1. PingFederate Authentication
- JWT token validation using PingFederate JWKS
- Automatic token verification with configurable leeway
- User information extraction from JWT payload
- Caching of JWKS keys for performance

### 2. Global Rate Limiting
- Redis-based global rate limiting with configurable limits
- Dual-window rate limiting (burst and default)
- Per-user and per-IP rate limiting across all endpoints
- Automatic rate limit enforcement with decorators

### 3. Request Routing
- Direct routing to RiskRadar API
- Support for all HTTP methods (GET, POST, PUT, PATCH, DELETE)
- Automatic header forwarding and request transformation
- Error handling and retry logic

### 4. Response Aggregation
- Multiple aggregation strategies (merge, list, first_success)
- Parallel RiskRadar endpoint calls for improved performance
- Frontend-compatible response formatting
- Metadata inclusion for debugging and monitoring

## Architecture

```
Frontend → BFF → RiskRadar API
           ↓
    [Auth] [Global Rate Limit] [Route] [Aggregate]
```

## API Endpoints

### Main Endpoints

- `GET /bff/health/` - Health check endpoint
- `GET /bff/metrics/` - Metrics endpoint (requires admin permissions)
- `GET /bff/config/` - Configuration endpoint (requires admin permissions)
- `POST /bff/aggregate/` - Multi-API aggregation endpoint
- `* /bff/proxy/<path>` - Main proxy endpoint for single API calls

### Proxy Endpoint Usage

```bash
# Route to RiskRadar API - all paths go to RiskRadar
GET /bff/proxy/locations/
GET /bff/proxy/perils/
POST /bff/proxy/risk-assessments/

# Backward compatibility - 'riskradar/' prefix is automatically removed
GET /bff/proxy/riskradar/locations/
```

### Aggregation Endpoint Usage

```json
POST /bff/aggregate/
{
  "endpoints": [
    {
      "path": "locations",
      "method": "GET",
      "params": {"limit": 10}
    },
    {
      "path": "perils",
      "method": "GET",
      "params": {"peril_type_id": 1}
    }
  ],
  "aggregation_type": "merge"
}
```

## Configuration

### Environment Variables

#### PingFederate Configuration
- `PINGFEDERATE_ISSUER` - PingFederate issuer URL
- `PINGFEDERATE_AUDIENCE` - Expected audience for JWT tokens
- `PINGFEDERATE_JWKS_URL` - JWKS endpoint URL
- `PINGFEDERATE_ALGORITHM` - JWT signing algorithm (default: RS256)
- `PINGFEDERATE_LEEWAY` - Token validation leeway in seconds (default: 30)

#### Rate Limiting Configuration
- `BFF_RATE_LIMITING_ENABLED` - Enable/disable rate limiting (default: true)
- `BFF_DEFAULT_RATE_LIMIT` - Default requests per window (default: 100)
- `BFF_DEFAULT_RATE_WINDOW` - Default window in seconds (default: 3600)
- `BFF_BURST_RATE_LIMIT` - Burst requests per window (default: 20)
- `BFF_BURST_RATE_WINDOW` - Burst window in seconds (default: 60)

#### RiskRadar API Configuration
- `RISKRADAR_API_URL` - RiskRadar API base URL
- `RISKRADAR_API_TIMEOUT` - Request timeout in seconds
- `RISKRADAR_API_RETRIES` - Number of retry attempts

#### Redis Configuration
- `REDIS_HOST` - Redis host (default: localhost)
- `REDIS_PORT` - Redis port (default: 6379)
- `REDIS_DB` - Redis database number (default: 0)
- `REDIS_PASSWORD` - Redis password (optional)

## Authentication Flow

1. Frontend sends request with `Authorization: Bearer <JWT_TOKEN>` header
2. BFF validates JWT token against PingFederate JWKS
3. User information is extracted from validated token
4. Request proceeds with authenticated user context

## Global Rate Limiting Flow

1. Extract user ID and IP address from request
2. Check global burst rate limit (short window)
3. Check global default rate limit (long window)
4. If within limits, process request and increment global counters
5. If exceeded, return 429 Too Many Requests

## Error Handling

The BFF provides comprehensive error handling with appropriate HTTP status codes:

- `401 Unauthorized` - Authentication failed
- `403 Forbidden` - Insufficient permissions
- `429 Too Many Requests` - Rate limit exceeded
- `502 Bad Gateway` - Backend API error
- `500 Internal Server Error` - Unexpected error

## Monitoring and Observability

### Health Checks
The health endpoint provides status for:
- Redis connectivity
- RiskRadar API availability
- PingFederate JWKS accessibility

### Metrics
The metrics endpoint provides:
- Global rate limiting statistics
- RiskRadar API configuration
- Active rate limit counters

### Logging
Comprehensive logging includes:
- Authentication events
- Rate limiting violations
- Backend API calls
- Error conditions

## Security Considerations

1. **JWT Validation**: All requests must include valid JWT tokens
2. **Rate Limiting**: Prevents abuse and ensures fair usage
3. **CORS**: Configurable CORS settings for frontend integration
4. **Request Size Limits**: Configurable maximum request size
5. **HTTPS**: Optional HTTPS requirement for production

## Performance Optimizations

1. **JWKS Caching**: JWT keys are cached to reduce external calls
2. **Redis Connection Pooling**: Efficient Redis connection management
3. **Parallel API Calls**: Aggregation endpoint calls APIs in parallel
4. **Request Timeouts**: Configurable timeouts prevent hanging requests

## Development Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure environment variables in `.env` file

3. Start Redis server:
   ```bash
   redis-server
   ```

4. Run Django development server:
   ```bash
   python manage.py runserver
   ```

## Testing

Run the test suite:
```bash
python manage.py test bff
```

## Deployment

For production deployment:

1. Set all required environment variables
2. Configure Redis cluster for high availability
3. Set up load balancing for multiple BFF instances
4. Configure monitoring and alerting
5. Enable HTTPS and security headers

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check PingFederate configuration
   - Verify JWKS URL accessibility
   - Check token format and claims

2. **Rate Limiting Issues**
   - Verify Redis connectivity
   - Check rate limit configuration
   - Monitor rate limit metrics

3. **Backend API Errors**
   - Check backend API health endpoints
   - Verify API URLs and timeouts
   - Review backend API logs

### Debug Mode

Enable debug logging by setting:
```bash
export BFF_LOG_LEVEL=DEBUG
```

This will provide detailed logging for troubleshooting.
