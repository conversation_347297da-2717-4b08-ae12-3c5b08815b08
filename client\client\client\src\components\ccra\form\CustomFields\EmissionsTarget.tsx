import React, { useEffect, useState } from 'react'
import { Form, Select, Input, Button, Collapse } from 'antd'
import styles from './EmissionsTarget.module.css'
import {
  emissionsTargetTypeOptions,
  emissionsTargetUnitOptions,
} from '../../form/const'
import {
  EmissionsTargetPercentReduction,
  EmissionsTargetAbsoluteReduction,
  EmissionsTargetIntensityReduction,
  EmissionsTargetBaseline,
  EmissionsTargetType,
} from './EmissionsTargetSubFields'
import type { RangePickerProps } from 'antd/lib/date-picker'
import moment from 'moment'
import StaticTableFieldCustomerTarget from './StaticFields/StaticTableFieldCustomerTarget'
import { PlusOutlined } from '@ant-design/icons'
import { IsoStringDatePicker } from '@components/form'
import type { CcraEmissionsTarget } from '@store/services/sdk/codegen'

const shouldExpandPanel = (value: string) =>
  ['Fully formed and science aligned plan', 'Fully formed plan'].includes(value)

const { Panel } = Collapse

const EmissionsTarget = ({ index }: { index: number }) => {
  const form = Form.useFormInstance()
  const [targetUnitOptions, setTargetUnitOptions] = useState(
    emissionsTargetUnitOptions
  )
  const emissionsTargetTypeValue = Form.useWatch(
    ['emissionsTargets', index, 'emissionsTargetType'],
    form
  )

  const disabledTargetYearDate: RangePickerProps['disabledDate'] = (
    current
  ) => {
    return current && current < moment().endOf('day')
  }

  return (
    <div className={styles.container}>
      <h2>Details about the customer's target(s)</h2>
      <Form.Item hidden name={['emissionsTargets', index, 'id']}>
        <Input />
      </Form.Item>
      <div className={styles.formRow}>
        <p>The customer has a target to reduce their </p>
        <EmissionsTargetType index={index} />
        <p>emissions by</p>
        <Form.Item name={['emissionsTargets', index, 'emissionsTargetYear']}>
          <IsoStringDatePicker
            picker="year"
            disabledDate={disabledTargetYearDate}
            format="YYYY"
          />
        </Form.Item>
      </div>
      <p>Their target is to achieve:</p>
      <div className={styles.formRow}>
        <Form.Item name={['emissionsTargets', index, 'emissionsTargetType']}>
          <Select
            options={emissionsTargetTypeOptions}
            style={{ width: 320 }}
            placeholder="Select emissions target type"
          />
        </Form.Item>
        {emissionsTargetTypeValue === 'percentageReduction' && (
          <EmissionsTargetPercentReduction index={index} />
        )}
        {emissionsTargetTypeValue === 'absoluteReduction' && (
          <EmissionsTargetAbsoluteReduction
            index={index}
            targetUnitOptions={targetUnitOptions}
            setTargetUnitOptions={setTargetUnitOptions}
          />
        )}
        {emissionsTargetTypeValue === 'intensityReduction' && (
          <EmissionsTargetIntensityReduction
            index={index}
            targetUnitOptions={targetUnitOptions}
            setTargetUnitOptions={setTargetUnitOptions}
          />
        )}
      </div>
      <h2>Baseline</h2>
      <EmissionsTargetBaseline
        index={index}
        targetUnitOptions={targetUnitOptions}
        setTargetUnitOptions={setTargetUnitOptions}
      />
      <div className={styles.formRow}>
        <p>They set this target in</p>
        <Form.Item
          name={['emissionsTargets', index, 'emissionsBaselineTargetSetYear']}
        >
          <IsoStringDatePicker picker="year" format="YYYY" />
        </Form.Item>
      </div>
    </div>
  )
}

const EmissionsTargets = () => {
  const form = Form.useFormInstance()
  const emissionsTargetValue: CcraEmissionsTarget[] | undefined = Form.useWatch(
    ['emissionsTargets'],
    form
  )
  const emissionsReductionPlanValue = Form.useWatch(
    'emissionsReductionPlan',
    form
  )
  const [activeTargetIndex, setActiveTargetIndex] = useState(0)
  const [activePanelKey, setActivePanelKey] = useState<string | string[]>('0')
  useEffect(() => {
    setActivePanelKey(
      shouldExpandPanel(emissionsReductionPlanValue) ? '1' : '0'
    )
  }, [emissionsReductionPlanValue])

  const handleAddTarget = () => {
    form.setFieldValue(
      ['emissionsTargets'],
      [
        ...(emissionsTargetValue || []),
        {
          pk: Date.now(),
          emissionsType: 'Scope 1 and 2',
        },
      ]
    )
    setActiveTargetIndex(emissionsTargetValue?.length || 0)
  }

  return (
    <div className={styles.collapseContainer}>
      <Collapse activeKey={activePanelKey} onChange={setActivePanelKey} ghost>
        <Panel
          header="Details about the customer's target(s) - if applicable"
          key="1"
        >
          {emissionsTargetValue?.map(
            (row, i) =>
              activeTargetIndex === i && (
                <div key={row.id}>
                  <EmissionsTarget index={i} key={row.id} />
                </div>
              )
          )}
          <Button
            icon={<PlusOutlined />}
            type="primary"
            className={styles.button}
            onClick={handleAddTarget}
          >
            Add New Target
          </Button>
          <h2>Summary of Targets</h2>
          {emissionsTargetValue?.map((row, i) => (
            <div key={row.id}>
              <StaticTableFieldCustomerTarget
                targetIndex={i}
                activeTargetIndex={activeTargetIndex}
                setActiveTargetIndex={setActiveTargetIndex}
              />
            </div>
          ))}
        </Panel>
      </Collapse>
    </div>
  )
}

export default EmissionsTargets
