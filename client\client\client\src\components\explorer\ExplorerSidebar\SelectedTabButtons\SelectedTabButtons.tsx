import React, { type ReactNode, useMemo } from 'react'
import ExplorerBookmarksDropdown from '@components/explorer/ExplorerBookmarksDropdown'
import type { ExplorerSelectableLayer } from '@store/features/explorer'
import type { SelectedIDType } from '@store/ui/helpers'
import ToggleSelectedTooltip from '../ToggleSelectedTooltip'
import ClearSelected from './ClearSelected'
import FitMap, { type SelectionBounds } from './FitMap'
import styles from './SelectedTabButtons.module.scss'

interface Props {
  children?: ReactNode
  layer: ExplorerSelectableLayer
  selectionBounds?: SelectionBounds
}

// Should probably just provide context and get the layer that way

const SelectedTabButtons = ({ children, layer, selectionBounds }: Props) => {
  const type = useMemo(() => `${layer}Ids` as SelectedIDType, [layer])

  return (
    <div className={styles.container}>
      {(layer === 'address' || layer === 'listing') && (
        <ToggleSelectedTooltip layer={layer} />
      )}
      <FitMap bounds={selectionBounds} />
      {children}
      <ExplorerBookmarksDropdown />
      <ClearSelected layer={type} />
    </div>
  )
}

export default SelectedTabButtons
