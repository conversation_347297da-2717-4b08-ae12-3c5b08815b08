import type React from 'react'
import { ExportPrintButton } from '..'

interface ExportControlOverlayProps {
  children?: React.ReactNode | React.ReactNode[]
  isFetching?: boolean
}

const ExportControlOverlay = (props: ExportControlOverlayProps) => {
  return (
    <div className="ExportControlOverlay">
      <ExportPrintButton isFetching={props?.isFetching} />
      {props?.children}
    </div>
  )
}

export default ExportControlOverlay
