import type React from 'react'
import { HoverHint } from '@components/generic'

interface ExportControlWidgetProps {
  children?: React.ReactNode | React.ReactNode[]
  title?: string
  hint?: string
}

const ExportControlWidget = (props: ExportControlWidgetProps) => {
  return (
    <div className="ExportControlWidget">
      <div className="export-control-widget-title">
        <span>{props?.title}</span>
        {props?.hint ? <HoverHint hint={props?.hint} /> : null}
      </div>
      <div className="export-control-widget-content">{props?.children}</div>
    </div>
  )
}

export default ExportControlWidget
