import { Button, Popover } from 'antd'
import { TeamOutlined } from '@ant-design/icons'
import useCustomerSearchResults from '@components/customer/CustomerSearch/useCustomerSearchResults'
import { skipArgObject } from '@util/helpers'

const CcraSelectedCustomers = ({ customerIds }: { customerIds: number[] }) => {
  const { results: customers } = useCustomerSearchResults(
    skipArgObject({
      ids: customerIds.join(','),
    })
  )
  const content = customers.map((customer) => (
    <p key={customer.customerNumber}>
      {customer.entityName} - {customer.customerNumber}
    </p>
  ))

  return (
    <Popover title="Selected Customer Entities" content={content}>
      <Button icon={<TeamOutlined />} />
    </Popover>
  )
}

export default CcraSelectedCustomers
