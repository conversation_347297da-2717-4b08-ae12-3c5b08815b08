import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Form } from 'antd'
import classNames from 'classnames'
import React from 'react'
import { LandBestUseSummary } from '@components/assets/summary/LandBestUseSummary'
import { useGetAssetsQuery } from '@store/services/assets'
import type { Valuation } from '@types'
import { ExportSection } from '../components'
import styles from './ExportLandBestUseSummarySection.module.scss'

export interface ExportLandBestUseSummarySectionProps {
  valuation: Valuation
}

export const ExportLandBestUseSummarySection = (
  props: ExportLandBestUseSummarySectionProps
) => {
  const { valuation } = props

  const [assetForm] = Form.useForm()

  const { data: internalValuationAssets, isFetching } = useGetAssetsQuery(
    valuation?.rvrValuation?.internalValuationId ?? skipToken
  )

  return (
    <ExportSection
      title={valuation.fullAddress}
      isFetching={isFetching}
      className={classNames(
        'ExportLandBestUseSummarySection',
        styles.container
      )}
    >
      <LandBestUseSummary
        valuationId={valuation.valuationId}
        assetForm={assetForm}
        editEnabled={false}
        comparisonAssets={internalValuationAssets}
        expandImprovements={true}
      />
    </ExportSection>
  )
}
