import { Form, Input, InputNumber } from 'antd'
import React, { useEffect } from 'react'

const CcraEmissionsReportHiddenFields = ({
  reportTypeId,
}: {
  reportTypeId: number | undefined
}) => {
  const form = Form.useFormInstance()
  const periodStartEndDateValue = Form.useWatch(['periodStartEndDate'], form)

  useEffect(() => {
    if (reportTypeId !== undefined) {
      form.setFieldValue(['emissionsReport', 'reportType'], reportTypeId)
    }
  }, [reportTypeId, form])

  useEffect(() => {
    if (periodStartEndDateValue) {
      form.setFieldValue(
        ['emissionsReport', 'reportingPeriod'],
        periodStartEndDateValue
      )
    }
  }, [periodStartEndDateValue, form])

  return (
    <>
      <Form.Item hidden name={['emissionsReport', 'pk']}>
        <InputNumber />
      </Form.Item>
      <Form.Item hidden name={['emissionsReport', 'reportType']}>
        <InputNumber />
      </Form.Item>
      <Form.Item hidden name={['emissionsReport', 'reportingPeriod']}>
        <Input />
      </Form.Item>
    </>
  )
}

export default CcraEmissionsReportHiddenFields
