import type React from 'react'
import { memo } from 'react'
import {
  Button,
  Form,
  Steps,
  Popconfirm,
  message,
  type FormInstance,
  Input,
} from 'antd'
import sdk from '@store/services/sdk'
import {
  CcraCustomerSummary,
  CustomerCompliance,
  CustomerEmissionsData,
  CustomerEmissionsStrategy,
  CustomerClimateRisks,
} from './form'
import { useCcraForm } from './hooks/useCcraForm'
import styles from './CcraFormView.module.scss'
import type {
  CcraCcraCreateApiArg,
  CcraCcraUpdateApiArg,
  Ccra,
  Customer,
} from '@store/services/sdk'
import type { CcraFormStatus, CCRAFormValues } from './form/types'
import { useMemo, useRef } from 'react'
import { emissionsMetricsArrayToMap } from '@components/emissions-metrics/util'
import CcraSelectedCustomers from './form/CcraSelectedCustomers'
import { createErrorMessage } from '@util/error'

const { Step } = Steps

type CcraFormProps = {
  selectedCustomers: Customer[]
  setView: (view: string) => void
  ccraData?: Ccra
}

type CcraUpdateFormProps = CcraFormProps & {
  pk: number
}

const CcraForm = ({
  form,
  next,
  prev,
  handleFinish,
  current,
  status,
  selectedCustomers,
  setView,
}: {
  form: FormInstance
  next: () => void
  prev: () => void
  handleFinish: (
    values: unknown,
    formStatus: CcraFormStatus,
    onFinish: (payload: unknown) => void
  ) => void
  current: number
  status: CcraFormStatus
  selectedCustomers: Customer[]
  setView: (view: string) => void
}) => {
  const businessUnit = selectedCustomers[0]?.businessUnit || ''

  const steps = [
    {
      title: 'Customer Compliance',
      content: <CustomerCompliance />,
    },
    {
      title: 'Customer Emissions Data',
      content: <CustomerEmissionsData />,
    },
    {
      title: 'Customer Emissions Strategy',
      content: <CustomerEmissionsStrategy />,
    },
    {
      title: 'Customer Climate Risks',
      content: <CustomerClimateRisks />,
    },
    {
      title: 'Summary',
      content: <CcraCustomerSummary setView={setView} />,
    },
  ]

  const containerRef = useRef<HTMLDivElement>(null)

  const scrollToTop = () => {
    if (containerRef.current) {
      containerRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const customerIds = selectedCustomers.map((customer) => customer.customerId)
  const customerNames = selectedCustomers
    .map((customer) => customer.entityName)
    .join(', ')
    .trim()

  return (
    <div ref={containerRef} style={{ width: '100%' }}>
      <div className={styles.container}>
        <Steps current={current}>
          {steps.map((item) => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>
        <Form
          form={form}
          onFinish={(values) => handleFinish(values, 'complete', form.submit)}
          layout="vertical"
          initialValues={{
            customers: customerIds,
            businessUnit,
            customerNames,

            emissionsTargets: [
              {
                id: Date.now(),
                emissionsType: 'Scope 1 and 2',
              },
            ],
          }}
        >
          <Form.Item name="customers" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="businessUnit" hidden>
            <Input value={businessUnit} />
          </Form.Item>
          <Form.Item name="customerNames" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="emissionsTargets" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="emissionsType" hidden>
            <Input />
          </Form.Item>
          <div className={styles.stepsContent}>
            {steps.map((item, index) => (
              <div
                key={item.title}
                className={`${styles.stepContent} ${
                  current === index ? styles.active : ''
                }`}
              >
                {item.content}
              </div>
            ))}
          </div>
          <div className={styles.stepsActionWrapper}>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.stepsAction}>
                <div className={styles.buttonGroup}>
                  {current > 0 && (
                    <Button
                      onClick={() => {
                        prev()
                        scrollToTop()
                      }}
                    >
                      Previous
                    </Button>
                  )}
                  {current < steps.length - 1 && (
                    <Button
                      onClick={() => {
                        next()
                        scrollToTop()
                      }}
                    >
                      Next
                    </Button>
                  )}
                  <CcraSelectedCustomers customerIds={customerIds} />
                </div>
                <div className={styles.buttonGroup}>
                  {current < steps.length - 1 && (
                    <Popconfirm
                      title={
                        status === 'incomplete'
                          ? 'Save changes to a draft state and exit?'
                          : 'Save changes to completed record and exit?'
                      }
                      onConfirm={() =>
                        handleFinish(
                          form.getFieldsValue(),
                          status, // a bit convoluted, since this should just be maintaining the status of an already saved record (i.e. just submitting the same status as before)
                          form.submit
                        )
                      }
                      okText="Yes"
                      cancelText="No"
                    >
                      <Button type="primary">Save</Button>
                    </Popconfirm>
                  )}
                  {current === steps.length - 1 && (
                    <Button type="primary" onClick={() => form.submit()}>
                      Submit
                    </Button>
                  )}
                  <Popconfirm
                    title="Are you sure you want to exit? Any unsaved changes will be lost."
                    onConfirm={() => setView('overview')}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button danger>Exit</Button>
                  </Popconfirm>
                </div>
              </div>
            </Form.Item>
          </div>
        </Form>
      </div>
    </div>
  )
}

const CcraCreateForm = ({
  selectedCustomers,
  setView,
}: Omit<CcraFormProps, 'ccraData'>) => {
  const [createCcra] = sdk.useCcraMultipartCreateMutation()

  const { form, next, prev, handleFinish, current, status } =
    useCcraForm(undefined)

  return (
    <CcraForm
      form={form}
      next={next}
      prev={prev}
      status={status}
      handleFinish={(values, formStatus) =>
        handleFinish(values, formStatus, (payload) =>
          createCcra(payload as CcraCcraCreateApiArg)
            .unwrap()
            .then(() => {
              message.success('Created successfully')
              setView('overview')
            })
            .catch((error) => {
              const errorMessage = createErrorMessage(error)
              message.error(errorMessage)
              console.error(errorMessage)
            })
        )
      }
      current={current}
      selectedCustomers={selectedCustomers}
      setView={setView}
    />
  )
}

const CcraUpdateForm = memo(
  ({ selectedCustomers, setView, ccraData, pk }: CcraUpdateFormProps) => {
    const [updateCcra] = sdk.useCcraMultipartUpdateMutation()

    // `useCcraForm` sets this to the form with useEffect
    const updatedCcraData = useMemo(() => {
      // TODO: the below line breaks changing emissionsReport metrics if editing a draft with no metrics saved
      // just need to work out the why
      if (!ccraData?.emissionsReport?.metrics?.length) return ccraData

      return {
        ...ccraData,
        emissionsReport: {
          ...ccraData.emissionsReport,
          metrics: emissionsMetricsArrayToMap(ccraData.emissionsReport.metrics),
        },
      }
    }, [ccraData])

    const { form, next, prev, handleFinish, current, status } =
      useCcraForm(updatedCcraData)

    return (
      <CcraForm
        form={form}
        next={next}
        prev={prev}
        status={status}
        handleFinish={(values, formStatus) =>
          handleFinish(values, formStatus, (data) => {
            const payload = data as CcraCcraUpdateApiArg
            updateCcra({ ...payload, pk })
              .unwrap()
              .then(() => {
                message.success('Updated successfully')
                setView('overview')
              })
              .catch((error) => {
                const errorMessage = createErrorMessage(error)
                message.error(errorMessage)
                console.error(errorMessage)
              })
          })
        }
        current={current}
        selectedCustomers={selectedCustomers}
        setView={setView}
      />
    )
  }
)

const CcraFormView = (props: CcraFormProps | CcraUpdateFormProps) => {
  return 'pk' in props ? (
    <CcraUpdateForm {...props} />
  ) : (
    <CcraCreateForm {...props} />
  )
}

export default CcraFormView
