import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import ReviewStatusForm from './ReviewStatusForm'
import { COMMENT_OPTION, VALIDATION_OPTIONS } from './ReviewStatusFormItems'

const getSelectElement = () => screen.getByRole('combobox')
const getCommentsElement = () => screen.getByRole('textbox')

async function selectOption(optionText: string) {
  const select = getSelectElement()
  await userEvent.click(select)
  await waitFor(() => expect(screen.getByText(optionText)).toBeInTheDocument())
  await userEvent.click(screen.getByText(optionText))
}

const setup = () => {
  render(<ReviewStatusForm prefix="test" />)
}

test.skip('Initial State', () => {
  setup()

  const select = getSelectElement()

  expect(select).toHaveProperty('value', '')
  expect(getCommentsElement()).toHaveProperty('value', '')
  expect(getCommentsElement()).toBeDisabled()
})

test.skip('Comment input rules change with selected option', async () => {
  setup()

  await selectOption(COMMENT_OPTION.label)

  expect(screen.getByText(COMMENT_OPTION.value)).toHaveAttribute(
    'aria-selected',
    'true'
  )

  expect(getCommentsElement()).not.toBeDisabled()
  expect(getCommentsElement()).toBeRequired()
})

test.skip('Comments are reset when not required', async () => {
  const commentValue = 'Bonk'

  setup()

  await selectOption(COMMENT_OPTION.label)
  await userEvent.type(getCommentsElement(), commentValue)
  expect(getCommentsElement()).toHaveProperty('value', commentValue)
  await selectOption(VALIDATION_OPTIONS[0].label)
  expect(getCommentsElement()).toHaveProperty('value', '')
})
