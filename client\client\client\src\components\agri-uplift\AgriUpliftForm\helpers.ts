import BigNumber from 'bignumber.js'
import type { FieldData, NamePath } from 'rc-field-form/es/interface'
import pluralize from 'pluralize'
import type {
  AgriUplift,
  AgriUpliftEmissionsSource,
  AgriUpliftFacility,
  CustomerEmissionsMetric,
  CustomerEmissionsReport,
} from '@store/services/finance/codegen'
import { emissionsMetricsMapToArray } from '@components/emissions-metrics/util'
import { concatPaths, equalsProperty, getPathArray } from '@util/helpers'

export type AgriUpliftFormValues = Omit<AgriUplift, 'emissionsSources'> & {
  emissionsSources: (Omit<AgriUpliftEmissionsSource, 'emissions'> & {
    emissions: Omit<CustomerEmissionsReport, 'metrics'> & {
      metrics: Record<string, CustomerEmissionsMetric>
    }
  })[]
}

export const equalsLoanId = <T extends { loanId: number }>(
  value: T['loanId']
) => equalsProperty<T, keyof T>('loanId', value)

export const getResourcePath =
  (path: NamePath) =>
  <T extends Record<string, unknown>>(key: Exclude<keyof T, symbol>) =>
    concatPaths(path, key)

export const getInvalidEmissionsSourceIndexes = (
  changedFields: FieldData[]
) => [
  ...new Set(
    changedFields
      .filter(
        (field) =>
          !!field.errors?.length &&
          getPathArray(field.name)[0] === 'emissionsSources'
      )
      .map((field) => Number(getPathArray(field.name)[1]))
  ),
]

export const isValidTo =
  <T extends Record<string, number>>(lhs: T) =>
  (rhs?: T | null) =>
    Object.entries(lhs).every(
      ([key, value]) => value?.toString() === rhs?.[key]?.toString()
    )

export const withinPrecision = (
  value: number | string | undefined,
  precision = 2
) => (value?.toString().split('.')[1] || '').length <= precision

export const createPrecisionRule = (precision: number) => ({
  message: `Number must be within ${precision} decimal ${pluralize(
    'place',
    precision
  )}.`,
  validator: (_: unknown, value: Parameters<typeof withinPrecision>[0]) => {
    if (withinPrecision(value, precision)) return Promise.resolve()
    return Promise.reject()
  },
})

/**
 * Sum decimal number with BigNumber at 2 DP
 */
export const sumDecimals = (...decimals: number[]) => {
  BigNumber.config({ DECIMAL_PLACES: 2 })
  return decimals
    .map((decimal) => new BigNumber(decimal, 10))
    .reduce((acc, curr) => acc.plus(curr))
    .toString()
}

export const emissionsSourcesArrayToMap = (
  emissionsSources: AgriUpliftEmissionsSource[] = []
) =>
  Object.fromEntries(
    emissionsSources?.map((source: AgriUpliftEmissionsSource) => [
      source.pk,
      source,
    ])
  )

export const sourceReportTypeChanged = <
  T extends { reportType: number },
  U extends T,
  V extends T,
>(
  initialReport: U | undefined,
  updatedReport: V | undefined
): boolean => {
  return (
    initialReport != null &&
    updatedReport != null &&
    initialReport.reportType !== updatedReport.reportType
  )
}

export const prepareFormValues = (
  initialSourcesMap: Record<number, AgriUpliftEmissionsSource>,
  formValues: AgriUpliftFormValues,
  status: AgriUplift['status']
) => {
  const emissionsSources = formValues.emissionsSources.map(
    ({ emissions, ...source }) => {
      // Typescript dance
      const updatedSource: AgriUpliftEmissionsSource = { ...source }
      if (emissions) {
        const { metrics: _, ...emissionsToUpdate } = emissions
        const updatedEmissions: CustomerEmissionsReport = {
          ...emissionsToUpdate,
          metrics: [],
        }
        // Transform metrics values back to shape serializer expects
        updatedEmissions.metrics = emissionsMetricsMapToArray(emissions.metrics)
        // Unset the pk if the report type has changed to create a new object
        // This solution isn't great, it may be better to do this on the input.
        // Have tried an approach on the server that ends up being pretty convoluted,
        // but that may be worth another attempt.
        if (
          sourceReportTypeChanged(
            initialSourcesMap[updatedSource.pk]?.emissions,
            emissions
          )
        ) {
          updatedEmissions.pk = undefined as unknown as number
        }
        updatedSource.emissions = updatedEmissions
      }
      // Badly executed hack to save an empty list with companion logic in the endpoint parser
      if (!updatedSource.facilities?.length) {
        updatedSource.facilities = '[]' as unknown as AgriUpliftFacility[]
      }
      return updatedSource
    }
  )

  const updatedValues: AgriUplift = {
    ...formValues,
    status,
    emissionsSources,
    // Remove emissions report pks if the report type has changed to create new ones
  }

  return updatedValues
}
