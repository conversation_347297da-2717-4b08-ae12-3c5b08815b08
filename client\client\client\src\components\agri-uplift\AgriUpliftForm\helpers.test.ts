import { emissionsMetricTypeIdToName } from '@components/emissions-metrics/util'
import type {
  AgriUplift,
  CustomerEmissionsMetricType,
} from '@store/services/finance/codegen'
import { type AgriUpliftFormValues, prepareFormValues } from './helpers'

describe('Transform form data for submission', () => {
  const values = {
    pk: 1,
    createdDatetime: '03/02/2025',
    status: 'completed',
    updatedDatetime: '03/02/2025',
    tradingGroup: {
      anzsic: '',
      deleted: 0,
      industry: null,
      riskGroupId: null,
      tradingGroupId: '1',
      tradingGroupName: 'Trading Group',
      tradingGroupNumber: 1,
    },
    tradingGroupId: '1',
    approvedMarginPercent: '1',
    emissionsSources: [
      {
        pk: 1,
        key: '1',
        name: 'Test',
        recordId: 1,
        facilities: [],
        emissions: {
          pk: 1,
          reportType: 1,
          reportingPeriod: ['2025-02-01', '2026-01-01'],
          metrics: {
            [emissionsMetricTypeIdToName(1)]: {
              pk: 1,
              metricTypeId: 1,
              reportTypeId: 1,
              value: '1.0',
            },
          },
        },
      },
    ],
    history: {} as AgriUplift['history'],
  } as unknown as AgriUpliftFormValues

  test('It transforms emissions metrics', () => {
    const updatedValues = prepareFormValues({}, values, 'completed')

    expect(
      updatedValues?.emissionsSources[0]?.emissions?.metrics?.[0].pk
    ).toEqual(1)
  })

  test('It unsets changed source pk', () => {
    const updatedValues = prepareFormValues(
      {
        1: {
          pk: 1,
          key: '1',
          name: 'Test',
          recordId: 1,
          facilities: [],
          emissions: {
            pk: 1,
            reportType: 2,
            reportTypeName: 'boo',
            status: 'completed',
            reportingPeriod: ['2025-02-01', '2026-01-01'],
            metrics: [
              {
                pk: 1,
                metricTypeId: 1,
                reportTypeId: 1,
                value: '1.0',
                metricType: {} as CustomerEmissionsMetricType,
              },
            ],
          },
        },
      },
      values,
      'completed'
    )

    expect(updatedValues?.emissionsSources[0]?.emissions).toBeTruthy()
    expect(updatedValues?.emissionsSources[0]?.emissions?.pk).toBe(undefined)
  })

  test('It sets value to clear list', () => {
    const updatedValues = prepareFormValues({}, values, 'completed')

    expect(updatedValues?.emissionsSources[0]?.facilities).toBe('[]')
  })
})
