import { TitlesLayer } from '@components/generic/TitlesLayer'
import type { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { ExportLegend, ExportLegendItem } from '../components/ExportLegend'
import { ExportMap } from '../components/ExportMap'
import { ExportSection } from '../components/ExportSection'

export interface ExportContourProfileSectionProps {
  titles: TitleFeatureCollection
  center: { lat: number; lng: number }
  isFetching: boolean
}

export const ExportContourProfileSection = (
  props: ExportContourProfileSectionProps
) => {
  const { titles, isFetching, center } = props

  return (
    <ExportSection isFetching={isFetching}>
      <ExportMap
        type="contour"
        center={center}
        isFetching={isFetching}
        size="full"
      >
        {titles ? (
          <TitlesLayer position={1} type="map" titles={titles?.features} />
        ) : null}
      </ExportMap>
      <ExportLegend>
        <ExportLegendItem label="Title Boundary" borderColor="red" weight={3} />
      </ExportLegend>
    </ExportSection>
  )
}
