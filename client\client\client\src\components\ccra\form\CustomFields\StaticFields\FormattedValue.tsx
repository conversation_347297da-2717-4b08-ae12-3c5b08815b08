import { formatValue } from '../../helpers'
import MultiOptionDisplay from './MultiOptionDisplay'

const FormattedValue = ({
  field,
  value,
}: { field: string; value: unknown }) => {
  if (
    ['climateRiskManagement', 'processFuels'].includes(field) &&
    Array.isArray(value)
  ) {
    return <MultiOptionDisplay selectedOptionIds={value} option={field} />
  }

  return <>{formatValue(field, value)}</>
}

export default FormattedValue
