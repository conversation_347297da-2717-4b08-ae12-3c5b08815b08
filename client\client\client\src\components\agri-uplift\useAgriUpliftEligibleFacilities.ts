import { useTradingGroupLendingListQuery } from '@store/services/sdk'
import { isEligibleFacility, toPresentableFacility } from './helpers'

function useAgriUpliftEligibleFacilities(tradingGroupId: string) {
  return useTradingGroupLendingListQuery(
    {
      pk: tradingGroupId,
    },
    {
      selectFromResult: ({ data = [], ...result }) => ({
        ...result,
        data: data.filter(isEligibleFacility).map(toPresentableFacility),
      }),
    }
  )
}

export default useAgriUpliftEligibleFacilities
