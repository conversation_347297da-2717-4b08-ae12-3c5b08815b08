.ExportDetailTableItem {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding: 0.25cm;
  text-align: left;
  grid-column-gap: 1cm;
  font-family: MyriadPro;
  font-weight: 100;
}

.ExportDetailTableItem ul {
  margin-bottom: 0;
}

.ExportDetailTableItem:nth-child(even) {
  background-color: rgb(244, 248, 250);
}

.ExportDetailTableItem:nth-child(odd) {
  background-color: rgb(235, 243, 247);
}

.ExportDetailTableItem .export-detail-table-item-label {
  font-family: MyriadPro;
}

.ExportDetailTableItem .export-detail-table-item-value:empty:before {
  content: '* Information Required';
  font-style: italic;
  color: #777;
}

.ExportDetailTableItem.secondaryValue {
  grid-template-columns: 0.5fr 2fr 1fr;
}

.ExportDetailTableItem.secondaryValue.tertiaryValue {
  grid-template-columns: max-content 1fr max-content 1fr;
}
