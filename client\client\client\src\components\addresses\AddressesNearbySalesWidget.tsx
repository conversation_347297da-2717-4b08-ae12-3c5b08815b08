import { LoadingOutlined } from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Button, Skeleton, Slider, Table } from 'antd'
import classNames from 'classnames'
import { debounce } from 'lodash'
import { type FC, useEffect, useState } from 'react'
import { NavLink } from 'react-router-dom'
import { Widget } from '@components/generic'
import { useGetAddressNearbySalesQuery } from '@store/services/address'
import styles from './AddressesNearbySalesWidget.module.scss'

export const AddressNearbySalesWidget: FC<{ addressId: string }> = ({
  addressId,
}) => {
  const [page, setPage] = useState<number>(1)
  const [sales, setSales] = useState<
    Array<ReturnType<typeof useGetAddressNearbySalesQuery>['data']>
  >([])
  const [sliderValue, setSliderValue] = useState<number>(50)
  const [radius, setRadius] = useState<number>(50)

  const { data, isFetching } = useGetAddressNearbySalesQuery(
    addressId ? { addressId, page, radius } : skipToken
  )

  // biome-ignore lint/correctness/useExhaustiveDependencies:
  useEffect(() => {
    // Setting the sales as empty on id change so it doesn't look like it's displaying the wrong list
    setSales([])
    setPage(1)
  }, [addressId])

  useEffect(() => {
    if (data?.results) setSales(data.results)
  }, [data?.results])

  const handleSliderChange = debounce(setRadius, 800)

  function onChange(value: number) {
    setSliderValue(value)
    handleSliderChange(value)
  }

  return (
    <Widget
      className={classNames('AddressesNearbySalesWidget', styles.container)}
    >
      <div className={styles.heading}>
        <h2>
          Nearby Sales {'<'} {sliderValue} km
        </h2>
        <Slider
          min={5}
          max={50}
          step={5}
          value={sliderValue}
          onChange={onChange}
        />
        <div>{isFetching && <LoadingOutlined />}</div>
      </div>
      <div className={styles.content}>
        {isFetching && !sales.length ? (
          <Skeleton />
        ) : (
          <div className="agrigis-table">
            <Table
              size="small"
              rowKey={(row) => row?.id}
              dataSource={sales}
              columns={[
                {
                  dataIndex: ['properties', 'fullAddress'],
                  title: 'Address',
                  render: (text: string, record) => {
                    return (
                      <NavLink
                        to={`/sale/${record?.id}/${
                          record?.properties?.source === 'VALOCITY'
                            ? 'valocity'
                            : 'anz'
                        }/`}
                        className="action-text"
                      >
                        {text}
                      </NavLink>
                    )
                  },
                },
                {
                  dataIndex: ['properties', 'distance'],
                  title: 'Distance',
                  render: (_: string, record) => {
                    if (record?.properties?.distance) {
                      return `${record?.properties?.distance?.toLocaleString(
                        'en-NZ',
                        { maxFractionalDigits: 2 }
                      )}km`
                    }
                  },
                },
                {
                  dataIndex: ['properties', 'status'],
                  title: 'Status',
                },
                {
                  dataIndex: ['properties', 'totalHa'],
                  title: 'Area',
                },
                {
                  dataIndex: ['properties', 'saleDate'],
                  title: 'Date',
                  render: (text: string) => text?.slice(0, 10),
                },
              ]}
              pagination={false}
            />
            <div className="agrigis-table-pagination">
              <Button
                onClick={() => setPage(page - 1)}
                disabled={!data?.previous}
              >
                Previous
              </Button>
              <div className="agrigis-table-pagination-current-page">
                {data?.currentPage}
              </div>
              <Button onClick={() => setPage(page + 1)} disabled={!data?.next}>
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </Widget>
  )
}
