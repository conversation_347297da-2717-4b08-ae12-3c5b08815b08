import type { ExplorerBaseLayer } from '@store/features/explorer'

type BaseLayer = {
  name: ExplorerBaseLayer
  displayName: string
  attribution: string
  url: string
}

const baseLayers: BaseLayer[] = [
  {
    name: 'ESRI Satellite',
    displayName: 'Hybrid',
    attribution:
      'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
  },
  {
    name: 'LINZ Satellite',
    displayName: 'Satellite',
    attribution: 'Powered by <a href="https://linz.govt.nz">LINZ</a>',
    url: 'https://basemaps.linz.govt.nz/v1/tiles/aerial/EPSG:3857/{z}/{x}/{y}.webp?api=d01h2ps0frja9pa5cngr23yf0b9',
  },
  {
    name: 'OpenStreetMap',
    displayName: 'Street',
    attribution:
      '&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors',
    url: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
  },
  {
    name: 'LINZ Topo',
    displayName: 'Topo',
    attribution: 'Powered by <a href="https://linz.govt.nz">LINZ',
    url: 'https://tiles-cdn.koordinates.com/services;key=9f7f7e0f623544afae68bfd23d69e52a/tiles/v4/layer=52343/EPSG:3857/{z}/{x}/{y}.png',
  },
]

export default baseLayers
