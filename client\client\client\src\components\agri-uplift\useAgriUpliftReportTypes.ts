import { useFinanceAgriUpliftProductRetrieveQuery } from '@store/services/finance/codegen'

function useAgriUpliftReportTypes() {
  return useFinanceAgriUpliftProductRetrieveQuery(undefined, {
    selectFromResult: ({ data, ...result }) => {
      return {
        ...result,
        data: data?.reportTypes || [],
      }
    },
  })
}

export function useAgriUpliftReportTypeMap() {
  return useFinanceAgriUpliftProductRetrieveQuery(undefined, {
    selectFromResult: ({ data, ...result }) => {
      return {
        ...result,
        data: Object.fromEntries(
          (data?.reportTypes || []).map((reportType) => [
            reportType.pk,
            reportType,
          ])
        ),
      }
    },
  })
}

export default useAgriUpliftReportTypes
