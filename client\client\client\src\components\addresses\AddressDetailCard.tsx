import { DownOutlined, LoadingOutlined, UpOutlined } from '@ant-design/icons'
import { Button, Card, Empty, Skeleton } from 'antd'
import classNames from 'classnames'
import React, { useMemo, useState } from 'react'
import { useAddressesSummaryRetrieveQuery } from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import { formatAddress } from '@util/labels'
import styles from './AddressDetailCard.module.scss'

interface Props {
  id: number | string | undefined
}

const ExplorerSelectedItemDetail = ({ id }: Props) => {
  const pk = id ? +id : undefined

  const [open, setOpen] = useState(true)

  const { data, isFetching } = useAddressesSummaryRetrieveQuery(
    skipArgObject({ pk })
  )

  const valid = !!(pk && data)

  const title =
    (isFetching && <LoadingOutlined />) ||
    (valid && formatAddress(data.fullAddress)) ||
    'No Property Selected'

  const details = useMemo(
    () =>
      valid && (
        <dl className={styles.details}>
          {data.fields.map((field) => (
            <div key={field.id} className={styles.detail}>
              <dt>{field.title}</dt>
              <dd>{field.value || '–'}</dd>
            </div>
          ))}
        </dl>
      ),
    [data, valid]
  )

  return (
    <Card
      key={id}
      size="small"
      title={title}
      extra={
        <Button
          type="link"
          size="small"
          onClick={() => setOpen((v) => !v)}
          title={open ? 'Close' : 'Open'}
        >
          {open ? <DownOutlined /> : <UpOutlined />}
        </Button>
      }
      className={classNames(styles.container, {
        [styles.open]: open,
      })}
    >
      <div className={classNames(styles.content)}>
        {(isFetching && <Skeleton />) || details || <Empty />}
      </div>
    </Card>
  )
}

export default ExplorerSelectedItemDetail
