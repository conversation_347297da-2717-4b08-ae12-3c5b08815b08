import { type ChangeEvent, type HTMLAttributes, useCallback } from 'react'

interface Pro<PERSON> extends Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> {
  value?: FileList
  onChange?: (value: FileList | null) => void
}

const FileInput = ({ value, onChange, ...props }: Props) => {
  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange?.(e.target.files)
    },
    [onChange]
  )

  return (
    <div {...props}>
      <input multiple onChange={handleChange} {...props} type="file" />
    </div>
  )
}

export default FileInput
