import type { EsstReportItemExcerpt } from '@store/services/esst/codegen'
import { reportableNames } from './util'

type Props = {
  esstReportItem: Pick<EsstReportItemExcerpt, 'name' | 'reportables'>
}

export default function EsstReportItemTitle({ esstReportItem }: Props) {
  return (
    <>
      <span>{esstReportItem.name}&nbsp;</span>
      <span>{reportableNames(esstReportItem.reportables)}</span>
    </>
  )
}
