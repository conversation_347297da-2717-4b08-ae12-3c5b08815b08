.container {
  p {
    margin: 6px;
    text-transform: uppercase;
    font-size: 11px;
    color: #888888;
  }

  > p {
    margin-left: 0;
  }
}

.formRow {
  display: flex;
  flex-direction: row;
}

.formRow p:first-of-type {
  margin-left: 0;
}

.collapseContainer {
  padding-bottom: var(--space-2);
}

.collapseContainer :global(.ant-collapse-header),
.collapseContainer :global(.ant-collapse-content-box) {
  padding: 0 !important;
}

.button {
  margin-bottom: var(--space-3);
}
