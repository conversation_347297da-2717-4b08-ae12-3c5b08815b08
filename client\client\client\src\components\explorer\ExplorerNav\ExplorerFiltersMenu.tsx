import { Button, DatePicker, Input } from 'antd'
import classNames from 'classnames'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from '@store'
import { setExplorerFilters } from '@store/ui/actions'
import { getExplorerFilters } from '@store/ui/selectors'
import { explorerFilterInitialState } from '@store/ui/slice'
import { useDebounce } from '@util/useDebounce'
import styles from './ExplorerFiltersMenu.module.scss'

const ExplorerFiltersMenu = () => {
  const filters = useSelector(getExplorerFilters)

  const dispatch = useDispatch()

  const [keyword, setKeyword] = useState<string>(filters?.keyword ?? '')
  const debouncedKeyword = useDebounce(keyword, 500)

  useEffect(() => {
    if (keyword === undefined && keyword !== filters?.keyword) {
      setKeyword(filters?.keyword)
    }
  }, [filters?.keyword, keyword])

  useEffect(() => {
    dispatch(
      setExplorerFilters({
        keyword: debouncedKeyword ?? '',
      })
    )
  }, [debouncedKeyword, dispatch])

  return (
    <React.Fragment>
      <Button
        onClick={() => {
          setKeyword(explorerFilterInitialState?.keyword || '')
          dispatch(setExplorerFilters({ ...explorerFilterInitialState }))
        }}
      >
        Clear Filters
      </Button>
      <hr />
      <div className={styles.grid}>
        <div className={styles.items}>
          <div className={classNames(styles.item, styles.fullSpan)}>
            <span>Keyword Search</span>
            <Input
              value={keyword}
              placeholder="Type a keyword to filter results..."
              onChange={(e) => {
                setKeyword(e.target.value)
              }}
            />
          </div>
          <div className={styles.item}>
            <span>Sales Start Date</span>
            <DatePicker
              className={styles.fullWidth}
              value={moment(filters?.saleStartDate)}
              onChange={(e) => {
                if (e) {
                  dispatch(setExplorerFilters({ saleStartDate: e }))
                }
              }}
            />
          </div>
          <div className={styles.item}>
            <span>Sales End Date</span>
            <DatePicker
              className={styles.fullWidth}
              value={
                !filters?.saleEndDate ? moment() : moment(filters?.saleEndDate)
              }
              onChange={(e) => {
                if (e) {
                  dispatch(setExplorerFilters({ saleEndDate: e }))
                }
              }}
            />
          </div>
        </div>
      </div>
    </React.Fragment>
  )
}

export default ExplorerFiltersMenu
