import { useMemo } from 'react'
import { PdfPage } from '@components/pdf'
import CustomerFinancialGraph from './CustomerFinancialGraph'
import CustomerFinancialTreeGraph from './CustomerFinancialTreeGraph'
import type { CustomerPdfProps } from './CustomerPdf'
import { type PdfGraphDefinition, renderGraphs } from './helpers'
import {
  commercialExpenses,
  commercialFinancialPerformance,
  farmFinancialPerformance,
  farmWorkingExpenses,
} from './pdfGraphs'

type Props = { components: string[] } & Pick<
  CustomerPdfProps,
  'kpis' | 'segment'
>

const CustomerPdfPerformanceGraphsPage = ({
  kpis,
  segment,
  components,
}: Props) => {
  const performanceGraphs = useMemo(() => {
    if (!kpis) return []
    return [
      components.includes('KPI') && {
        elem: (
          <CustomerFinancialGraph
            {...(segment === 'R'
              ? farmFinancialPerformance
              : commercialFinancialPerformance)}
            data={kpis}
          />
        ),
        key: 'measure-financial-performance',
        name: 'Financial Performance',
      },
      components.includes('FWE') && {
        elem: (
          <CustomerFinancialTreeGraph
            {...(segment === 'R' ? farmWorkingExpenses : commercialExpenses)}
            data={kpis}
          />
        ),
        key: 'measure-fwe',
        name:
          segment === 'R'
            ? 'Farm Working Expenses \u2013 Yearly Average'
            : 'Expense Breakdown',
      },
    ].filter((x) => x) as PdfGraphDefinition[]
  }, [kpis, segment, components])

  if (!['KPI', 'FWE'].some((x) => components.includes(x))) return null

  return <PdfPage>{renderGraphs(performanceGraphs)}</PdfPage>
}

export default CustomerPdfPerformanceGraphsPage
