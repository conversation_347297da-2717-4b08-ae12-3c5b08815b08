import { LoadingOutlined, SearchOutlined } from '@ant-design/icons'
import { AutoComplete, type AutoCompleteProps, Empty, Input } from 'antd'
import type React from 'react'
import { useMemo, useState } from 'react'
import { useDebounce } from '@util/useDebounce'
import styles from './styles.module.css'
import classNames from 'classnames'
import {
  useEntityGroupSearchListQuery,
  type EntityGroup,
} from '@store/services/sdk'

interface EntityGroupSearchOption
  extends Pick<EntityGroup, 'entityId' | 'entityName' | 'groupType'> {
  value: string
  label: React.ReactNode
}

type EntityGroupSearchProps = AutoCompleteProps<
  unknown,
  EntityGroupSearchOption
>

export const PLACEHOLDER_TEXT = 'Customer Group / Trading Group Name'

const toOption = ({
  entityId,
  entityName,
  groupType,
}: EntityGroup): EntityGroupSearchOption => ({
  value: entityId.toString(),
  entityName,
  entityId,
  groupType,
  label: (
    <div className={styles.label}>
      <div>{entityName}</div>
      <div>
        {groupType === 'customer_group' ? 'Customer Group' : 'Trading Group'}
      </div>
    </div>
  ),
})

const EntityGroupSearch = ({ onSelect, ...props }: EntityGroupSearchProps) => {
  const [searchValue, setSearchValue] = useState('')
  const [selectedValue, setSelectedValue] = useState<string | undefined>()
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const {
    data: results,
    isFetching,
    isLoading,
  } = useEntityGroupSearchListQuery(
    {
      match: debouncedSearchValue,
    },
    {
      skip: !debouncedSearchValue,
    }
  )

  const options = useMemo(() => {
    const entityGroups = results ?? []
    return entityGroups.map(toOption)
  }, [results])

  const loading = isFetching || isLoading

  return (
    <AutoComplete
      {...props}
      className={classNames(styles.container, props.className)}
      dropdownClassName={props.className}
      allowClear={true}
      value={selectedValue}
      options={options}
      onClear={() => setSearchValue('')}
      onSearch={setSearchValue}
      onSelect={(_, option: EntityGroupSearchOption) => {
        setSelectedValue(option.entityName)
        onSelect?.(option.entityId, option)
      }}
      notFoundContent={
        Boolean(debouncedSearchValue?.length) &&
        !loading && (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No groups found"
          />
        )
      }
    >
      <Input
        prefix={loading ? <LoadingOutlined /> : <SearchOutlined />}
        placeholder={PLACEHOLDER_TEXT}
      />
    </AutoComplete>
  )
}

export default EntityGroupSearch
