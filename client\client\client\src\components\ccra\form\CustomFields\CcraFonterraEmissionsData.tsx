import { Button, Form, InputNumber } from 'antd'
import {
  getFieldName,
  getLabelForField,
  getUnitForField,
  defaultFormItemRules,
  extractReportDetails,
  filterAndSortMetrics,
  getEndYear,
} from '../helpers'

import styles from './CcraFields.module.css'
import classNames from 'classnames'
import { useCcraCcraReportTypesListQuery } from '@store/services/sdk'
import CcraEmissionsReportField from './CcraEmissionsReportField'
import CcraEmissionsReportHiddenFields from './CcraEmissionsReportHiddenFields'

const TRANSITION_YEAR = 2024

const PreTransitionYearFields = () => {
  return (
    <>
      <Form.Item
        name={getFieldName('biologicalIntensity')}
        label={getLabelForField('biologicalIntensity')}
        rules={defaultFormItemRules}
      >
        <InputNumber
          addonAfter={getUnitForField('biologicalIntensity')}
          controls={false}
          min={0}
          precision={2}
        />
      </Form.Item>
      <Form.Item
        name={getFieldName('nonBiologicalIntensity')}
        label={getLabelForField('nonBiologicalIntensity')}
        rules={defaultFormItemRules}
      >
        <InputNumber
          addonAfter={getUnitForField('nonBiologicalIntensity')}
          controls={false}
          min={0}
          precision={2}
        />
      </Form.Item>
    </>
  )
}

const PostTransitionYearFields = () => {
  const { data } = useCcraCcraReportTypesListQuery()
  const { fields, pk: reportTypeId } = data
    ? extractReportDetails(data, 'fonterra_ccit')
    : { fields: [], pk: undefined }

  const methaneFields = filterAndSortMetrics(fields, 'Methane')
  const carbonDioxideFields = filterAndSortMetrics(fields, 'Carbon Dioxide')
  const nitrousOxideFields = filterAndSortMetrics(fields, 'Nitrous Oxide')

  if (!reportTypeId) return null

  return (
    <div className={classNames(styles.gridContainer, styles.grid2Cols)}>
      <fieldset>
        <legend className={styles.label}>biological intensity</legend>
        <h4 className={styles.label}>methane</h4>
        {methaneFields.map((field) => (
          <CcraEmissionsReportField
            key={field.metricType.name}
            field={field.metricType}
            reportTypeId={reportTypeId}
          />
        ))}
        <h4 className={styles.label}>nitrous oxide</h4>
        {nitrousOxideFields.map((field) => (
          <CcraEmissionsReportField
            key={field.metricType.name}
            field={field.metricType}
            reportTypeId={reportTypeId}
          />
        ))}
      </fieldset>
      <fieldset>
        <legend className={styles.label}>non biological intensity</legend>
        <h4 className={styles.label}>carbon dioxide</h4>
        {carbonDioxideFields.map((field) => (
          <CcraEmissionsReportField
            key={field.metricType.name}
            field={field.metricType}
            reportTypeId={reportTypeId}
          />
        ))}
      </fieldset>
      <CcraEmissionsReportHiddenFields reportTypeId={reportTypeId} />
    </div>
  )
}

const CcraFonterraEmissionsData = () => {
  const form = Form.useFormInstance()
  const periodStartEndDateValue = Form.useWatch(
    getFieldName('periodStartEndDate'),
    form
  )

  const endYear = getEndYear(periodStartEndDateValue)

  if (!endYear) {
    return null
  }

  return (
    <>
      <Form.Item
        name={getFieldName('kgms')}
        label={getLabelForField('kgms')}
        rules={defaultFormItemRules}
      >
        <InputNumber
          addonAfter={getUnitForField('kgms')}
          controls={false}
          min={0}
          precision={2}
        />
      </Form.Item>
      {endYear < TRANSITION_YEAR ? (
        <PreTransitionYearFields />
      ) : (
        <PostTransitionYearFields />
      )}
    </>
  )
}

export default CcraFonterraEmissionsData
