import {
  DeleteOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { Button, Empty, Form, Input, Popconfirm, Table, Tooltip } from 'antd'
import type React from 'react'
import { useEffect, useState } from 'react'
import {
  DragDropContext,
  Draggable,
  type DropResult,
  Droppable,
  type ResponderProvided,
} from 'react-beautiful-dnd'
import { RANKED_EMISSIONS_CONTRIBUTORS } from '../const'
import type {
  DraggableTableRowProps,
  DroppableTableBodyProps,
  RankedContributorItem,
} from '../types'
import styles from './EditableTable.module.scss'
import type { ColumnType, ColumnGroupType } from 'antd/es/table'

const resetData = RANKED_EMISSIONS_CONTRIBUTORS.map((item, idx) => ({
  ...item,
  index: idx + 1,
}))

const isColumnType = (
  column:
    | ColumnType<RankedContributorItem>
    | ColumnGroupType<RankedContributorItem>
): column is ColumnType<RankedContributorItem> => {
  return (column as ColumnType<RankedContributorItem>).dataIndex !== undefined
}

const DroppableTableBody = ({
  columnId,
  docs,
  ...props
}: DroppableTableBodyProps) => {
  return (
    <Droppable droppableId={columnId}>
      {(provided, _snapshot) => (
        <>
          <tbody
            key={columnId}
            ref={(el) => void provided.innerRef(el)}
            {...props}
            {...provided.droppableProps}
          />
          {provided.placeholder}
        </>
      )}
    </Droppable>
  )
}

const DraggableTableRow = ({
  index,
  record,
  columnId,
  docs,
  orderedItems,
  setOrderedItems,
  tableColumns,
  ...props
}: DraggableTableRowProps) => {
  const [inputValue, setInputValue] = useState(record?.contributor)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
  }

  const handleInputBlur = () => {
    const newItems = orderedItems.map((item) => {
      if (item.index === record.index) {
        return { ...item, contributor: inputValue }
      }
      return item
    })
    setOrderedItems(newItems)
  }

  if (!docs.length) {
    return (
      <tr {...props}>
        <td colSpan={tableColumns.length}>
          <div>
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </div>
        </td>
      </tr>
    )
  }

  return (
    <Draggable
      key={String(record.contributor)}
      draggableId={String(record.index)}
      index={index}
    >
      {(provided, snapshot) => {
        const className = snapshot.isDragging ? `${styles.rowDragging}` : ''
        return (
          <tr
            key={record.contributor}
            ref={(el) => void provided.innerRef(el)}
            {...props}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={className}
          >
            {tableColumns.map((column) => (
              <td key={column.key}>
                {isColumnType(column) &&
                record.isCustomContributor &&
                column.dataIndex === 'contributor' ? (
                  <Input
                    value={inputValue}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    placeholder="Enter contributor name..."
                  />
                ) : isColumnType(column) && column.render ? (
                  column.render('', record, index)
                ) : isColumnType(column) ? (
                  record[column.dataIndex as keyof RankedContributorItem]
                ) : null}
              </td>
            ))}
          </tr>
        )
      }}
    </Draggable>
  )
}

const EditableTableFieldTopContributors = ({
  fieldName,
}: {
  fieldName: string[]
}) => {
  const form = Form.useFormInstance()
  const initialValue = form.getFieldValue(fieldName)
  const [orderedItems, setOrderedItems] = useState<RankedContributorItem[]>(
    initialValue && initialValue.length > 0
      ? initialValue
      : RANKED_EMISSIONS_CONTRIBUTORS
  )
  const tableColumns = [
    {
      title: 'Rank',
      dataIndex: 'index',
      key: 'index',
      width: 150,
    },
    {
      title: 'Contributors',
      dataIndex: 'contributor',
      key: 'contributors',
      render: (_: string, record: RankedContributorItem) => {
        return (
          <span>
            {record.contributor}
            {record.helpText && (
              <Tooltip title={record.helpText}>
                <QuestionCircleOutlined
                  style={{ marginLeft: 6, position: 'relative', top: '-2px' }}
                />
              </Tooltip>
            )}
          </span>
        )
      },
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          For any contributors that don't apply click the delete button
          <Popconfirm
            title="Are you sure you want to reset to the original order and contributors?"
            onConfirm={() => setOrderedItems(resetData)}
          >
            <Button
              icon={<ReloadOutlined />}
              size="small"
              style={{ marginLeft: 6 }}
            />
          </Popconfirm>
        </div>
      ),
      dataIndex: 'actions',
      key: 'actions',
      width: 200,
      render: (_: string, record: RankedContributorItem) => (
        <Popconfirm
          title="Are you sure you want to delete this record?"
          onConfirm={() => handleDelete(record.index)}
        >
          <Button icon={<DeleteOutlined />} />
        </Popconfirm>
      ),
    },
  ]

  const handleDelete = (key: React.Key) => {
    const newData = orderedItems.filter((item) => item.index !== key)

    const updatedData = newData.map((item, idx) => ({
      ...item,
      index: idx + 1,
    }))

    setOrderedItems(updatedData)
  }

  // This forces the current editable input to blur when clicking anywhere else
  // in the table. This is needed since the table re-rendered on every keystroke in the input,
  // I had to make the input only update state on blur. Then, when trying to re-order,
  // this or another row, the input would remain focused and the blur event would not fire and
  // update the state. After re-ordering the input would reset to its previous value.
  useEffect(() => {
    const handleClickOutsideInput = (event: MouseEvent) => {
      if (
        document.activeElement instanceof HTMLInputElement &&
        document.activeElement !== event.target &&
        (event.target as Element).closest('.ant-table')
      ) {
        document.activeElement.blur()
      }
    }

    document.addEventListener('mousedown', handleClickOutsideInput)
    return () => {
      document.removeEventListener('mousedown', handleClickOutsideInput)
    }
  }, [])

  useEffect(() => {
    form.setFieldValue(fieldName, orderedItems)
  }, [orderedItems, fieldName, form])

  const onDragEnd = (result: DropResult, _provided: ResponderProvided) => {
    const destination = result.destination
    const source = result.source

    if (!destination || result.reason === 'CANCEL') {
      return
    }
    reorder(source, destination)
  }

  const reorder = (
    source: { index: number },
    destination: { index: number }
  ) => {
    const editedList = [...orderedItems]
    const [removed] = editedList.splice(source.index, 1)
    editedList.splice(destination.index, 0, removed)

    editedList.forEach((item, index) => {
      item.index = index + 1
    })

    setOrderedItems(editedList)
  }

  const handleAdd = () => {
    const newIndex = orderedItems.length + 1
    setOrderedItems([
      ...orderedItems,
      { contributor: '', index: newIndex, isCustomContributor: true },
    ])
  }

  return (
    <>
      <DragDropContext onDragEnd={onDragEnd}>
        <Table
          dataSource={orderedItems}
          columns={tableColumns}
          rowKey="index"
          pagination={false}
          components={{
            body: {
              wrapper: (val: DroppableTableBodyProps) => (
                <DroppableTableBody
                  {...val}
                  columnId="orderedContributors"
                  docs={orderedItems}
                />
              ),
              row: (val: DraggableTableRowProps) => (
                <DraggableTableRow
                  {...val}
                  docs={orderedItems}
                  orderedItems={orderedItems}
                  setOrderedItems={setOrderedItems}
                  tableColumns={tableColumns}
                />
              ),
            },
          }}
          onRow={(record, index) => ({
            index,
            record,
            onClick: () => {
              // must provide an onClick
            },
          })}
        />
      </DragDropContext>
      <Tooltip title="Other contributors include waste, wastewater and refrigerants">
        <Button onClick={handleAdd} style={{ marginTop: 16 }}>
          Other
          <QuestionCircleOutlined />
        </Button>
      </Tooltip>
      <Input type="hidden" />
    </>
  )
}

export default EditableTableFieldTopContributors
