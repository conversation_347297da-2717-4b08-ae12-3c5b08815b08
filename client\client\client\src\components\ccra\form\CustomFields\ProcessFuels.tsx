import { useCcraFormOptionsListQuery } from '@store/services/sdk'
import { Checkbox } from 'antd'
import styles from './CheckboxGroup.module.css'
import type { CheckboxGroupProps } from 'antd/es/checkbox/Group'
import type { CheckboxValueType } from 'antd/es/checkbox/Group'
import classnames from 'classnames'

const ProcessFuels = ({
  value,
  onChange,
  ...restProps
}: CheckboxGroupProps) => {
  const { data } = useCcraFormOptionsListQuery({
    optionType: 'processFuels',
  })

  const options = data?.map((item) => {
    return {
      label: item.value,
      value: item.id,
    }
  })

  const noneOption = options?.find((option) =>
    option.label.includes('None of the above / Unsure')
  )

  // If none option is selected, all other options should be deselected.
  // If any other option is selected, none option should be deselected.
  const handleChange = (checkedValues: CheckboxValueType[]) => {
    if (!noneOption) {
      onChange?.(checkedValues)
      return
    }

    const currentValue = value || []
    const wasNoneSelected = currentValue.includes(noneOption.value)
    const isNoneSelected = checkedValues.includes(noneOption.value)

    if (!wasNoneSelected && isNoneSelected) {
      onChange?.([noneOption.value])
      return
    }

    if (wasNoneSelected && isNoneSelected && checkedValues.length > 1) {
      const filteredValues = checkedValues.filter((v) => v !== noneOption.value)
      onChange?.(filteredValues)
      return
    }

    onChange?.(checkedValues)
  }

  return (
    <div
      className={classnames(
        styles.checkboxGroupWrapper,
        styles.checkboxBottomMargin
      )}
    >
      <Checkbox.Group
        {...restProps}
        options={options}
        value={value}
        onChange={handleChange}
      />
    </div>
  )
}

export default ProcessFuels
