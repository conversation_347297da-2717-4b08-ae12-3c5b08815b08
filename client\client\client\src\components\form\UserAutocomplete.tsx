import { LoadingOutlined } from '@ant-design/icons'
import { type AutoCompleteProps, Select } from 'antd'
import type { DefaultOptionType } from 'antd/lib/select'
import { debounce, noop } from 'lodash'
import { useCallback, useMemo, useState } from 'react'
import { type Username, useUsernamesListQuery } from '@store/services/sdk'

type UsernameOption = Username & DefaultOptionType

interface Props extends AutoCompleteProps {
  onChange?: (option: UsernameOption | null) => void
  value?: UsernameOption | null
}

const UserAutocomplete = ({ value, onChange = noop, ...props }: Props) => {
  const [username, setUsername] = useState('')

  const { data = [], isLoading } = useUsernamesListQuery({ username })

  const options: UsernameOption[] = data.map((usernameOption) => {
    let displayName = usernameOption.username
    if (usernameOption.firstName && usernameOption.lastName) {
      displayName = `${usernameOption.firstName} ${usernameOption.lastName}`
    }
    if (usernameOption.title) {
      displayName = `${displayName} - ${usernameOption.title}`
    }
    return {
      ...usernameOption,
      label: displayName,
      value: usernameOption.username,
    }
  })

  const clearHandler = useCallback(() => {
    onChange(null)
  }, [onChange])

  const selectHandler = useCallback(
    (_, option: UsernameOption) => {
      onChange(option)
    },
    [onChange]
  )

  const searchHandler = useCallback((value: string) => {
    setUsername(value)
  }, [])

  const debouncedSearchHandler = useMemo(
    () => debounce(searchHandler, 400),
    [searchHandler]
  )

  return (
    <Select
      {...props}
      allowClear
      showSearch
      notFoundContent={isLoading ? <LoadingOutlined /> : null}
      options={options}
      onClear={clearHandler}
      onSelect={selectHandler}
      onSearch={debouncedSearchHandler}
      value={
        value?.firstName && value?.lastName
          ? `${value.firstName} ${value.lastName}`
          : value?.username
      }
      filterOption={false}
    />
  )
}

export default UserAutocomplete
