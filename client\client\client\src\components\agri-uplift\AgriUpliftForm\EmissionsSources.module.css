.panel {
  border-bottom: 1px solid var(--border-color) !important;
}

.panel :global(.ant-collapse-header) {
  /* had to imoprtant because ant css is garbo*/
  align-items: baseline !important;
  padding: var(--space-2) 0 var(--space-2) !important;
}

.panel :global(.ant-collapse-expand-icon) {
  align-self: start !important;

  > span {
    margin-right: var(--space-3) !important;
  }
}

.panel :global(.ant-collapse-content-box) {
  padding: 0;
  padding-top: var(--space-3) !important;
  padding-bottom: var(--space-6) !important;
}
