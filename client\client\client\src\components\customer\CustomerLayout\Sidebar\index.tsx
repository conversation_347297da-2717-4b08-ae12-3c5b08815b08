import React, { useMemo } from 'react'
import Profile from '@components/icons/Profile'
import DollarSignCircle from '@components/icons/DollarSignCircle'
import Loan from '@components/icons/Loan'
import ChartArrow from '@components/icons/ChartArrow'
import ChartGrowth from '@components/icons/ChartGrowth'
import Business from '@components/icons/Business'
import AgriBusiness from '@components/icons/AgriBusiness'
import Calculator from '@components/icons/Calculator'
import ThermalCoal from '@components/icons/ThermalCoal'
import C02Markets from '@components/icons/C02Markets'
import SustainabilityGlobeHeart from '@components/icons/SustainabilityGlobeHeart'
import { useCustomerRetrieveQuery } from '@store/services/sdk'
import {
  CUSTOMER_BENCHMARKING_VIEW_ENTITLEMENTS,
  CCIT_VIEW_ENTITLEMENTS,
  CUSTOMER_FINANCIALS_VIEW_ENTITLEMENTS,
  VALUATION_VIEW_ENTITLEMENTS,
  ESST_VIEW_ENTITLEMENTS,
} from '@util/entitlements'
import { insertIf, skipArgObject } from '@util/helpers'
import Container from './Container'
import Menu from './Menu'
import { SidebarProvider } from './context'
import type { MenuItem } from './helpers'
import useEsstPreview from '@components/esst/useEsstPreview'

type Props = {
  customerId: string | undefined
}

const Sidebar = ({ customerId }: Props) => {
  const pk = Number(customerId)

  const { data: customer } = useCustomerRetrieveQuery(skipArgObject({ pk }))

  const showEsstPreview = useEsstPreview()

  const items: MenuItem[] = useMemo(
    () => [
      {
        name: '/',
        label: 'Profile',
        icon: <Profile />,
      },
      {
        name: 'balances',
        label: 'Cash Balances',
        icon: <DollarSignCircle />,
      },
      {
        name: 'facilities',
        label: 'Facilities',
        icon: <Loan />,
      },
      {
        name: 'metrics',
        label: 'Financial Metrics',
        icon: <ChartArrow />,
        requiredEntitlements: CUSTOMER_FINANCIALS_VIEW_ENTITLEMENTS,
      },
      ...insertIf(customer?.segment === 'W', {
        name: 'single-customer',
        label: 'Single Customer View',
        icon: <Business />,
        requiredEntitlements: CUSTOMER_FINANCIALS_VIEW_ENTITLEMENTS,
      }),
      ...insertIf(customer?.segment === 'R', {
        name: 'my-farm',
        label: 'My Farm View',
        icon: <AgriBusiness />,
        requiredEntitlements: CUSTOMER_FINANCIALS_VIEW_ENTITLEMENTS,
      }),
      {
        name: 'benchmarks',
        label: 'Benchmarking',
        icon: <ChartGrowth />,
        requiredEntitlements: CUSTOMER_BENCHMARKING_VIEW_ENTITLEMENTS,
      },
      {
        name: 'covenants',
        label: 'Metric Testing',
        icon: <Calculator />,
        requiredEntitlements: CUSTOMER_BENCHMARKING_VIEW_ENTITLEMENTS,
      },
      {
        label: 'Emissions',
        name: 'emissions',
        icon: <ThermalCoal />,
        disabled: customer?.segment !== 'R',
        requiredEntitlements: [VALUATION_VIEW_ENTITLEMENTS],
      },
      ...(showEsstPreview
        ? [
            {
              label: 'ESG Data',
              name: 'esg',
              requiredEntitlements: CCIT_VIEW_ENTITLEMENTS,
              children: [
                {
                  label: 'Customer Climate Information',
                  name: 'climate',
                  icon: <C02Markets />,
                  requiredEntitlements: CCIT_VIEW_ENTITLEMENTS,
                },
                {
                  label: 'Environmental & Social Screening',
                  name: 'esst',
                  icon: <SustainabilityGlobeHeart />,
                  requiredEntitlements: ESST_VIEW_ENTITLEMENTS,
                },
              ],
            },
          ]
        : [
            {
              label: 'ESG Data',
              name: 'climate',
              icon: <C02Markets />,
              requiredEntitlements: CCIT_VIEW_ENTITLEMENTS,
            },
          ]),
    ],
    [customer?.segment, showEsstPreview]
  )

  return (
    <SidebarProvider customerId={Number(customerId)} disabled={!customerId}>
      <Container>
        <Menu items={items} />
      </Container>
    </SidebarProvider>
  )
}

export default Sidebar
