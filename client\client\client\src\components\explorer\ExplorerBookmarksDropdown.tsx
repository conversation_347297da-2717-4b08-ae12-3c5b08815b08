import {
  BookOutlined,
  DeleteOutlined,
  PlusOutlined,
  ReadOutlined,
  SaveOutlined,
} from '@ant-design/icons'
import { Badge, Button, Dropdown, Menu } from 'antd'
import React, { useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { removeBookmark } from '@store/ui/actions'
import type { SelectionBookmark } from '@store/ui/types'
import { useBookmarks } from '@util/useBookmarks'
import { SIDEBAR_DROPDOWN_PROPS } from './const'

const KEY_DELIMETER = '-'

const ExplorerBookmarksDropdown = () => {
  const { bookmarks, create, load, updateSelected } = useBookmarks()

  const dispatch = useDispatch()

  const removeFromBookmarks = useCallback(
    (bookmark: SelectionBookmark) => {
      dispatch(removeBookmark({ bookmark }))
    },
    [dispatch]
  )

  const items = [
    {
      key: 'new',
      label: 'New',
      icon: <PlusOutlined />,
      onClick: create,
    },
    {
      key: 'update',
      label: 'Update',
      icon: <SaveOutlined />,
      onClick: updateSelected,
    },
    {
      key: 'load',
      type: 'group',
      icon: <ReadOutlined />,
      disabled: !bookmarks.length,
      label: `Load (${bookmarks.length})`,
      children: bookmarks?.map((bookmark) => ({
        key: `bookmark-${bookmark.id}`,
        label: (
          <span style={{ display: 'flex', gap: 12 }}>
            {bookmark.name}
            <Button
              size="small"
              type="text"
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                removeFromBookmarks(bookmark)
              }}
            />
          </span>
        ),
      })),
    },
  ]

  const handleClick = useCallback(
    ({ key }: { key: string }) => {
      if (key.startsWith('bookmark')) {
        const [, id] = key.split(KEY_DELIMETER)
        const bookmarkToLoad = bookmarks.find(
          (bookmark) => bookmark.id === Number(id)
        )
        if (bookmarkToLoad) load(bookmarkToLoad)
      }
    },
    [bookmarks, load]
  )

  return (
    <>
      <Dropdown
        {...SIDEBAR_DROPDOWN_PROPS}
        overlay={<Menu items={items} onClick={handleClick} />}
      >
        <Badge color="var(--primary)" count={bookmarks.length} size="small">
          <Button size="small" icon={<BookOutlined />}>
            Bookmarks
          </Button>
        </Badge>
      </Dropdown>
    </>
  )
}

export default ExplorerBookmarksDropdown
