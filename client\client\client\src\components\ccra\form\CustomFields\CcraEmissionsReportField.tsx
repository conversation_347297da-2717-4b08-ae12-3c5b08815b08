import React from 'react'
import { Form, InputNumber } from 'antd'
import { defaultFormItemRules, getLabelForField } from '../helpers'
import type { CustomerEmissionsMetricType } from '@store/services/sdk/codegen'
import { emissionsMetricTypeIdToName } from '@components/emissions-metrics/util'
import type { CustomerEmissionsMetric } from '@store/services/finance/codegen'
import { isValidTo } from '@components/agri-uplift/AgriUpliftForm/helpers'

function EmissionsMetricInput({
  metricType,
  metricTypeId,
  reportTypeId,
  value,
  onChange,
}: {
  metricTypeId: number
  reportTypeId: number
  metricType: CustomerEmissionsMetricType
  value?: CustomerEmissionsMetric
  onChange?: (value: CustomerEmissionsMetric) => void
}) {
  const inputValue = isValidTo({ metricTypeId, reportTypeId })(value)
    ? value
    : null
  return (
    <InputNumber
      precision={2}
      addonAfter={metricType.unit}
      controls={false}
      value={inputValue?.value}
      onChange={(newValue) => {
        onChange?.({
          ...(value as CustomerEmissionsMetric),
          reportTypeId,
          metricTypeId,
          value: newValue ?? '',
          metricType,
        })
      }}
    />
  )
}

const CcraEmissionsReportField = ({
  field,
  reportTypeId,
}: {
  field: CustomerEmissionsMetricType
  reportTypeId: number
}) => {
  return (
    <Form.Item
      name={[
        'emissionsReport',
        'metrics',
        emissionsMetricTypeIdToName(field.pk),
      ]}
      label={getLabelForField(field.name)}
      rules={defaultFormItemRules}
    >
      <EmissionsMetricInput
        metricTypeId={field.pk}
        metricType={field}
        reportTypeId={reportTypeId}
      />
    </Form.Item>
  )
}

export default CcraEmissionsReportField
