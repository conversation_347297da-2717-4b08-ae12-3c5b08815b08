import { Document, Page } from '@react-pdf/renderer'
import React from 'react'
import { styles } from '@components/pdf'
import type { AddressLine } from '@store/services/sdk'
import AddressLinePDFItem from './AddressLinePDFItem'

interface Props {
  addresses: AddressLine[]
}

const AddressLinePDF = ({ addresses }: Props) => {
  return (
    <Document>
      <Page style={styles.page}>
        {addresses.map((address, i, self) => (
          <AddressLinePDFItem
            key={address.id}
            address={address}
            last={i + 1 === self.length}
          />
        ))}
      </Page>
    </Document>
  )
}

export default AddressLinePDF
