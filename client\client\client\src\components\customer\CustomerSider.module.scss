.CustomerSider {
  border-right: 1px var(--sea-spray) solid;
  overflow-y: scroll;

  &.collapsed {
    overflow-y: hidden;
    overflow-x: hidden;

    div:not(:first-child) {
      opacity: 0;
    }
  }

  .listItem {
    font-family: MyriadPro;
    gap: 1em;
    opacity: 0.6;

    &.selected {
      background-color: var(--primary-10);
      opacity: 1;
    }

    .name {
      font-weight: bold;
      font-family: gotham;
      text-transform: uppercase;
      color: var(--secondary);
      max-width: 200px;
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .categoryName {
      color: var(--primary);
      font-weight: bold;
    }

    .subcategoryName {
      color: #666;
    }
  }
}

.content {
  background-color: white;
  overflow-y: scroll;
  max-width: max-content;
  min-width: 500px;
}

.radioButtonLabel {
  display: flex;
  gap: 8px;
  justify-content: space-between;
  align-items: center;
}

.header {
  display: flex;
  justify-content: space-between;

  .headerText {
    font-family: gotham;
    text-transform: uppercase;
    color: var(--primary);
  }

  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1em;
  width: 100%;
  padding-top: 1em;

  h1,
  h2,
  h3 {
    margin: 0;
    padding: 0;
  }

  h1,
  h2 {
    font-family: Gotham;
    letter-spacing: 0.5px;
    font-size: 16px;
  }

  h1 {
    font-weight: bold;
    text-transform: uppercase;

    margin-bottom: 0.5em;
    color: var(--secondary);
  }

  h2 {
    font-family: MyriadPro;
    color: var(--primary);
    margin-bottom: 0.25em;
  }

  h3 {
    font-size: 14px;
    font-family: MyriadPro;
    color: rgba(0, 0, 0, 0.6);
    display: flex;
    gap: 0.25em;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 0.5em 1em;
  border: 1px solid #eee;
}

.container {
  overflow-y: scroll;
  max-height: calc(100vh - 282px);
}

.collapsed {
  .pagination,
  .radio,
  .container,
  .headerText {
    display: none;
  }
}

.header,
.container {
  transition: all 100ms ease 100ms;
  padding: 1em;
}

.DebouncedSearch {
  width: 100%;
  min-width: 200px;
}

.results {
  max-height: calc(100vh - 282px - 32px - 1em);
}
