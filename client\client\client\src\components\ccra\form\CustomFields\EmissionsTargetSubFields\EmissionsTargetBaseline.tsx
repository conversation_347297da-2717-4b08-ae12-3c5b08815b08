import type React from 'react'
import type { Dispatch, SetStateAction } from 'react'

import styles from '../EmissionsTarget.module.css'
import { Form, InputNumber } from 'antd'
import type { RangePickerProps } from 'antd/lib/date-picker'
import { SelectWithOptionInput } from '@components/form/SelectWithOptionInput'
import moment from 'moment'
import { IsoStringDatePicker } from '@components/form'

const EmissionsTargetBaseline = ({
  index,
  targetUnitOptions,
  setTargetUnitOptions,
}: {
  index: number
  targetUnitOptions: { value: string; label: string }[]
  setTargetUnitOptions: Dispatch<
    SetStateAction<{ value: string; label: string }[]>
  >
}) => {
  const disabledBaselineYearDate: RangePickerProps['disabledDate'] = (
    current
  ) => {
    const startDate = moment('2016-01-01', 'YYYY-MM-DD')
    const endDate = moment('2026-12-31', 'YYYY-MM-DD')
    return current && (current < startDate || current > endDate)
  }

  return (
    <div className={styles.formRow}>
      <p>Their baseline year to measure against is</p>
      <Form.Item name={['emissionsTargets', index, 'emissionsBaselineYear']}>
        <IsoStringDatePicker
          picker="year"
          disabledDate={disabledBaselineYearDate}
          placeholder="Select year"
          format={'YYYY'}
        />
      </Form.Item>
      <p>at that time their emissions were</p>
      <Form.Item name={['emissionsTargets', index, 'emissionsBaselineValue']}>
        <InputNumber
          min={1}
          style={{ width: 260 }}
          controls={false}
          placeholder="Enter value"
          addonAfter={
            <Form.Item
              name={['emissionsTargets', index, 'emissionsBaselineUnits']}
              style={{ marginBottom: -2 }}
            >
              <SelectWithOptionInput
                options={targetUnitOptions}
                setOptions={setTargetUnitOptions}
              />
            </Form.Item>
          }
        />
      </Form.Item>
    </div>
  )
}

export default EmissionsTargetBaseline
