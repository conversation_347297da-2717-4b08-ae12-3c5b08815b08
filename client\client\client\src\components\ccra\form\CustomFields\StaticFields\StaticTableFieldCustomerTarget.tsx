import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import type { CcraEmissionsTarget } from '@store/services/sdk'
import { Form, Table, Button, Popconfirm, Tag, Tooltip } from 'antd'
import moment from 'moment'
import React, { type Dispatch, type SetStateAction } from 'react'

const targetTypeValueLabelMap = {
  percentageReduction: '% of reduction',
  absoluteReduction: 'Absolute emissions',
  intensityReduction: 'Emissions intensity',
}

const TableActions = ({
  targetIndex,
  activeTargetIndex,
  setActiveTargetIndex,
}: {
  targetIndex: number
  activeTargetIndex: number
  setActiveTargetIndex: Dispatch<SetStateAction<number>>
}) => {
  const form = Form.useFormInstance()
  const handleConfirmDelete = () => {
    const currentArray = form.getFieldValue(['emissionsTargets']) || []

    const updatedArray = currentArray.filter(
      (_: CcraEmissionsTarget, index: number) => index !== targetIndex
    )

    form.setFieldValue(['emissionsTargets'], updatedArray)
    setActiveTargetIndex(updatedArray.length - 1)
  }
  const handleEdit = () => {
    setActiveTargetIndex(targetIndex)
  }
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      {activeTargetIndex === targetIndex && <Tag color="green">Editing</Tag>}
      <Tooltip title="Delete Target">
        <Popconfirm
          placement="topLeft"
          title="Are you sure you want to delete this target?"
          onConfirm={handleConfirmDelete}
          okText="Yes"
          cancelText="No"
        >
          <Button icon={<DeleteOutlined />} />
        </Popconfirm>
      </Tooltip>
      <Tooltip title="Edit Target">
        <Button icon={<EditOutlined />} onClick={handleEdit} />
      </Tooltip>
    </div>
  )
}

const StaticTableFieldCustomerTarget = ({
  targetIndex,
  activeTargetIndex,
  setActiveTargetIndex,
  providedValues,
  displayMode,
}: {
  targetIndex: number
  activeTargetIndex?: number
  setActiveTargetIndex?: Dispatch<SetStateAction<number>>
  providedValues?: CcraEmissionsTarget
  displayMode?: boolean
}) => {
  const columns = [
    {
      title:
        !displayMode &&
        setActiveTargetIndex &&
        activeTargetIndex !== undefined ? (
          <TableActions
            targetIndex={targetIndex}
            activeTargetIndex={activeTargetIndex}
            setActiveTargetIndex={setActiveTargetIndex}
          />
        ) : (
          ''
        ),
      dataIndex: 'heading',
      key: 'heading',
      width: 160,
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Target set',
      dataIndex: 'targetSet',
      key: 'targetSet',
    },
    {
      title: 'Achieve by',
      dataIndex: 'achieveBy',
      key: 'achieveBy',
    },
    {
      title: 'To reduce',
      dataIndex: 'toReduce',
      key: 'toReduce',
    },
    {
      title: 'Target',
      dataIndex: 'target',
      key: 'target',
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
    },
  ]

  const intensityColumns = [
    ...columns,
    {
      title: 'Per production unit',
      dataIndex: 'perUnit',
      key: 'perUnit',
    },
  ]
  const form = Form.useFormInstance()
  let emissionsTargetsValue = Form.useWatch(
    ['emissionsTargets', targetIndex],
    form
  )
  let emissionsTargetTypeValue = Form.useWatch(
    ['emissionsTargets', targetIndex, 'emissionsTargetType'],
    form
  )

  if (providedValues) {
    emissionsTargetsValue = providedValues
    emissionsTargetTypeValue = providedValues.emissionsTargetType
  }

  let targetValue: string

  switch (emissionsTargetTypeValue) {
    case 'percentageReduction':
      targetValue = emissionsTargetsValue?.emissionsTargetPercentage
        ? `${emissionsTargetsValue?.emissionsTargetPercentage}%`
        : ''
      break
    case 'absoluteReduction':
      targetValue =
        emissionsTargetsValue?.emissionsTargetAbsoluteFrom &&
        emissionsTargetsValue?.emissionsTargetAbsoluteTo &&
        emissionsTargetsValue?.emissionsTargetAbsoluteUnits
          ? `${emissionsTargetsValue?.emissionsTargetAbsoluteFrom} to
            ${emissionsTargetsValue?.emissionsTargetAbsoluteTo} ${emissionsTargetsValue?.emissionsTargetAbsoluteUnits}`
          : ''
      break
    case 'intensityReduction':
      targetValue =
        emissionsTargetsValue?.emissionsTargetIntensityFrom &&
        emissionsTargetsValue?.emissionsTargetIntensityTo &&
        emissionsTargetsValue?.emissionsTargetIntensityUnits
          ? `${emissionsTargetsValue?.emissionsTargetIntensityFrom} to
            ${emissionsTargetsValue?.emissionsTargetIntensityTo} ${emissionsTargetsValue?.emissionsTargetIntensityUnits}`
          : ''
      break
    default:
      targetValue = ''
  }

  const data = [
    {
      key: '1',
      heading: 'Baseline',
      date: emissionsTargetsValue?.emissionsBaselineYear
        ? moment(emissionsTargetsValue.emissionsBaselineYear).format('YYYY')
        : '',
      targetSet: '',
      achieveBy: '',
      toReduce: '',
      target: '',
      value:
        emissionsTargetsValue?.emissionsBaselineValue &&
        emissionsTargetsValue?.emissionsBaselineUnits
          ? `${emissionsTargetsValue?.emissionsBaselineValue} ${emissionsTargetsValue?.emissionsBaselineUnits}`
          : '',
    },
    {
      key: '2',
      heading: 'Target',
      date: '',
      targetSet: emissionsTargetsValue?.emissionsBaselineTargetSetYear
        ? moment(emissionsTargetsValue.emissionsBaselineTargetSetYear).format(
            'YYYY'
          )
        : '',
      achieveBy: emissionsTargetsValue?.emissionsTargetYear
        ? moment(emissionsTargetsValue.emissionsTargetYear).format('YYYY')
        : '',
      toReduce: emissionsTargetsValue?.emissionsType
        ? emissionsTargetsValue?.emissionsType
        : '',
      target: emissionsTargetsValue?.emissionsTargetType
        ? targetTypeValueLabelMap[
            emissionsTargetsValue?.emissionsTargetType as keyof typeof targetTypeValueLabelMap
          ] || ''
        : '',
      value: targetValue,
    },
  ]

  const intensityData = data.map((item, i) => ({
    ...item,
    perUnit:
      i === 0
        ? ''
        : emissionsTargetsValue?.emissionsTargetIntensityPerUnitOf || '',
  }))

  return (
    <Table
      size={displayMode ? 'small' : 'middle'}
      columns={
        emissionsTargetTypeValue === 'intensityReduction'
          ? intensityColumns
          : columns
      }
      dataSource={
        emissionsTargetTypeValue === 'intensityReduction' ? intensityData : data
      }
      bordered
      pagination={false}
      style={{ marginBottom: 24 }}
    />
  )
}

export default StaticTableFieldCustomerTarget
