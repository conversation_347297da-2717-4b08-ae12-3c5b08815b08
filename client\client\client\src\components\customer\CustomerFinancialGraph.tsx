/**
 * Don't confuse this with the KPI graph which shows a dual axes.
 * I might need to rename these to be specific graphs.
 */
import { Column } from '@ant-design/charts'
import { format } from 'date-fns'
import type React from 'react'
import type { Kpi } from '@store/services/sdk'
import { sortDateStringPropertyAscending } from '@util/sort'
import { measuresMap } from './static'

type Props = {
  print?: boolean
  data: Kpi[]
  measures: (Kpi['measure'] & string)[]
  segment: string
} & Omit<React.ComponentProps<typeof Column>, 'data' | 'xField' | 'yField'>

const CustomerFinancialGraph = ({
  data = [],
  print,
  measures,
  segment,
  ...props
}: Props) => {
  const dataSource = measures.flatMap((measure) =>
    data
      .filter((kpi) => kpi.measure === measure)
      .sort(sortDateStringPropertyAscending('periodTo'))
      .map(({ measure, periodTo, value }) => ({
        measure: measuresMap[segment][measure],
        periodTo: format(new Date(periodTo), 'dd/MM/yyyy'),
        value: Number(value),
      }))
  )

  return (
    <Column
      xField={'periodTo'}
      color={[
        'rgba(0, 125, 186, 0.75)',
        'rgba(0, 65, 101, 0.75)',
        'rgba(198, 223, 234, 0.75)',
        'rgba(185, 201, 208, 0.75)',
        'rgba(185, 204, 195, 0.75)',
        'rgba(211, 205, 139, 0.75)',
        'rgba(237, 232, 196, 0.75)',
        'rgba(122, 153, 172, 0.75)',
        'rgba(91, 198, 232, 0.75)',
        'rgba(57, 74, 88, 0.75)',
        'rgba(116, 118, 120, 0.75)',
        'rgba(170, 156, 143, 0.75)',
        'rgba(223, 122, 0, 0.75)',
        'rgba(253, 200, 47, 0.75)',
        'rgba(0, 198, 215, 0.75)',
        'rgba(88, 145, 153, 0.75)',
        'rgba(0, 125, 186, 0.75)',
        'rgba(0, 63, 102, 0.75)',
        'rgba(255, 77, 79, 0.75)',
        'rgba(250, 173, 20, 0.75)',
        'rgb(82, 196, 26, 0.75)',
      ]}
      yField={'value'}
      legend={{ position: 'bottom' }}
      yAxis={{
        label: {
          formatter: (text) => {
            return `$${Number(text).toLocaleString('en-NZ')}`
          },
        },
      }}
      isGroup={true}
      appendPadding={10}
      seriesField={'measure'}
      data={dataSource}
      {...{
        ...(print && {
          animation: false,
          pixelRatio: 3.125,
          tooltip: false,
        }),
        ...props,
      }}
    />
  )
}

export default CustomerFinancialGraph
