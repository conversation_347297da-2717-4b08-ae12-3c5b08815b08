import { skipToken } from '@reduxjs/toolkit/dist/query'
import React, { useMemo } from 'react'
import { FeatureGroup } from 'react-leaflet'
import { AgriMap } from '@components/AgriMap'
import { MapContainer } from '@components/generic'
import {
  useGetAddressQuery,
  useGetAddressTitlesQuery,
} from '@store/services/address'
import { TitleFeatureLayer } from './layers/TitleFeatureLayer'

type Props = {
  addressId: string
}

const AddressTitleMap = ({ addressId }: Props) => {
  const { data: address } = useGetAddressQuery(addressId ?? skipToken)
  const { data: addressTitles } = useGetAddressTitlesQuery(
    addressId ?? skipToken
  )

  const latLng = useMemo(() => {
    // Use current state extent location or current address latLng
    // If that fails, use last remembered location or default latLng
    return {
      lat: address?.lat ?? -36.8454297,
      lng: address?.lng ?? 174.764475,
    }
  }, [address])

  return (
    <MapContainer variant="valuation">
      <AgriMap center={latLng}>
        <FeatureGroup>
          {addressTitles ? <TitleFeatureLayer titles={addressTitles} /> : null}
        </FeatureGroup>
      </AgriMap>
    </MapContainer>
  )
}

export default AddressTitleMap
