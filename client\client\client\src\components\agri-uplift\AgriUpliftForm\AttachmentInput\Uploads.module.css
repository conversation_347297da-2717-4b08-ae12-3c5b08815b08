.container {
  margin: 0;
  margin-top: var(--space-1);
}

.list {
  border-top: 1px solid var(--border-color);
}

.item {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

.fileName {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.item:not(:hover) {
  > :last-child {
    opacity: 0;
  }
}

.item > :last-child:hover {
  color: var(--color-interactive--hover);
}
