import React, { type ReactNode } from 'react'
import styles from './ExportParagraph.module.scss'

interface ExportParagraphProps {
  children?: string | ReactNode | ReactNode[]
  html?: string
}

const ExportParagraph = ({ html, children }: ExportParagraphProps) => {
  if (html) {
    return (
      <div
        className={styles?.content}
        // biome-ignore lint/security/noDangerouslySetInnerHtml: This is bad and should be removed, but is in use at the moment
        dangerouslySetInnerHTML={{ __html: html }}
      />
    )
  }

  return <div className={'ExportParagraph'}>{children}</div>
}

export default ExportParagraph
