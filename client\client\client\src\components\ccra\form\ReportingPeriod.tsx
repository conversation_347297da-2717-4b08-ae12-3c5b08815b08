import FormattedDateRangePicker from '@components/form/FormattedDateRangePicker'
import useMaxDays from '@components/form/useMaxDays'
import { memo } from 'react'

function ReportingPeriod({ ...props }) {
  const maxDaysProps = useMaxDays(365)

  return (
    <FormattedDateRangePicker
      format="DD/MM/YYYY"
      {...maxDaysProps}
      {...props}
    />
  )
}

export default memo(ReportingPeriod)
