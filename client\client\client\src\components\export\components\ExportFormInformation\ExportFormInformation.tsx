import type React from 'react'

interface ExportFormInformationProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
}

const ExportFormInformation = (props: ExportFormInformationProps) => {
  const { children, ...divProps } = props
  return (
    <div className="ExportFormInformation" {...divProps}>
      {props?.children}
    </div>
  )
}

export default ExportFormInformation
