import React from 'react'
import { FeatureGroup } from 'react-leaflet'
import { useNavigate } from 'react-router-dom'
import type { AddressNeighbourFeatureCollection } from '../../../models/address/AddressNeighbour'
import { createNeighbourLayer } from '../util/createNeighbourLayer'

export const NeighbourFeatureLayer = (props: {
  neighbours: AddressNeighbourFeatureCollection | undefined
  noVerbose?: boolean
}) => {
  const { neighbours, noVerbose } = props
  const navigate = useNavigate()
  if (!neighbours) {
    return <></>
  }
  return (
    <FeatureGroup>
      {neighbours.features
        ? neighbours.features?.map((x) =>
            createNeighbourLayer(x, navigate, noVerbose)
          )
        : null}
    </FeatureGroup>
  )
}
