import classNames from 'classnames'
import type React from 'react'
import type { ReactNode } from 'react'
import styles from './ExportPage.module.scss'

interface ExportPageProps {
  children: ReactNode | ReactNode[]
  variant?: 'cover' | 'map' | 'legal-disclaimers' | 'marketing' | 'form'
  title?: string
  subtitle?: React.ReactNode | React.ReactNode[]
  className?: string
}

const ExportPage = (props: ExportPageProps) => {
  return (
    <div
      className={classNames(styles?.ExportPage, props.className)}
      data-variant={props?.variant ? props?.variant : 'default'}
    >
      <div className={classNames('export-page-header')}>
        {props?.title ? (
          <div className={classNames('export-page-title', styles?.title)}>
            {props?.title}
          </div>
        ) : null}
        {props?.subtitle ? (
          <div className={styles?.subtitle}>{props?.subtitle}</div>
        ) : null}
      </div>
      {props?.children}
    </div>
  )
}

export default ExportPage
