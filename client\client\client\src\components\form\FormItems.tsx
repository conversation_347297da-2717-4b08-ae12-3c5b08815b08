import { Form, type FormItemProps, Input } from 'antd'
import type { NamePath } from 'antd/lib/form/interface'
import { startCase } from 'lodash'
import React, { isValidElement } from 'react'
import { isObjectLiteral } from '@util/guards'
import { getPathArray } from '@util/helpers'
import DatePickerShadow from './DatePickerShadow'

export interface FormItem<T> extends Omit<FormItemProps, 'name'> {
  name: keyof T & string
}

export interface FormItemsProps<T> {
  path?: NamePath
  items: FormItem<T>[]
  options?: {
    placeholder?: string
  }
}

const isDatePicker = (child: React.ReactNode) =>
  React.isValidElement(child) &&
  isObjectLiteral(child.type) &&
  'MonthPicker' in child.type

const Item = <T,>({
  item,
  path = [],
  options: inputProps = {},
}: Omit<FormItemsProps<T>, 'items'> & { item: FormItem<T> }) => {
  const { children, label, name, ...props } = item
  const hasDatePicker = React.Children.toArray(children).some(isDatePicker)

  const itemName = [...getPathArray(path), name]
  const itemLabel = label ?? startCase(name)

  const itemProps = {
    ...props,
    label: itemLabel,
  }

  return (
    <>
      {!hasDatePicker ? (
        <Form.Item {...itemProps} name={itemName} shouldUpdate>
          {children ? (
            isValidElement(children) &&
            React.cloneElement(children, { ...itemProps, ...inputProps })
          ) : (
            <Input {...inputProps} />
          )}
        </Form.Item>
      ) : (
        <>
          <Form.Item {...itemProps}>
            <DatePickerShadow path={itemName} />
          </Form.Item>
          <Form.Item name={itemName} hidden>
            <Input {...inputProps} />
          </Form.Item>
        </>
      )}
    </>
  )
}

const FormItems = <T,>({ items, ...props }: FormItemsProps<T>) => {
  return (
    <React.Fragment>
      {items.map((item, i) => (
        <Item key={`${item.name}-${i}`} item={item} {...props} />
      ))}
    </React.Fragment>
  )
}

export default FormItems
