.ExportFormInput {
  display: block;
  margin-bottom: 0.25cm;
}

.ExportFormInput .input-container {
  font-family: MyriadPro;
  font-weight: 100;
  display: inline-block;
  text-align: left;
  background-color: white;
  height: 24px;
  outline: 1px solid var(--export-blue);
  width: 100%;
  padding: 0 0.125cm;
}

.ExportFormInput .input-container[data-type='checkbox'] {
  break-inside: avoid;
  width: 18px;
  height: 18px;
  margin-right: 0.25cm;
  line-height: 20px;
}

.ExportFormInput .input-checkbox-group {
  display: grid;
  grid-template-columns: 24px 1fr;
}

.ExportFormInput .input-checkbox-text {
  margin-right: 0.5cm;
}

.ExportFormInput .input-label {
  margin-bottom: 0.25cm;
}

.ExportFormInput .input-grid {
  display: flex;
  padding-bottom: 6px;
}

.ExportFormInput[data-variant='inline-bland'],
.ExportFormInput[data-variant='inline'] {
  display: grid;
  grid-template-columns: max-content 1fr;
}

.ExportFormInput[data-variant='signature'] .input-container,
.ExportFormInput[data-variant='textbox'] .input-container {
  height: 3cm;
}

.ExportFormInput[data-variant='signature'] .input-label {
  display: none;
}

.ExportFormInput[data-variant='signature'] .input-container:before {
  content: 'Signature';
}

.ExportFormInput[data-variant='inline-bland'] .input-label,
.ExportFormInput[data-variant='inline'] .input-label {
  padding-right: 0.25cm;
}

.ExportFormInput[data-variant='inline-bland'] .input-label:after,
.ExportFormInput[data-variant='inline'] .input-label:after {
  content: ':';
}

.ExportFormInput[data-variant='inline'] .input-container {
  text-align: left;
}

.ExportFormInput[data-variant='inline'] .input-container:before {
  content: '$';
  padding: 0 0.125cm;
}

.ExportFormInput > .input-group[data-even='false'],
.ExportFormInput > .input-group[data-even='true'] {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.ExportFormInput
  > .input-group[data-even='false']
  > .input-checkbox-group:last-child {
  grid-column: span 2;
}

.ExportFormInput > .input-group > .input-container {
  margin: 0;
  margin-top: 0;
  margin-bottom: 0.25cm !important;
}

.ExportFormInput[data-variant='yes-or-no'] > .input-group {
  display: flex;
}
