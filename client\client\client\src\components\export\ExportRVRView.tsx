import React, { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import {
  ExportControlOverlay,
  ExportPage,
  ExportSection,
  ExportWrapper,
} from '@components/export/components'
import { CoverPage } from '@components/export/pages/CoverPage'
import { ExportLandBestUseSummaryPage } from '@components/export/pages/ExportLandBestUseSummaryPage'
import { ExportLandClassMapPage } from '@components/export/pages/ExportLandClassMapPage'
import { ExportMortgageApportionmentTablePage } from '@components/export/pages/ExportMortgageApportionmentTablePage'
import { ExportRVRSummaryPage } from '@components/export/pages/ExportRVRSummaryPage'
import { LoadingOverlay } from '@components/generic'
import { RVRIdentifiedIssues } from '@components/rvr/RVRIdentifiedIssues'
import { RVRValuationSummary } from '@components/rvr/RVRValuationSummary'
import { useGetRVRQuery, useGetRVRValuationsQuery } from '@store/services/rvr'
import {
  useTradingGroupRetrieveQuery,
  useUserCurrentRetrieveQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'

export interface ExportRVRPageRouteParams {
  rvrId: string
}

export const ExportRVRView = () => {
  const { rvrId = '' } = useParams()

  const { data: RVR, isFetching: isRVRFetching } = useGetRVRQuery(rvrId)

  const tradingGroupId =
    RVR?.tradingGroupId === 'prospect' ? null : RVR?.tradingGroupId

  const { data: tradingGroupInformation, isFetching: isTradingGroupFetching } =
    useTradingGroupRetrieveQuery(skipArgObject({ pk: tradingGroupId }))

  const { data: user, isFetching: isUserFetching } =
    useUserCurrentRetrieveQuery()

  const { data: rvrValuations } = useGetRVRValuationsQuery(rvrId)

  const isFetching = isRVRFetching || isTradingGroupFetching || isUserFetching

  const filename = `RVR REPORT - ID ${rvrId}`

  const documentTitle = 'RVR Review'
  document.title = filename

  const documentSubtitle = useMemo(() => {
    return (
      <React.Fragment>
        {tradingGroupInformation?.tradingGroupName && (
          <div>{tradingGroupInformation?.tradingGroupName}</div>
        )}
        <div>RM: {RVR?.customerRmNumber}</div>
        {user?.userInformation ? (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              marginTop: '1cm',
            }}
          >
            <span>Prepared By {user.userInformation.name}</span>
            <span style={{ textAlign: 'right', fontSize: '28px' }}>
              {user.userInformation.title}
            </span>
          </div>
        ) : null}
      </React.Fragment>
    )
  }, [
    RVR?.customerRmNumber,
    tradingGroupInformation?.tradingGroupName,
    user?.userInformation,
  ])

  if (!RVR) {
    return <LoadingOverlay data-testid="export-rvr-page" />
  }

  return (
    <React.Fragment>
      <ExportControlOverlay isFetching={isFetching} />
      <ExportWrapper className="ExportRVRPage" data-testid="export-rvr-page">
        <CoverPage
          title={documentTitle}
          isFetching={isFetching}
          subtitle={documentSubtitle}
          classification={'Confidential: Internal Use Only'}
        />
        <ExportPage variant="legal-disclaimers">
          <div className="export-disclaimer-text">Disclaimers:</div>
          <div className="export-disclaimer-text">
            (i) This review has been prepared by the appraiser based on
            information from multiple sources, including the valuation provided
            by the external valuer, and represents the opinion of the appraiser.
          </div>

          <div className="export-disclaimer-text">
            (ii) This review is not a Credit approval. The enclosed is not a
            substitute for a review of the valuation by the Account Manager. All
            Credit issues and request for approval must be directed through your
            normal Credit chain.
          </div>

          <div className="export-disclaimer-text">
            (iii) This review has been prepared on behalf of ANZ Bank New
            Zealand Limited (&quot;Bank&quot;) for the Bank&apos;s internal use
            only and is not intended to be distributed to or relied on by any
            other person. If you receive this in error, please notify the sender
            immediately. No warranty is made as to the accuracy or reliability
            of this document or the information contained in it and, to the
            maximum extent permitted by law, the Bank disclaims all liability
            and responsibility for any direct or indirect loss or damage which
            may be suffered by any recipient through relying on anything
            contained in or omitted from this document or the information, and
            each recipient waives all claims in that regard.
          </div>
        </ExportPage>
        {rvrValuations?.map((valuation) => (
          <ExportPage key={valuation.valuationId} variant="form">
            <ExportSection title={`Valuation ${valuation.valuationId}`}>
              <RVRValuationSummary valuation={valuation} />
            </ExportSection>
          </ExportPage>
        ))}
        <ExportPage>
          <ExportSection>
            <RVRIdentifiedIssues rvrId={rvrId} />
          </ExportSection>
        </ExportPage>
        <ExportRVRSummaryPage rvrId={rvrId} />
        {rvrValuations?.map((valuation) => (
          <React.Fragment key={valuation.id}>
            <ExportLandBestUseSummaryPage
              key={`best-use-${valuation.valuationId}`}
              valuation={valuation}
            />
            <ExportMortgageApportionmentTablePage
              key={`mortgage-appointment-${valuation.valuationId}`}
              valuation={valuation}
            />
          </React.Fragment>
        ))}
        {rvrValuations?.map((valuation) => (
          <ExportLandClassMapPage
            key={valuation.valuationId}
            valuation={valuation}
          />
        ))}
      </ExportWrapper>
    </React.Fragment>
  )
}

export default ExportRVRView
