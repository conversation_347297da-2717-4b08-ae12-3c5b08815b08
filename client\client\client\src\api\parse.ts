import { TitleProperties } from '@models/title/TitleFeatureCollection'

export const parseTitleProperties = (result: any): TitleProperties => {
  return {
    id: result.id,
    fid: result.fid,
    titleId: result.fid,
    titleNo: result.title_no,
    status: result.status,
    type: result.type,
    landDistrict: result.land_district,
    issueDate: result.issue_date,
    guaranteeStatus: result.guarantee_status,
    estateDescription: result.estate_description,
    owners: result.owners,
    spatialExtentsShared: result.spatial_extents_shared,
    area: result.area,
    surveyArea: result.survey_area,
    surveyAreaHa: result.survey_area_ha,
    deletedDate: result.deleted_date,
  }
}
