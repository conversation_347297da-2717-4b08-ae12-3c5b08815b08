.container {
  --tag-margin: var(--space-1);

  display: inline-flex;
  display: table-row;
  justify-content: flex-start;
  gap: 1ch;
  margin: 0 var(--tag-margin) var(--tag-margin) 0;

  line-height: 1;
  color: var(--color-muted);

  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 6px 6px 4px;
  text-decoration: none;

  dt {
    font-weight: 400;
    display: table-cell;
  }

  dd {
    display: contents;

    > * {
      display: table-cell;
    }
  }
}

.container:hover {
  border-color: var(--border-color--hover);
}
