import React from 'react'
import SelectedTabButtons from '../SelectedTabButtons'
import { SelectedValocityListingItem } from '../SelectedValocityListingItem'

interface SelectedValocityListingsProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
  valocityListingIds?: string[]
}

const SelectedValocityListings = ({
  valocityListingIds,
  ...divProps
}: SelectedValocityListingsProps) => {
  return (
    <div {...divProps}>
      <SelectedTabButtons layer="valocityListing" />

      {valocityListingIds?.length ? (
        <React.Fragment>
          <table className="map-container-modal-table">
            <thead>
              <tr>
                <th>Address</th>
                <th>Asking Price</th>
                <th>Listing Date</th>
                <th>Area (ha)</th>
                <th>Listing Site</th>
              </tr>
            </thead>
            <tbody>
              {valocityListingIds
                ?.filter((x: string) => x !== null)
                ?.map((valocityListingId: string, index: number) => {
                  return (
                    <SelectedValocityListingItem
                      key={`selected-valocity-listing-item-${valocityListingId}`}
                      position={index}
                      valocityListingId={valocityListingId}
                    />
                  )
                })}
            </tbody>
          </table>
        </React.Fragment>
      ) : (
        <p style={{ textAlign: 'center' }}>
          No Valocity listings have been selected yet...
        </p>
      )}
    </div>
  )
}

export default SelectedValocityListings
