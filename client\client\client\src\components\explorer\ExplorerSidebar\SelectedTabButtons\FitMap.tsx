import { FullscreenOutlined } from '@ant-design/icons'
import type { ButtonProps } from 'antd'
import { useCallback } from 'react'
import { useLeaflet } from 'react-leaflet'
import SelectedTabButton from './SelectedTabButton'

export type SelectionBounds = L.LatLngBounds | null

interface Props extends ButtonProps {
  bounds?: SelectionBounds
}

const FitMap = ({ bounds = null, ...props }: Props) => {
  const { map } = useLeaflet()

  const handleClick = useCallback(() => {
    if (!(map && bounds)) return
    map.fitBounds(bounds, { padding: [10, 10] })
  }, [bounds, map])

  if (bounds === null) return null

  return (
    <SelectedTabButton
      icon={<FullscreenOutlined />}
      onClick={handleClick}
      {...props}
    >
      View Selection
    </SelectedTabButton>
  )
}

export default FitMap
