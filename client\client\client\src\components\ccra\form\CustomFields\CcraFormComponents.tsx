import { QuestionCircleOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'

export const HeadingTooltip = ({ text }: { text: string }) => {
  return (
    <Tooltip title={text}>
      <QuestionCircleOutlined
        style={{
          marginLeft: 5,
          fontSize: '0.7em',
          position: 'relative',
          top: '-0.20em',
        }}
      />
    </Tooltip>
  )
}

export const FormSection = ({
  children,
  title,
  tooltip,
}: {
  children: React.ReactNode
  title?: string
  tooltip?: string
}) => {
  if (!title) return <>{children}</>

  return (
    <>
      <h2>
        {title}
        {tooltip && <HeadingTooltip text={tooltip} />}
      </h2>
      {children}
    </>
  )
}

export const FormParagraph = ({
  text,
  disabled = false,
}: { text: string; disabled?: boolean }) => {
  if (disabled) return null
  return <p>{text}</p>
}
