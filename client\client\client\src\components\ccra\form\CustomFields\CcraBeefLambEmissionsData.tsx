import React from 'react'
import {
  extractReportDetails,
  filterAndSortMetrics,
  getFieldName,
} from '../helpers'

import styles from './CcraFields.module.css'
import classNames from 'classnames'
import { useCcraCcraReportTypesListQuery } from '@store/services/sdk'
import CcraEmissionsReportField from './CcraEmissionsReportField'
import CcraEmissionsReportHiddenFields from './CcraEmissionsReportHiddenFields'
import { Form } from 'antd'

const CcraBeefLambEmissionsData = () => {
  const form = Form.useFormInstance()
  const { data } = useCcraCcraReportTypesListQuery()
  const { fields, pk: reportTypeId } = data
    ? extractReportDetails(data, 'beef_lamb_ccit')
    : { fields: [], pk: undefined }

  const liveStockEmissionsFields = filterAndSortMetrics(
    fields,
    'Livestock Emissions'
  )
  const fertiliserLimeUseFields = filterAndSortMetrics(
    fields,
    'Fertiliser & Lime Use'
  )
  const otherEmissionsFields = filterAndSortMetrics(fields, 'Other Emissions')

  const periodStartEndDateValue = Form.useWatch(
    getFieldName('periodStartEndDate'),
    form
  )

  if (!reportTypeId || !periodStartEndDateValue) return null

  return (
    <>
      <div className={classNames(styles.gridContainer, styles.grid3Cols)}>
        <fieldset>
          <legend className={styles.label}>livestock emissions</legend>
          {liveStockEmissionsFields.map((field) => (
            <CcraEmissionsReportField
              key={field.metricType.name}
              field={field.metricType}
              reportTypeId={reportTypeId}
            />
          ))}
        </fieldset>
        <fieldset>
          <legend className={styles.label}>fertiliser & lime use</legend>
          {fertiliserLimeUseFields.map((field) => (
            <CcraEmissionsReportField
              key={field.metricType.name}
              field={field.metricType}
              reportTypeId={reportTypeId}
            />
          ))}
        </fieldset>
        <fieldset>
          <legend className={styles.label}>other emissions</legend>
          {otherEmissionsFields.map((field) => (
            <CcraEmissionsReportField
              key={field.metricType.name}
              field={field.metricType}
              reportTypeId={reportTypeId}
            />
          ))}
        </fieldset>
        <CcraEmissionsReportHiddenFields reportTypeId={reportTypeId} />
      </div>
    </>
  )
}

export default CcraBeefLambEmissionsData
