import { Progress } from 'antd'
import React from 'react'

export function renderProgress(value: boolean) {
  if (value) {
    return <Progress size="small" percent={0} />
  }
  return <Progress size="small" percent={100} />
}

export const DataProgressBars = (props: {
  addressIsLoading?: boolean | null
  elevationIsLoading?: boolean | null
  unionIsLoading?: boolean | null
  smapFamilyIsLoading?: boolean | null
  titlesIsLoading?: boolean | null
  neighboursIsLoading?: boolean | null
  dvrIsLoading?: boolean | null
  salesIsLoading?: boolean | null
}) => {
  const {
    addressIsLoading,
    elevationIsLoading,
    unionIsLoading,
    titlesIsLoading,
    neighboursIsLoading,
    dvrIsLoading,
    salesIsLoading,
  } = props
  return addressIsLoading ||
    elevationIsLoading ||
    unionIsLoading ||
    titlesIsLoading ||
    neighboursIsLoading ? (
    <div className="address-data-load-progress">
      {addressIsLoading !== null ? (
        <div>
          <span>Address Data</span>
          {renderProgress(addressIsLoading || false)}
        </div>
      ) : (
        <></>
      )}
      {elevationIsLoading !== null ? (
        <div>
          <span>Elevation Data</span>
          {renderProgress(elevationIsLoading || false)}
        </div>
      ) : (
        <></>
      )}
      {unionIsLoading !== null ? (
        <div>
          <span>Spatial Data</span>
          {renderProgress(unionIsLoading || false)}
        </div>
      ) : (
        <></>
      )}
      {titlesIsLoading !== null ? (
        <div>
          <span>Titles Data</span>
          {renderProgress(titlesIsLoading || false)}
        </div>
      ) : (
        <></>
      )}
      {neighboursIsLoading !== null ? (
        <div>
          <span>Neighbours Data</span>
          {renderProgress(neighboursIsLoading || false)}
        </div>
      ) : (
        <></>
      )}
      {dvrIsLoading !== null ? (
        <div>
          <span>DVR Data</span>
          {renderProgress(dvrIsLoading || false)}
        </div>
      ) : (
        <></>
      )}
      {salesIsLoading !== null ? (
        <div>
          <span>Sales Data</span>
          {renderProgress(salesIsLoading || false)}
        </div>
      ) : (
        <></>
      )}
    </div>
  ) : (
    <></>
  )
}
