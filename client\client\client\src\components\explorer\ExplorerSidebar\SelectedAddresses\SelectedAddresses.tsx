import { FilePdfOutlined } from '@ant-design/icons'
import { List } from 'antd'
import classNames from 'classnames'
import type React from 'react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { usePrevious } from 'react-use'
import ExplorerSelectedItemDetail from '@components/addresses/AddressDetailCard'
import { LinkButton } from '@components/generic'
import { truthy } from '@util/guards'
import { SelectedAddressItem } from '../SelectedAddressItem'
import SelectedTabButtons from '../SelectedTabButtons'
import styles from './SelectedAddresses.module.scss'

interface SelectedAddressesProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
  addressIds?: string[]
}

const SelectedAddresses = ({
  addressIds = [],
  ...divProps
}: SelectedAddressesProps) => {
  const previous = usePrevious(addressIds)

  const listEndRef = useRef<HTMLDivElement>(null)

  const lastId = useMemo(() => addressIds[addressIds.length - 1], [addressIds])
  const previousIds = useMemo(() => previous ?? [], [previous])

  const [detailId, setDetailId] = useState(lastId)

  useEffect(() => {
    setDetailId(lastId)
  }, [lastId])

  // biome-ignore lint/correctness/useExhaustiveDependencies:
  useEffect(() => {
    if (listEndRef.current && addressIds.length > previousIds.length) {
      listEndRef.current.scrollIntoView()
    }
  }, [addressIds, previousIds, lastId, detailId])

  return (
    <div
      className={classNames('SelectedAddresses', styles.container)}
      {...divProps}
    >
      <SelectedTabButtons layer="address">
        <LinkButton
          href={`/addresses/pdf/line?ids=${addressIds.join(',')}`}
          target="_blank"
          rel="noopener"
          icon={<FilePdfOutlined />}
          size="small"
        >
          Export to PDF
        </LinkButton>
      </SelectedTabButtons>
      {addressIds.length ? (
        <div className={styles.addresses}>
          <List size="small" className={styles.list}>
            {addressIds.filter(truthy).map((addressId: string, index) => {
              return (
                <SelectedAddressItem
                  key={`selected-address-item-${addressId}`}
                  index={index}
                  isNew={
                    !!(
                      previousIds.length < addressIds.length &&
                      addressId === addressIds[addressIds.length - 1]
                    )
                  }
                  addressId={Number(addressId)}
                  setSelected={setDetailId}
                />
              )
            })}
            <div ref={listEndRef} />
          </List>
        </div>
      ) : (
        <p style={{ textAlign: 'center' }}>
          No properties have been selected yet...
        </p>
      )}
      <ExplorerSelectedItemDetail id={detailId} />
    </div>
  )
}

export default SelectedAddresses
