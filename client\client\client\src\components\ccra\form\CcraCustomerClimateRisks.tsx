import { Button, Checkbox, Col, Form, Input, Radio, Row, message } from 'antd'
import { useMemo } from 'react'
import EditableTableFieldPhysicalRisks from './CustomFields/EditableTableFieldPhysicalRisks'
import EditableTableFieldTransitionRisks from './CustomFields/EditableTableFieldTransitionRisks'
import FormItems from './FormItems'
import {
  getFieldName,
  getLabelForField,
  validateTransitionRisksValue,
  handleCopyRisksToClipboard,
} from './helpers'
import { CopyOutlined } from '@ant-design/icons'
import { ROW_PROPS } from './const'
import type { CcraFormItem } from './types'

const CustomerClimateRisks = () => {
  const form = Form.useFormInstance()
  const hasAssessedClimateTransitionRisksValue = Form.useWatch(
    getFieldName('hasAssessedClimateTransitionRisks'),
    form
  )
  const plansToAssessClimateTransitionRisksValue = Form.useWatch(
    getFieldName('plansToAssessClimateTransitionRisks'),
    form
  )
  const hasAssessedPhysicalRisksValue = Form.useWatch(
    getFieldName('hasAssessedPhysicalRisks'),
    form
  )
  const plansToAssessPhysicalRisksValue = Form.useWatch(
    getFieldName('plansToAssessPhysicalRisks'),
    form
  )
  const physicalRisksValue = Form.useWatch(getFieldName('physicalRisks'), form)
  const transitionRisksValue = Form.useWatch(
    getFieldName('transitionRisks'),
    form
  )

  const climateRiskFormItems: CcraFormItem[] = useMemo(
    () => [
      { heading: 'Customer Climate Risks' },
      {
        paragraph:
          'Climate related risks are potential changes in the foreseeable future that could have financial impacts on customers.',
      },
      {
        subHeading: 'Transition Risks',
        tooltip:
          'Transition risks are those associated with the pace and extent at which an organization manages and adapts to the internal and external pace of change to reduce greenhouse gas emissions and transition to renewable energy.',
      },
      {
        name: getFieldName('hasAssessedClimateTransitionRisks'),
        label: getLabelForField('hasAssessedClimateTransitionRisks'),
        children: (
          <Radio.Group buttonStyle="solid">
            <Radio.Button value={true}>Yes</Radio.Button>
            <Radio.Button value={false}>No</Radio.Button>
          </Radio.Group>
        ),
      },
      {
        name: getFieldName('plansToAssessClimateTransitionRisks'),
        label: getLabelForField('plansToAssessClimateTransitionRisks'),
        children: (
          <Radio.Group
            buttonStyle="solid"
            disabled={hasAssessedClimateTransitionRisksValue}
          >
            <Radio.Button value={true}>Yes</Radio.Button>
            <Radio.Button value={false}>No</Radio.Button>
          </Radio.Group>
        ),
        required: !hasAssessedClimateTransitionRisksValue,
      },
      {
        name: getFieldName('transitionRisks'),
        label: getLabelForField('transitionRisks'),
        children: (
          <EditableTableFieldTransitionRisks
            fieldName={getFieldName('transitionRisks')}
          />
        ),
        disabled: !hasAssessedClimateTransitionRisksValue,
      },
      {
        name: getFieldName('confirmtransitionRisks'),
        label: '',
        children: <Input type="hidden" style={{ height: 0 }} />,
        // hidden input that prevents validation if the transition risk options
        // have not all been selected
        disabled: validateTransitionRisksValue(transitionRisksValue),
        customRuleMessage: 'All material to customer options must be selected',
      },
      {
        children: (
          <Button
            type="primary"
            onClick={() =>
              handleCopyRisksToClipboard(
                hasAssessedClimateTransitionRisksValue,
                plansToAssessClimateTransitionRisksValue,
                transitionRisksValue,
                getLabelForField,
                'Transition'
              )
            }
            style={{ marginBottom: 28 }}
          >
            Copy Transition Risks <CopyOutlined />
          </Button>
        ),
        customField: true,
        disabled: !hasAssessedClimateTransitionRisksValue,
      },
      {
        subHeading: 'Physical Risks',
        tooltip: `Physical risks resulting from climate change can be acute (driven by an event such as a flood or storm)
          or chronic (arising from longer-term shifts in climate patterns), presenting increasing financial risks including
          damage to assets, interruption of operations, and disruption to supply chains.`,
      },
      {
        name: getFieldName('hasAssessedPhysicalRisks'),
        label: getLabelForField('hasAssessedPhysicalRisks'),
        children: (
          <Radio.Group buttonStyle="solid">
            <Radio.Button value={true}>Yes</Radio.Button>
            <Radio.Button value={false}>No</Radio.Button>
          </Radio.Group>
        ),
      },
      {
        name: getFieldName('plansToAssessPhysicalRisks'),
        label: getLabelForField('plansToAssessPhysicalRisks'),
        children: (
          <Radio.Group
            buttonStyle="solid"
            disabled={hasAssessedPhysicalRisksValue}
          >
            <Radio.Button value={true}>Yes</Radio.Button>
            <Radio.Button value={false}>No</Radio.Button>
          </Radio.Group>
        ),
        required: !hasAssessedPhysicalRisksValue,
      },
      {
        name: getFieldName('physicalRisks'),
        label: getLabelForField('physicalRisks'),
        children: (
          <EditableTableFieldPhysicalRisks
            fieldName={getFieldName('physicalRisks')}
          />
        ),
        disabled: !hasAssessedPhysicalRisksValue,
      },
      {
        name: getFieldName('confirmphysicalRisks'),
        label: '',
        children: <Input type="hidden" style={{ height: 0 }} />,
        // hidden input that prevents validation if the transition risk options
        // have not all been selected
        disabled: validateTransitionRisksValue(physicalRisksValue),
        customRuleMessage: 'All material to customer options must be selected',
      },
      {
        children: (
          <Button
            type="primary"
            onClick={() => {
              handleCopyRisksToClipboard(
                hasAssessedPhysicalRisksValue,
                plansToAssessPhysicalRisksValue,
                physicalRisksValue,
                getLabelForField,
                'Physical',
                true
              )
            }}
          >
            Copy Physical Risks <CopyOutlined />
          </Button>
        ),
        customField: true,
        disabled: !hasAssessedPhysicalRisksValue,
      },
    ],
    [
      hasAssessedClimateTransitionRisksValue,
      transitionRisksValue,
      hasAssessedPhysicalRisksValue,
      physicalRisksValue,
      plansToAssessClimateTransitionRisksValue,
      plansToAssessPhysicalRisksValue,
    ]
  )

  return (
    <Row {...ROW_PROPS}>
      <Col span={24}>
        <FormItems items={climateRiskFormItems} />
      </Col>
    </Row>
  )
}

export default CustomerClimateRisks
