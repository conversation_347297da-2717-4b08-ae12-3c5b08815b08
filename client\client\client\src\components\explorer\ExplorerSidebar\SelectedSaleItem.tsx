import { CloseOutlined, CompassOutlined, MoreOutlined } from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Button, type ButtonProps, Dropdown, List, Skeleton } from 'antd'
import classNames from 'classnames'
import type { LatLngExpression } from 'leaflet'
import type React from 'react'
import { useLeaflet } from 'react-leaflet'
import { useDispatch } from 'react-redux'
import ExplorerSelectedListingMenu from '@components/explorer/ExplorerSelectedListingMenu'
import ExplorerSelectedSaleMenu from '@components/explorer/ExplorerSelectedSaleMenu'
import { useGetSaleQuery } from '@store/services/sale'
import {
  toggleSelectedListingId,
  toggleSelectedSaleId,
} from '@store/ui/actions'
import { isLatLngTuple } from '@util/guards'
import { formatAddress } from '@util/labels'
import ExplorerLayerStyleSelectionButton from '../ExplorerLayerStyleSelectionButton'
import styles from './SelectedAddressItem.module.scss'

interface SelectedSaleItemProps extends React.HTMLAttributes<HTMLElement> {
  saleId: number
  position: number
  saleType: string
}

const ItemButton = ({
  children,
  onClick,
  ...props
}: Omit<ButtonProps, 'size'>) => {
  const clickHandler: ButtonProps['onClick'] = (e) => {
    e.stopPropagation()
    if (onClick) onClick(e)
  }

  return (
    <Button type="text" {...props} size="small" onClick={clickHandler}>
      {children}
    </Button>
  )
}

export const SelectedSaleItem = ({
  saleId,
  position,
  saleType,
  ...props
}: SelectedSaleItemProps) => {
  const dispatch = useDispatch()
  const { map } = useLeaflet()

  const { data: sale } = useGetSaleQuery(saleId?.toString() ?? skipToken)

  function handleZoom() {
    if (sale) {
      const coordinates = [...sale.geometry.coordinates].reverse()
      if (!isLatLngTuple(coordinates)) return
      map?.setView(coordinates as LatLngExpression, 15.5)
    }
  }

  function handleRemove() {
    if (saleType === 'LISTING') {
      dispatch(toggleSelectedListingId(String(sale?.id)))
    } else {
      dispatch(toggleSelectedSaleId(String(sale?.id)))
    }
  }

  const isListing = saleType === 'LISTING'

  if (!sale) return null
  return (
    <List.Item
      {...props}
      className={classNames('SelectedAddressItem', styles.container)}
    >
      <>
        {sale ? (
          <div className={styles.content}>
            <ExplorerLayerStyleSelectionButton
              context="address"
              id={saleId}
              index={position}
            />
            <ItemButton
              type="default"
              icon={<CompassOutlined />}
              onClick={handleZoom}
            />
            <span className={styles.label}>
              {formatAddress(sale.properties.fullAddress ?? undefined, {
                length: 45,
              })}
            </span>
            <Dropdown
              overlay={
                isListing ? (
                  <ExplorerSelectedListingMenu id={Number(sale.id)} />
                ) : (
                  <ExplorerSelectedSaleMenu id={Number(sale.id)} />
                )
              }
              placement="bottomLeft"
              trigger={['click']}
            >
              <ItemButton icon={<MoreOutlined />} />
            </Dropdown>
          </div>
        ) : (
          <Skeleton.Input size="small" className={styles.skeleton} />
        )}
        <ItemButton icon={<CloseOutlined />} onClick={handleRemove} />
      </>
    </List.Item>
  )
}
