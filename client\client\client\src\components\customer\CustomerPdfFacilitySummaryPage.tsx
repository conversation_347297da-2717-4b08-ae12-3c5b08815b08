/*
import { Text, View } from '@react-pdf/renderer'
import { useMemo } from 'react'
import { PdfPage, PdfSection, PdfTable, stylesheet } from '@components/pdf'
import { SPACE } from '@components/pdf/stylesheet'
import useFacilitySummary from '@store/hooks/useFacilitySummary'
import { formatDollarValue } from '@util'
import CustomerFacilityAmortisationGraph from './CustomerFacilityAmortisationGraph'
import CustomerFacilityFundingGraph from './CustomerFacilityFundingGraph'
import CustomerFacilityMaturityGraph from './CustomerFacilityMaturityGraph'
import { type PdfGraphDefinition, renderGraphs } from './helpers'

type Props = { components: string[] } & { facilities: ExtendedFacility[] }

const CustomerPdfFacilitySummaryPage = ({ components, facilities }: Props) => {
  const { fundingSummary, maturitySummary, amortisationSummary } =
    useFacilitySummary(facilities)

  const loanGraphs = useMemo(
    () => [
      components.includes('MATURITY') && {
        elem: (
          <CustomerFacilityMaturityGraph
            maturitySummary={maturitySummary}
            print={true}
          />
        ),
        key: 'customer-facilities-maturity-profile',
        name: 'ANZ Facility Maturity Profile',
        description:
          'This section presents a representation of your maturing debt over time using a bar graph. It provides insights into the amount of debt scheduled to mature in each year, offering a clear understanding of your upcoming repayment obligations.',
      },
      components.includes('AMORTISATION') && {
        elem: (
          <CustomerFacilityAmortisationGraph
            amortisationSummary={amortisationSummary}
            print={true}
          />
        ),
        key: 'customer-facilities-amorisation-profile',
        name: 'ANZ Facility Amortisation Profile',
        description:
          'This section aims to illustrate the aggregate balance over time for different types of facilities: green loans, revolving credit, and standard loans. It provides a comprehensive view of how the balances of these loan types have evolved over the given time period.',
      },
      components.includes('FUNDING') && {
        elem: (
          <CustomerFacilityFundingGraph
            fundingSummary={fundingSummary}
            print={true}
          />
        ),
        key: 'customer-facilities-funding-profile',
        name: 'ANZ Facility Funding Profile',
        description:
          'This section presents the distribution of debt among different loan types: green loans, revolving credit, and standard loans. It offers a visual representation of the proportionate share of each loan type within your total debt.',
      },
    ],
    [amortisationSummary, fundingSummary, maturitySummary, components]
  ).filter((x) => x) as PdfGraphDefinition[]

  if (
    !['FACILITIES', 'MATURITY', 'FUNDING', 'AMORTISATION'].some((x) =>
      components.includes(x)
    )
  ) {
    return <></>
  }

  return (
    <PdfPage>
      {components.includes('FACILITIES') && (
        <PdfSection key={'customer-facilities'}>
          <Text style={stylesheet.headingL}>ANZ Facility Summary</Text>
          <View>
            <Text style={stylesheet.body}>
              This section provides an overview of your financial facilities and
              loans. It includes a table that summarizes important information
              about each facility, such as the loan product, maturity date,
              interest rate, and current balance/limit.
            </Text>
          </View>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: SPACE[6],
              marginBottom: SPACE[4],
            }}
          >
            <View style={{ flex: 1 }}>
              <PdfTable
                columns={[
                  {
                    key: 'entityName',
                    title: 'Customer',
                  },
                  {
                    key: 'productName',
                    title: 'Product',
                  },
                  {
                    key: 'maturityDate',
                    title: 'Maturity Date',
                  },
                  {
                    key: 'interestRate',
                    title: 'Interest Rate',
                  },
                  {
                    key: 'limit',
                    title: 'Balance / Limit',
                  },
                ]}
                rows={facilities.map(
                  ({
                    entityName,
                    interestRate,
                    productName,
                    maturityDate,
                    limit,
                    accountType,
                  }) => {
                    return {
                      entityName,
                      interestRate: interestRate
                        ? `${Number(interestRate).toFixed(2)}%`
                        : '',
                      productName,
                      maturityDate:
                        accountType === 'Limit' ? 'On Demand' : maturityDate,
                      limit: formatDollarValue(Number(limit)),
                    }
                  }
                )}
              />
            </View>
          </View>
        </PdfSection>
      )}
      {renderGraphs(loanGraphs)}
    </PdfPage>
  )
}

export default CustomerPdfFacilitySummaryPage
*/
