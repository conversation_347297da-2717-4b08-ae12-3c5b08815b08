import { Skeleton } from 'antd'
import type React from 'react'
import type { ReactNode } from 'react'
import esgisLogo from '../../../img/esgis-logo-negative.svg'
import { ExportPage } from '../components/ExportPage'
import styles from './CoverPage.module.scss'

interface CoverPageProps {
  title: string
  subtitle?: string | ReactNode | ReactNode[]
  classification?: ReactNode
  children?: ReactNode[] | ReactNode
  isFetching?: boolean
}

interface CoverPageWrapperProps {
  title: string
  children: ReactNode[] | ReactNode
  classification?: ReactNode
  isFetching?: boolean
}

export const ExportCoverPageWrapper = ({
  children,
}: {
  children: ReactNode | ReactNode[]
}) => {
  return <div className={styles.ExportCoverPageWrapper}>{children}</div>
}

export const ExportCoverPageContent = ({
  children,
}: {
  children: ReactNode | ReactNode[]
}) => {
  return <div className={styles.ExportCoverPageContent}>{children}</div>
}

export const ExportCoverPageDate = ({
  children,
}: {
  children?: ReactNode | ReactNode[]
}) => {
  return (
    <div className={styles.ExportCoverPageDate}>
      {children}
      {new Date().toDateString()}
    </div>
  )
}

export const ExportCoverPageTitle = ({
  children,
}: {
  children: ReactNode | ReactNode[]
}) => {
  return <div className={styles.ExportCoverPageTitle}>{children}</div>
}

export const ExportCoverPageCornerSplash = ({
  children,
}: {
  children?: ReactNode | ReactNode[]
}) => {
  return <div className={styles.ExportCoverPageCornerSplash}>{children}</div>
}

const ExportCoverPageClassification = ({
  classification,
}: {
  classification: React.ReactNode
}) => {
  return (
    <div className={styles.ExportCoverPageClassification}>{classification}</div>
  )
}

export const CoverPageWrapper = (props: CoverPageWrapperProps) => {
  if (props.isFetching) {
    return (
      <ExportCoverPageWrapper>
        <Skeleton />
      </ExportCoverPageWrapper>
    )
  }

  return (
    <ExportCoverPageWrapper>
      <ExportCoverPageTitle>{props.title}</ExportCoverPageTitle>
      <ExportCoverPageContent>{props.children}</ExportCoverPageContent>
      <ExportCoverPageDate />
      <CoverPageLogo />
      {props.classification ? (
        <ExportCoverPageClassification classification={props.classification} />
      ) : null}
      <ExportCoverPageCornerSplash />
    </ExportCoverPageWrapper>
  )
}

export const CoverPageLogo = () => {
  return (
    <div className="ExportCoverPageLogo">
      <img height={48} src={esgisLogo} alt="esgis logo negative" />
    </div>
  )
}

export const CoverPage = (props: CoverPageProps) => {
  return (
    <ExportPage variant="cover">
      <CoverPageWrapper
        isFetching={props.isFetching}
        title={props.title}
        classification={props.classification}
      >
        {props.subtitle}
        {props.children}
      </CoverPageWrapper>
    </ExportPage>
  )
}
