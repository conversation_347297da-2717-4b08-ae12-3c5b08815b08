.container {
  --inset: var(--space-3);

  position: relative;
}

.button {
  composes: interactive from "@styles/util.module.css";
}

.arrow {
  color: var(--color-secondary);
  font-size: 0.725em;
  vertical-align: middle;
  opacity: 0.6;
}

.overlay {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1;
}

.list {
  width: max-content;
  margin-top: var(--space-0);
  padding: var(--space-1) var(--inset);
  transform: translateX(calc(-1 * var(--inset)));
  background: var(--color-background);
  /*background: var(--color-dark);*/
  border-radius: var(--space-0);
  border: 1px solid var(--color-border);
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);

  a {
    /*color: var(--color-inverse);*/
  }
}

.container:not(:hover) .list {
  display: none;
}
