import type React from 'react'
import { ContentSubtitle } from '../../ExportSection'

interface ExportFormSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
  title?: string
}

const ExportFormSection = (props: ExportFormSectionProps) => {
  const { children, title, ...divProps } = props
  return (
    <div className="ExportFormSection" {...divProps}>
      {title ? <ContentSubtitle>{title}</ContentSubtitle> : null}
      {props?.children}
    </div>
  )
}

export default ExportFormSection
