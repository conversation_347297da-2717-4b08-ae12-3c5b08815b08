import type { FinanceAgriUpliftListApiArg } from '@store/services/finance/codegen'
import { isFunction } from 'lodash'
import { useCallback, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'

export const CONFIG: SearchParamConfig<FinanceAgriUpliftListApiArg> = {
  creator: 'array',
  currentUser: 'value',
  orderBy: 'array',
  page: 'value',
  reportType: 'array',
  size: 'value',
  status: 'array',
  tradingGroup: 'value',
  tradingGroupRegion: 'array',
}

const toSearchParams = <T extends Record<string, unknown>>(
  state: T
): URLSearchParams => {
  const searchParams = new URLSearchParams()
  for (const [key, value] of Object.entries(state)) {
    if (Array.isArray(value)) {
      for (const v of value) {
        if (v != null) {
          searchParams.append(key, v.toString())
        }
      }
    } else {
      if (value != null) {
        searchParams.append(key, value.toString())
      }
    }
  }
  return searchParams
}

type SearchParamConfig<T> = Record<keyof T, 'array' | 'value'>

const fromSearchParams = <T extends Record<string, unknown>>(
  searchParams: URLSearchParams,
  config: SearchParamConfig<T>
): T => {
  const state: Record<string, unknown | unknown[]> = {}
  for (const [key, value] of searchParams.entries()) {
    if (config[key] === 'array') {
      if (!state[key]) {
        state[key] = [value]
      } else {
        ;(state[key] as unknown[]).push(value)
      }
    } else {
      state[key] = value
    }
  }
  return state as T
}

const useAgriUpliftTableFiltersSearchParams = (
  initialState?: FinanceAgriUpliftListApiArg
): [
  FinanceAgriUpliftListApiArg,
  (
    state:
      | FinanceAgriUpliftListApiArg
      | ((
          prevState: FinanceAgriUpliftListApiArg
        ) => FinanceAgriUpliftListApiArg)
  ) => void,
] => {
  const [searchParams, setSearchParams] = useSearchParams(
    initialState ? toSearchParams(initialState) : undefined
  )
  const setFilterState = useCallback(
    (
      state:
        | FinanceAgriUpliftListApiArg
        | ((
            prevState: FinanceAgriUpliftListApiArg
          ) => FinanceAgriUpliftListApiArg)
    ) => {
      if (isFunction(state)) {
        setSearchParams((prev: URLSearchParams) => {
          const prevState = fromSearchParams<FinanceAgriUpliftListApiArg>(
            prev,
            CONFIG
          )
          const updatedState = toSearchParams(state(prevState))
          return updatedState
        })
      } else {
        setSearchParams(toSearchParams(state))
      }
    },
    [setSearchParams]
  )

  const state = useMemo(() => {
    return fromSearchParams(searchParams, CONFIG)
  }, [searchParams])

  return [state, setFilterState]
}

export default useAgriUpliftTableFiltersSearchParams
