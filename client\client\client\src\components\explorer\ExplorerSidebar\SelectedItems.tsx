import { Tabs, Typography } from 'antd'
import React, { type ReactNode, useEffect } from 'react'
import { useLocalStorage, usePrevious } from 'react-use'
import { SalesSummaryModal } from '@components/map/modals/SalesSummaryModal'
import { type RootState, useSelector } from '@store'
import { getSelectedIdsByType } from '@store/ui/selectors'
import { SelectedAddresses } from './SelectedAddresses'
import { SelectedListings } from './SelectedListings'
import { SelectedSales } from './SelectedSales'
import { SelectedValocityListings } from './SelectedValocityListings'
import { SelectedValocitySales } from './SelectedValocitySales'

const selector = (state: RootState) => {
  return {
    addressIds: getSelectedIdsByType('addressIds')(state),
    saleIds: getSelectedIdsByType('saleIds')(state),
    listingIds: getSelectedIdsByType('listingIds')(state),
    valocitySaleIds: getSelectedIdsByType('valocitySaleIds')(state),
    valocityListingIds: getSelectedIdsByType('valocityListingIds')(state),
  }
}

function useSelectedIds() {
  const current = useSelector(selector)
  const previous = usePrevious(current)

  const [selectedTab, setSelectedTab] = useLocalStorage(
    'explorerSidebarTab',
    'addressIds'
  )

  useEffect(() => {
    if (!previous) return
    for (const [key, currentIds] of Object.entries(current)) {
      const previousIds = previous[key as keyof typeof previous]
      if (previousIds && currentIds.length > previousIds.length) {
        setSelectedTab(key)
      }
    }
  }, [current, previous, setSelectedTab])

  return {
    ...current,
    selectedTab,
    setSelectedTab,
  }
}

const TabLabel = ({ label, count }: { label: ReactNode; count?: number }) => (
  <>
    {label}{' '}
    <Typography.Text
      type="secondary"
      style={{ fontSize: '0.8em', verticalAlign: 'super', opacity: count }}
    >
      {count}
    </Typography.Text>
  </>
)

const SelectedItems = () => {
  const {
    addressIds,
    saleIds,
    listingIds,
    valocitySaleIds,
    valocityListingIds,
    selectedTab,
    setSelectedTab,
  } = useSelectedIds()

  // This will need to be updated to use the items prop on tabs which will clean everything up a bit by iterating over the keys

  return (
    <>
      <SalesSummaryModal />
      <Tabs
        size="small"
        type="card"
        activeKey={selectedTab}
        onChange={setSelectedTab}
        style={{ height: '100%' }}
      >
        <Tabs.TabPane
          tab={<TabLabel label="Properties" count={addressIds.length} />}
          key="addressIds"
        >
          <SelectedAddresses addressIds={addressIds} />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={<TabLabel label="Sales" count={saleIds.length} />}
          key="saleIds"
        >
          <SelectedSales saleIds={saleIds} />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={<TabLabel label="Listings" count={listingIds.length} />}
          key="listingIds"
        >
          <SelectedListings listingIds={listingIds} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="Valocity Sales" key="valocitySaleIds">
          <SelectedValocitySales valocitySaleIds={valocitySaleIds} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="Valocity Listings" key="valocityListingIds">
          <SelectedValocityListings valocityListingIds={valocityListingIds} />
        </Tabs.TabPane>
      </Tabs>
    </>
  )
}

export default SelectedItems
