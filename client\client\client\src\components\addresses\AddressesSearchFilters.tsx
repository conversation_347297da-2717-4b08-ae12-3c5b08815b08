import { DeleteOutlined, FilterOutlined } from '@ant-design/icons'
import { Button, Input, Select, Space, message } from 'antd'
import _ from 'lodash'
import React, { useCallback, useEffect, useState } from 'react'
import { shallowEqual, useDispatch, useSelector } from 'react-redux'
import { VALOCITY_USE_OPTIONS } from '@util/const'
import { ADDRESS_SEARCH_PLACEHOLDER } from '@util/language'
import type { RootState } from '../../store'
import { uiSelectors } from '../../store/ui'
import { clearFilter, setFilterValue } from '../../store/ui/actions'
import { useDebounce } from '../../util/useDebounce'
import { ButtonWidget } from '../generic'
import { SafeNumberInput } from '../generic/SafeNumberInput'
import { Widget } from '../generic/Widget'
import { REGION_OPTIONS } from '../sales/generic/SaleForm'

const LAND_ZONE_OPTIONS = [
  { option: 'COMMERCIAL', value: 'COMMERCIAL' },
  { option: 'COMMUNITY USES', value: 'COMMUNITY USES' },
  { option: 'DESIGNATED RESERVED LAND', value: 'DESIGNATED RESERVED LAND' },
  { option: 'INDUSTRIAL', value: 'INDUSTRIAL' },
  {
    option: 'LAND IN MORE THAN ONE ZONE OR DESIGNATION',
    value: 'LAND IN MORE THAN ONE ZONE OR DESIGNATION',
  },
  { option: 'LIFESTYLE', value: 'LIFESTYLE' },
  { option: 'RECREATIONAL', value: 'RECREATIONAL' },
  { option: 'RESIDENTIAL', value: 'RESIDENTIAL' },
  { option: 'RURAL', value: 'RURAL' },
]

const selector = (state: RootState) => {
  return {
    filterState: uiSelectors.getFilterState(state, 'addressDashboard'),
  }
}

export const AddressesSearchFilters = () => {
  const { filterState } = useSelector(selector, shallowEqual)

  const dispatch = useDispatch()

  const filterDispatch = useCallback(
    (payload: { type: string; value: unknown }) => {
      dispatch(
        setFilterValue({
          pageName: 'addressDashboard',
          filterKey: payload?.type,
          filterValue: payload?.value,
        })
      )
    },
    [dispatch]
  )

  const [match, setMatch] = useState<string>()
  const debouncedMatch = useDebounce(match, 200)

  useEffect(() => {
    filterDispatch({ type: 'match', value: debouncedMatch })
  }, [debouncedMatch, filterDispatch])

  const filterDispatcher = useCallback(
    <T,>(type: string) =>
      (value?: T) => {
        filterDispatch({ type, value })
      },
    [filterDispatch]
  )

  return (
    <Widget
      title="Filters"
      icon={<FilterOutlined />}
      extra={
        <ButtonWidget>
          <Button
            type="default"
            icon={<DeleteOutlined />}
            onClick={() => {
              setMatch('')
              dispatch(clearFilter({ pageName: 'addressDashboard' }))
              void message.success('Settings successfully cleared.')
            }}
          >
            Reset Filters
          </Button>
        </ButtonWidget>
      }
    >
      <div className="agrigis-filters">
        <div className="control-container select">
          <span className="control-name">Search Term</span>
          <Input
            value={match}
            placeholder={
              _.get(filterState, 'match')
                ? _.get(filterState, 'match')
                : ADDRESS_SEARCH_PLACEHOLDER
            }
            onChange={(e) => {
              setMatch(e.target.value)
            }}
          />
        </div>
      </div>
      <div className="agrigis-filters">
        <div className="control-container select">
          <span className="control-name">Highest & Best Use</span>
          <Select
            placeholder="Select one or more..."
            size="small"
            mode="multiple"
            defaultValue={[]}
            options={VALOCITY_USE_OPTIONS}
            onChange={filterDispatcher<string[]>('bestUses')}
            value={_.get(filterState, 'bestUses')}
          />
        </div>
        <div className="control-container select">
          <span className="control-name">TLA/District</span>
          <Select
            placeholder="Select one or more..."
            size="small"
            mode="multiple"
            defaultValue={[]}
            options={REGION_OPTIONS}
            onChange={filterDispatcher<string[]>('regions')}
            value={_.get(filterState, 'regions')}
          />
        </div>
        <div className="control-container select">
          <span className="control-name">Land Zone</span>
          <Select
            placeholder="Select one or more..."
            size="small"
            mode="multiple"
            defaultValue={[]}
            options={LAND_ZONE_OPTIONS}
            onChange={filterDispatcher<string[]>('landZones')}
            value={_.get(filterState, 'landZones')}
          />
        </div>
      </div>
      <div className="agrigis-filters">
        <div className="control-container select">
          <span className="control-name">Capital Value ($K)</span>
          <Space>
            <SafeNumberInput
              size="small"
              prefix=">="
              value={_.get(filterState, 'minCv')}
              callback={filterDispatcher<number>('minCv')}
            />
            <SafeNumberInput
              size="small"
              prefix="<="
              value={_.get(filterState, 'maxCv')}
              callback={filterDispatcher<number>('maxCv')}
            />
          </Space>
        </div>
        <div className="control-container select">
          <span className="control-name">Land ($K)</span>
          <Space>
            <SafeNumberInput
              size="small"
              prefix=">="
              value={_.get(filterState, 'minLv')}
              callback={filterDispatcher<number>('minLv')}
            />
            <SafeNumberInput
              size="small"
              prefix="<="
              value={_.get(filterState, 'maxLv')}
              callback={filterDispatcher<number>('maxLv')}
            />
          </Space>
        </div>
        <div className="control-container select">
          <span className="control-name">Improvements ($K)</span>
          <Space>
            <SafeNumberInput
              size="small"
              prefix=">="
              value={_.get(filterState, 'minIv')}
              callback={filterDispatcher<number>('minIv')}
            />
            <SafeNumberInput
              size="small"
              prefix="<="
              value={_.get(filterState, 'maxIv')}
              callback={filterDispatcher<number>('maxIv')}
            />
          </Space>
        </div>
      </div>
      <div className="agrigis-filters">
        <div className="control-container select">
          <span className="control-name">Floor Area (Ha)</span>
          <Space>
            <SafeNumberInput
              size="small"
              prefix=">="
              value={_.get(filterState, 'minFloorArea')}
              callback={filterDispatcher<number>('minFloorArea')}
            />
            <SafeNumberInput
              size="small"
              prefix="<="
              value={_.get(filterState, 'maxFloorArea')}
              callback={filterDispatcher<number>('maxFloorArea')}
            />
          </Space>
        </div>
        <div className="control-container select">
          <span className="control-name">Land Area (Ha)</span>
          <Space>
            <SafeNumberInput
              size="small"
              prefix=">="
              value={_.get(filterState, 'minLandArea')}
              callback={filterDispatcher<number>('minLandArea')}
            />
            <SafeNumberInput
              size="small"
              prefix="<="
              value={_.get(filterState, 'maxLandArea')}
              callback={filterDispatcher<number>('maxLandArea')}
            />
          </Space>
        </div>
      </div>
    </Widget>
  )
}
