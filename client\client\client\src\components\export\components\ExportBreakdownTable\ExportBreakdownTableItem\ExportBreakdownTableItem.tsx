import type React from 'react'

interface ExportBreakdownTableItemProps
  extends React.HTMLAttributes<HTMLDivElement> {
  label: string | React.ReactNode | React.ReactNode[]
  value?: number | undefined
  secondaryLabel?: string
}

const ExportBreakdownTableItem = (props: ExportBreakdownTableItemProps) => {
  const { label, value, secondaryLabel, ...divProps } = props
  return (
    <div className="ExportBreakdownTableItem" {...divProps}>
      <div className="export-breakdown-table-item-label">{label}</div>
      <div className="export-breakdown-table-item-value">
        {secondaryLabel
          ? secondaryLabel
          : value === undefined
            ? '*'
            : `$${(value ?? 0)?.toLocaleString('en-NZ', {
                maximumFractionDigits: 2,
              })}`}
      </div>
    </div>
  )
}

export default ExportBreakdownTableItem
