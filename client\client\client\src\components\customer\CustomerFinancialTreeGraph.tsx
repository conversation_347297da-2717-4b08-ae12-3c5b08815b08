/**
 * Don't confuse this with the KPI graph which shows a dual axes.
 * I might need to rename these to be specific graphs.
 */
import { Treemap } from '@ant-design/charts'
import type React from 'react'
import { useMemo } from 'react'
import type { Kpi } from '@store/services/sdk'
import { formatDollarValue } from '@util'
import type { DataVisualisationItem } from '@util/types'
import { measuresMap } from './static'

type Color = [number, number, number, number]
const getColors = (start: Color, end: Color, steps: number) => {
  const [rX, gX, bX, aX] = start
  const [rY, gY, bY, aY] = end
  const rD = (rY - rX) / steps
  const gD = (gY - gX) / steps
  const bD = (bY - bX) / steps

  const arr = []
  for (let i = 1; i < steps + 1; i++) {
    const r = (rX + rD * i).toFixed()
    const g = (gX + gD * i).toFixed()
    const b = (bX + bD * i).toFixed()
    const a = aX || aY
    arr.push(`rgb(${r}, ${g}, ${b}, ${a})`)
  }

  return arr
}

const calculateAverage = (
  arr: ({ value: number } & Pick<Kpi, 'measure'>)[]
) => {
  const map = new Map()

  // Group values by measure
  for (const item of arr) {
    if (map.has(item.measure)) {
      const { total, count } = map.get(item.measure)
      map.set(item.measure, {
        total: (total as number) + item.value,
        count: (count as number) + 1,
      })
    } else {
      map.set(item.measure, { total: item.value, count: 1 })
    }
  }

  // Calculate average for each measure
  const result: typeof arr = []
  let numYears = 0
  for (const obj of map.values()) {
    if (obj.count > numYears) {
      numYears = obj.count
    }
  }
  map.forEach((value, measure) => {
    const average = value.total / numYears
    result.push({ measure, value: average })
  })

  return result
}

type Props = {
  print?: boolean
  data: (Kpi | DataVisualisationItem)[]
  measures: string[]
  segment: string
} & Omit<React.ComponentProps<typeof Treemap>, 'data' | 'xField' | 'yField'>

const CustomerFinancialTreeGraph = ({
  data,
  print,
  measures,
  segment,
  ...props
}: Props) => {
  const tree = useMemo(() => {
    let _arr: ({ value: number; year: number } & Pick<Kpi, 'measure'>)[] = []

    for (const k of measures) {
      _arr = [
        ..._arr,
        ...data
          .filter(({ measure }) => measure === k)
          .map(({ value, measure, periodTo }) => ({
            value: Number(value),
            measure: measuresMap[segment][measure],
            year: new Date(periodTo).getFullYear(),
          })),
      ]
    }

    return {
      name: 'root',
      children: calculateAverage(_arr),
    }
  }, [data, measures, segment])

  return (
    <Treemap
      {...{
        data: tree,
        appendPadding: 10,
        legend: false,
        color: getColors(
          [0, 65, 101, 1],
          [231, 231, 231, 1],
          tree.children.length
        ),
        label: {
          formatter: (record) => {
            const { measure, value } = record as DataVisualisationItem
            return `${measure}\n${formatDollarValue(Number(value))}`
          },
        },
        tooltip: {
          formatter: (datum) => ({
            name: datum.measure ?? 'Customer',
            value: formatDollarValue(Number(datum.value ?? datum.customer)),
          }),
        },
        colorField: 'measure',
        ...(print && {
          animation: false,
          pixelRatio: 3.125,
          tooltip: false,
        }),
        ...props,
      }}
    />
  )
}

export default CustomerFinancialTreeGraph
