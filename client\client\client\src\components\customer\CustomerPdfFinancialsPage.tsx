import { View } from '@react-pdf/renderer'
import React from 'react'
import {
  PdfChartImage,
  PdfHeading,
  PdfPage,
  PdfTableHeading,
  rem,
} from '@components/pdf'
import PdfStackedChartLayout from '@components/pdf/PdfStackedChartLayout'
import {
  type CustomerFinancialsType,
  SCV_MODES,
  type ScvModeType,
} from '@store/features/customer'
import type { Kpi } from '@store/services/sdk'
import type { DataVisualisationItem } from '@util/types'
import CustomerPdfDefinitionsTable from './CustomerPdfDefinitionsTable'
import CustomerPdfDisclaimers from './CustomerPdfDisclaimers'
import CustomerPdfScvTable from './CustomerPdfScvTable'
import { kpiGraphHooks, kpiTableHooks } from './helpers'
import CustomerMfvGraph from './mfv/CustomerMfvGraph'
import { mfvAppendixCalculations } from './mfv/static'
import CreditorCashConversionDaysGraph from './scv/CreditorCashConversionDaysGraph'
import CreditorsCapitalGraph from './scv/CreditorsCapitalGraph'
import { SCV_CALCULATIONS } from './scv/CustomerScvAppendix'
import { getData as getCashConversionStats } from './scv/CustomerScvCashCoversionStats'
import CustomerScvGraph from './scv/CustomerScvGraph'
import { getData as getWorkingCapitalStats } from './scv/CustomerScvWorkingCapitalStats'
import DebtorInventoryDaysGraph from './scv/DebtorInventoryDaysGraph'
import ReceivablesInventoriesGraph from './scv/ReceivablesInventoriesGraph'
import {
  getCreditorCashConversionDaysArr,
  getCreditorsCapitalArr,
} from './scv/helpers'
import { scvAppendixCalculations, scvAppendixDefinitions } from './scv/static'
import { getCustomerFieldLabel } from './static'

// NOTE:
// The approach in this component and pdf could be simplified
// Most of the elements here are stand alone views that can be separated into page and section comoponents
// The same organisation should also be applied to the UI

const MfvGraph = CustomerMfvGraph
const ScvGraph = CustomerScvGraph

type Props = {
  kpis: Kpi[]
  selectedSections: CustomerFinancialsType[]
}

type ScvSectionProps = {
  breakPage?: boolean
  kpis: Kpi[]
  dataType: CustomerFinancialsType
}

const isScvMode = (dataType: CustomerFinancialsType): dataType is ScvModeType =>
  SCV_MODES.includes(dataType as ScvModeType)

const CashConversionCycleSection = ({ kpis, breakPage }: ScvSectionProps) => (
  <PdfStackedChartLayout
    title="Cash Conversion Cycle (Days)"
    stats={getCashConversionStats(getCreditorCashConversionDaysArr(kpis))}
    graph1={<DebtorInventoryDaysGraph kpis={kpis} />}
    graph2={<CreditorCashConversionDaysGraph kpis={kpis} />}
    breakPage={breakPage}
  />
)

const WorkingCapitalSection = ({ kpis, breakPage }: ScvSectionProps) => {
  return (
    <PdfStackedChartLayout
      title="Working Capital ($000s)"
      stats={getWorkingCapitalStats(getCreditorsCapitalArr(kpis))}
      graph1={<ReceivablesInventoriesGraph kpis={kpis} />}
      graph2={<CreditorsCapitalGraph kpis={kpis} />}
      breakPage={breakPage}
    />
  )
}

const CustomerScvSection = ({ breakPage, dataType, kpis }: ScvSectionProps) => {
  const showGraph = dataType in kpiGraphHooks
  const showTable = dataType in kpiTableHooks

  const graphData = kpiGraphHooks[dataType](kpis)
  const tableData = kpiTableHooks[dataType](kpis)

  if (dataType === 'cashConversionCycle')
    return <CashConversionCycleSection {...{ kpis, breakPage, dataType }} />

  if (dataType === 'workingCapital')
    return <WorkingCapitalSection {...{ kpis, breakPage, dataType }} />

  // mfv
  if (dataType === 'calculations')
    return (
      <>
        <PdfHeading break={breakPage} size="l">
          Calculations
        </PdfHeading>
        <CustomerPdfDefinitionsTable definitions={mfvAppendixCalculations} />
      </>
    )

  // scv
  if (dataType === 'definitions')
    return (
      <CustomerPdfDefinitionsTable
        break={breakPage}
        title="Definitions"
        definitions={scvAppendixDefinitions}
      />
    )

  // scv
  if (dataType === 'figuresAndCalculations')
    return (
      <>
        <PdfHeading break={breakPage} size="l">
          Figures and Calculations
        </PdfHeading>
        {SCV_CALCULATIONS.map((calculation) => (
          <React.Fragment key={calculation.title}>
            <View wrap={false}>
              <PdfTableHeading>{calculation.title}</PdfTableHeading>
              <CustomerPdfScvTable
                data={calculation.kpiTransform(kpis)}
                margins={false}
              />
            </View>
          </React.Fragment>
        ))}
        <CustomerPdfDefinitionsTable
          title="Calculations"
          titleSize="m"
          definitions={scvAppendixCalculations}
        />
      </>
    )

  if (!(graphData.length || tableData.length)) return null

  return (
    <>
      <PdfHeading
        break={breakPage}
        size="l"
        style={{ marginTop: rem(3) }}
        minPresenceAhead={100}
      >
        {getCustomerFieldLabel(dataType)}
      </PdfHeading>
      {showGraph && (
        <PdfChartImage
          width={460}
          style={{ marginTop: rem(3), marginBottom: rem(3) }}
        >
          {isScvMode(dataType) ? (
            <ScvGraph
              data={graphData as DataVisualisationItem[]} // we assume this has the correct type from the hook dictionary
              dataType={dataType}
            />
          ) : (
            <MfvGraph data={graphData} mfvMode={dataType} />
          )}
        </PdfChartImage>
      )}
      {showTable && (
        <View
        // Could prevent wrap based on number of rows
        // Setting no wrap is an issue for the renderer if there are more than fit on a page
        >
          <CustomerPdfScvTable data={tableData} dataType={dataType} />
        </View>
      )}
      <CustomerPdfDisclaimers dataType={dataType} />
    </>
  )
}

const CustomerPdfFinancialsPage = ({ kpis, selectedSections = [] }: Props) => {
  if (!selectedSections?.length) return null

  return (
    <PdfPage>
      {selectedSections.map((dataType, i) => (
        <CustomerScvSection
          key={dataType}
          breakPage={i > 0}
          dataType={dataType}
          kpis={kpis}
        />
      ))}
    </PdfPage>
  )
}

export default CustomerPdfFinancialsPage
