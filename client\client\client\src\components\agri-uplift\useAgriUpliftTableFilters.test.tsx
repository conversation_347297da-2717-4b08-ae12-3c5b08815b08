import React from 'react'
import fc from 'fast-check'
import useAgriUpliftTableFiltersSearchParams, {
  CONFIG,
} from './useAgriUpliftTableFilters'
import { renderHook } from '@testing-library/react-hooks'
import { MemoryRouter } from 'react-router-dom'

const configEntries = Object.entries(CONFIG) as [
  keyof typeof CONFIG,
  'array' | 'value',
][]

describe('search params updated as expected', () => {
  for (const [key, type] of configEntries) {
    it(`should always return ${key} parameter as an ${type}`, () => {
      fc.assert(
        fc.property(
          fc.array(fc.string(), { minLength: 0, maxLength: 5 }),
          (queryValues) => {
            const queryParams = new URLSearchParams()
            for (const status of queryValues) {
              queryParams.append(key, status)
            }

            const { result } = renderHook(
              () => useAgriUpliftTableFiltersSearchParams(),
              {
                wrapper: ({ children }) => (
                  <MemoryRouter
                    initialEntries={[`/?${queryParams.toString()}`]}
                  >
                    {children}
                  </MemoryRouter>
                ),
              }
            )

            const [filterState] = result.current

            if (queryValues.length === 0) {
              expect(filterState[key]).toBeUndefined()
            } else {
              if (type === 'array') {
                expect(Array.isArray(filterState[key])).toBe(true)
                expect(filterState[key]).toEqual(queryValues)
              } else {
                expect(Array.isArray(filterState[key])).toBe(false)
                expect(filterState[key]).toEqual(queryValues.at(-1))
              }
            }

            return true
          }
        )
      )
    })
  }
})
