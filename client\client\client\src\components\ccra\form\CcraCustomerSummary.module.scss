.summaryContainer {
  padding: 10px;
}

.descriptions {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.summaryContainer {
  width: 100%;
  h2,
  h3 {
    font-family: MyriadPro;
    color: var(--primary);
    margin-bottom: 0.4em;
    margin-top: 0.25em;
    font-size: 24px;
  }
  h3 {
    font-size: 18px;
  }
  p {
    color: rgba(0, 0, 0, 0.6);
    font-size: 12px;
  }
}

.actions {
  position: sticky;
  bottom: -16px;
}

.buttonGroup {
  display: flex;
  margin-bottom: 16px;
  button {
    margin-right: 12px;
  }
  padding: 16px;
  background-color: #fafafa;
  border-top: 1px solid #f0f2f5;
}
