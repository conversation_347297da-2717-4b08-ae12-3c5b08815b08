import {
  LoadingOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons'
import { AutoComplete, type AutoCompleteProps, Input, Popover } from 'antd'
import type React from 'react'
import { useMemo, useState } from 'react'
import {
  type CustomerGroup,
  useCustomerGroupSearchListQuery,
} from '@store/services/sdk'
import { useDebounce } from '@util/useDebounce'
import util from '@styles/util.module.css'

type CustomerSegment = 'R' | 'W'

type CustomerGroupSearchProps = AutoCompleteProps & {
  customerSetCode?: string
  segment?: CustomerSegment
}

// Customer name and number tooltip
// Trading group number with name

const CustomerGroupSearchLabel = ({
  customerGroup,
}: {
  customerGroup: CustomerGroup
}) => {
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'baseline',
      }}
    >
      {customerGroup.name}{' '}
      <Popover
        content={
          <ul style={{ marginBottom: 0, lineHeight: 1.2 }}>
            {customerGroup.customers.map((customer) => (
              <li key={customer.pk}>{customer.entityName}</li>
            ))}
          </ul>
        }
        placement="leftTop"
        zIndex={99999999}
      >
        <span className={util.muted}>{customerGroup.customers.length}</span>
        <UserOutlined />
      </Popover>
    </div>
  )
}

const CustomerGroupSearch = ({
  customerSetCode,
  segment,
  ...props
}: CustomerGroupSearchProps) => {
  const [match, setMatch] = useState<string | undefined>()
  const debouncedMatch = useDebounce(match, 500)
  const queryValid: boolean = !!debouncedMatch && debouncedMatch.length > 2

  const { data, isLoading, isFetching } = useCustomerGroupSearchListQuery(
    {
      match: debouncedMatch,
      customerSetCode,
      segment: segment as string,
    },
    {
      skip: !queryValid,
    }
  )

  const options = useMemo(
    () =>
      (data?.results || []).map((option) => {
        return {
          key: option.id,
          value: option.name,
          label: <CustomerGroupSearchLabel customerGroup={option} />,
          id: option.id,
        }
      }),
    [data]
  )

  const loading = isLoading || isFetching

  return (
    <AutoComplete
      {...props}
      options={options}
      onSearch={setMatch}
      allowClear={true}
      notFoundContent={
        (queryValid && !loading && (
          <span className={util.muted}>No matches found</span>
        )) ||
        (loading && <LoadingOutlined />)
      }
    >
      <Input
        prefix={<SearchOutlined />}
        placeholder="Search for an ANZ customer group"
        spellCheck={false}
      />
    </AutoComplete>
  )
}

export default CustomerGroupSearch
