import {
  DeleteOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import {
  Button,
  Card,
  Divider,
  Empty,
  Form,
  Input,
  Popconfirm,
  Radio,
  type RadioChangeEvent,
  Table,
  Tooltip,
} from 'antd'
import _ from 'lodash'
import type React from 'react'
import { useEffect, useState } from 'react'
import { useRef } from 'react'
import { RANKED_TRANSITION_RISKS } from '../const'
import type { RankedTransitionRiskItem } from '../types'
import styles from './EditableTable.module.scss'

const EditableTableFieldTransitionRisks = ({
  fieldName,
}: { fieldName: string[] }) => {
  const form = Form.useFormInstance()
  const initialValue = form.getFieldValue(fieldName)
  const [orderedItems, setOrderedItems] = useState<RankedTransitionRiskItem[]>(
    initialValue && initialValue.length > 0
      ? initialValue
      : RANKED_TRANSITION_RISKS
  )

  const handleInputChange = (i: number, value: string) => {
    const newItems = [...orderedItems]
    newItems[i].measureTaken = value
    setOrderedItems(newItems)
  }

  const handleRadioChange = (i: number, e: RadioChangeEvent) => {
    const newItems = [...orderedItems]
    newItems[i].materialToCustomer = e.target.value
    setOrderedItems(newItems)
  }

  useEffect(() => {
    form.setFieldValue(fieldName, orderedItems)
  }, [orderedItems, fieldName, form])

  const tableColumns = [
    {
      title: 'Risks',
      dataIndex: 'risk',
      key: 'risks',
      width: '220px',
      render: (risk: string, record: RankedTransitionRiskItem) => (
        <span>
          {risk}
          {record.helpText && (
            <Tooltip title={record.helpText}>
              <QuestionCircleOutlined
                style={{ marginLeft: 6, position: 'relative', top: '-2px' }}
              />
            </Tooltip>
          )}
        </span>
      ),
    },
    {
      title: 'Material to customer?',
      dataIndex: 'materialToCustomer',
      key: 'materialToCustomer',
      width: '250px',
      render: (_: string, _record: RankedTransitionRiskItem, i: number) => (
        <Radio.Group
          buttonStyle="solid"
          size="small"
          onChange={(e) => handleRadioChange(i, e)}
          defaultValue={orderedItems[i]?.materialToCustomer || null}
        >
          <Radio.Button value="Yes">Yes</Radio.Button>
          <Radio.Button value="No">No</Radio.Button>
          <Radio.Button value="Unsure">Unsure</Radio.Button>
          <Radio.Button value="N/A">N/A</Radio.Button>
        </Radio.Group>
      ),
    },
    {
      title: 'Measures taken to mitigate, if any',
      dataIndex: 'measureTaken',
      key: 'measureTaken',
      render: (_: string, record: RankedTransitionRiskItem, index: number) => (
        <Input
          value={record.measureTaken}
          onChange={(e) => handleInputChange(index, e.target.value)}
          placeholder="Enter measures taken..."
        />
      ),
    },
  ]

  return (
    <>
      <Table
        dataSource={orderedItems}
        columns={tableColumns}
        rowKey="index"
        pagination={false}
      />
      <Input type="hidden" value={JSON.stringify(orderedItems)} />
    </>
  )
}

export default EditableTableFieldTransitionRisks
