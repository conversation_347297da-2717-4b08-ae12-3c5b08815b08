import React, { useState } from 'react'
import styles from './Dropdown.module.css'

type Props = {
  title: (renderProps: { open: boolean }) => React.ReactNode | React.ReactNode
  children: React.ReactNode
}

const Dropdown = ({ title, children }: Props) => {
  const [open, setOpen] = useState(false)

  return (
    <details
      open={open}
      onToggle={(e) => {
        e.preventDefault()
        setOpen((o) => !o)
      }}
    >
      <summary className={styles.title}>
        {typeof title === 'function' ? title({ open }) : title}
      </summary>
      {children}
    </details>
  )
}

export default React.memo(Dropdown)
