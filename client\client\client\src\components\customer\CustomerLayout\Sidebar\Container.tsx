import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import type { ReactNode } from 'react'
import Logo from '@components/customer/Logo'
import vars from '@styles/new/var.module.css'
import { useSidebar } from './context'
import styles from './Container.module.css'

type Props = {
  children: ReactNode
}

export default function SidebarContainer({ children }: Props) {
  const { collapsed, setCollapsed } = useSidebar()

  return (
    <div
      className={classNames(vars.new, styles.container, {
        [styles.collapsed]: collapsed,
      })}
    >
      <div className={styles.header}>
        <h1 className={styles.title}>
          <Logo />
        </h1>
      </div>
      <div className={styles.menu}>{children}</div>
      <button
        type="button"
        onClick={() => setCollapsed(!collapsed)}
        className={styles.footer}
      >
        {collapsed ? <ArrowRightOutlined /> : <ArrowLeftOutlined />}
      </button>
    </div>
  )
}
