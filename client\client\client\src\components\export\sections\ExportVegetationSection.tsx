import { Bar } from '@ant-design/charts'
import React, { useMemo } from 'react'
import type { AnzUnionFeatureCollection } from '../../../models/gis/AnzUnionFeatureCollection'
import { getLegend } from '../../../types/GISLayerDescriptors'
import { GeoJSON } from '../../leaflet/GeoJSON'
import {
  ExportAttributions,
  ExportAttributionsItem,
} from '../components/ExportAttributions'
import { ExportGraphContainer } from '../components/ExportGraphContainer'
import { ExportLegend, ExportLegendItem } from '../components/ExportLegend'
import { ExportMap } from '../components/ExportMap'
import { ExportSection } from '../components/ExportSection'

export interface ExportVegetationSectionProps {
  vegetation: { [key: string]: number } | undefined
  center: { lat: number; lng: number }
  anzUnion: AnzUnionFeatureCollection | undefined
}

export const ExportVegetationSection = (
  props: ExportVegetationSectionProps
) => {
  const { vegetation = {}, center, anzUnion } = props

  const chartData = useMemo(() => {
    return Object.keys(vegetation).map((k) => {
      return { label: k, value: vegetation[k] }
    })
  }, [vegetation])

  const legend = getLegend('vegetation')

  return (
    <React.Fragment>
      <ExportSection>
        <div className="export-section-inner flex">
          <ExportGraphContainer>
            <Bar
              color={({ label }) =>
                label && legend ? legend.getColor(label) : '#ccc'
              }
              data={chartData}
              xField="value"
              yField="label"
            />
          </ExportGraphContainer>
          <ExportLegend direction="vertical">
            {Object.keys(legend?.legendEntries ?? {})?.map((k) => {
              const entry = legend?.legendEntries[k]
              if (
                !Object.keys(vegetation).includes(k) ||
                entry?.label === 'Undefined' ||
                entry?.label === undefined
              ) {
                return null
              }
              return (
                <ExportLegendItem
                  key={k}
                  fillColor={entry?.color}
                  borderColor={entry?.color}
                  label={entry?.label ?? ''}
                />
              )
            })}
          </ExportLegend>
        </div>
      </ExportSection>
      <ExportSection title="Vegetation Visualised">
        <ExportMap type="road" center={center} size="half">
          {anzUnion?.features?.map((feature, index) => {
            const color = legend?.getColor(feature?.properties?.vegetation)
            return (
              <GeoJSON
                key={`vegetation-${index}-${feature?.id}-${color}`}
                data={feature}
                style={{
                  color: color,
                  weight: 2,
                  fillOpacity: 0.75,
                }}
              />
            )
          })}
        </ExportMap>
      </ExportSection>
      <ExportAttributions>
        <ExportAttributionsItem type="lrisVegetation" />
      </ExportAttributions>
    </React.Fragment>
  )
}
