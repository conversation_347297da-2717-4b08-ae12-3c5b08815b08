import { Helmet } from 'react-helmet'
import { Outlet } from 'react-router-dom'
import ProtectedRoute from '@components/ProtectedRoute'
import { requiredEntitlements } from './const'

const AgriUpliftRouteWrapper = () => {
  return (
    <>
      <Helmet
        defaultTitle="Agri Uplift Finance"
        titleTemplate="Agri Uplift Finance – %s | ESGIS"
      />
      <ProtectedRoute requiredEntitlements={requiredEntitlements}>
        <Outlet />
      </ProtectedRoute>
    </>
  )
}

export default AgriUpliftRouteWrapper
