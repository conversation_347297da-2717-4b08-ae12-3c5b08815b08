import type { TableProps } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import type { Ccra } from '@store/services/sdk/codegen'
import type { FormItemProps, Rule } from 'antd/lib/form'
import type { CustomerEmissionsReport } from '@store/services/finance/codegen'
import type { CustomerEmissionsMetricMap } from '@components/emissions-metrics/util'

export type RankedContributorItem = {
  contributor: string
  index: number
  isCustomContributor: boolean
  helpText?: string
  dataIndex?: string
}

export type RankedTransitionRiskItem = {
  risk: string
  index: number
  helpText?: string
  measureTaken: string
  materialToCustomer: string
}

export type RankedPhysicalRiskItem = {
  risk: string
  index: number
  helpText?: string
  measureTaken: string
  isInsured: boolean
  materialToCustomer?: string
}

export type TopContributorsTableColumns = {
  title: string
  dataIndex: string
  key: string
}

export type DroppableTableBodyProps = {
  columnId: string
  docs: RankedContributorItem[]
}

export type DraggableTableRowProps = {
  index: number
  record: RankedContributorItem
  columnId: string
  docs: RankedContributorItem[]
  orderedItems: RankedContributorItem[]
  tableColumns: ColumnsType<RankedContributorItem>
  setOrderedItems: React.Dispatch<React.SetStateAction<RankedContributorItem[]>>
}

export type DroppableTransitionRisksTableBodyProps = {
  columnId: string
  docs: RankedTransitionRiskItem[]
}

export type DroppablePhysicalRisksTableBodyProps = {
  columnId: string
  docs: RankedPhysicalRiskItem[]
}

export type DraggablePhysicalRisksTableRowProps = {
  index: number
  record: RankedPhysicalRiskItem
  columnId: string
  docs: RankedPhysicalRiskItem[]
}

export type DraggableTransitionRisksTableRowProps = {
  index: number
  record: RankedTransitionRiskItem
  columnId: string
  docs: RankedTransitionRiskItem[]
}

export type DragEndResult = {
  destination: { index: number }
  source: { index: number }
  draggableId: string
  reason: string
}

export type RankedContributorTableProps = TableProps<RankedContributorItem> & {
  components: {
    body: {
      wrapper: React.FC<DroppableTableBodyProps>
      row: React.FC<DraggableTableRowProps>
    }
  }
  onRow?: (
    record: RankedContributorItem,
    index: number
  ) => React.HTMLProps<HTMLTableRowElement>
}

export type CcraFormField = {
  name: string[]
  label: string
  tooltip?: string
  unit?: string
  isCustomField?: boolean
  isDate?: boolean
  isDateRange?: boolean
  businessUnit?: Ccra['businessUnit']
  summaryLabel?: string
}

export interface CcraFormValues {
  ccra?: {
    compliance?: Record<string, unknown>
    emissionsData?: Record<string, unknown>
    emissionsStrategy?: Record<string, unknown>
    climateRisks?: Record<string, unknown>
  }
}

export interface DescriptionsRendererProps {
  section: string
  data?: Record<string, unknown>
  businessUnit?: string
}

export interface TableFieldLabelProps {
  field?: string
  label?: string
}

export type CcraFormStatus = 'incomplete' | 'complete'

export type CcraFormItem = {
  name?: string[]
  label?: string
  tooltip?: string
  children?: React.ReactNode
  heading?: string
  subHeading?: string
  paragraph?: string
  customField?: boolean
  required?: boolean
  disabled?: boolean
  checkboxConfirm?: boolean
  valuePropName?: string
  customRuleMessage?: string
  inlineStyle?: boolean
  customRules?: Rule[]
  hidden?: boolean
  businessUnit?: Ccra['businessUnit']
}

export type CcraBusinessUnit = Ccra['businessUnit']
export type CcraCorporateFormField = Extract<CcraBusinessUnit, 'CORPORATE'>
export type CcraAgriFormField = Extract<CcraBusinessUnit, 'AGRI'>

export type EnhancedMetricType = {
  pk: number
  name: string
  unit: string
  isSection?: boolean
  category?: string
  order?: number
}

export type CCRAFormValues = Omit<Ccra, 'emissionsReport'> & {
  emissionsReport?: Omit<CustomerEmissionsReport, 'metrics'> & {
    metrics: CustomerEmissionsMetricMap
  }
}

export interface FormItemOptions
  extends Omit<FormItemProps, 'name' | 'label' | 'tooltip'> {
  isRequired?: boolean
  customRules?: Rule[]
  className?: string
  hidden?: boolean
  valuePropName?: string
  disabled?: boolean
  fieldName: string
}
