import { EditOutlined, SaveOutlined, SearchOutlined } from '@ant-design/icons'
import { Button, Space, Tabs, message } from 'antd'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useParams } from 'react-router-dom'
import { useNavigate } from 'react-router-dom'
import {
  ButtonWidget,
  MapContainer,
  Pane,
  ResizableContainer,
  ResizablePanel,
} from '@components/generic'
import { FormPage } from '@components/generic/Layout'
import { useTitleSearch } from '@store/hooks/useTitleSearch'
import {
  useGetValuationSummaryQuery,
  useUpdateValuationTitlesMutation,
} from '@store/services/valuations'
import type { TitleFeature } from '@models/title/TitleFeatureCollection'
import { gisApi } from '../../store/services/gis'
import type { AddressTitleEditMap } from '../../types'
import { AgriEditableMap } from '../AgriEditableMap'
import { AgriMap } from '../AgriMap'
import { TitleTable } from '../TitleTable'
import { Widget } from '../generic/Widget'

export interface AddressEditPageRouteParams {
  valuationId: string
}

type TabOption = 'titleSearch' | 'savedTitles'

const AddressEditView = () => {
  const { valuationId = '' } = useParams()
  const navigate = useNavigate()

  const [titleEdits, setTitleEdits] = useState<AddressTitleEditMap>({})

  const dispatch = useDispatch()

  const [updateValuationTitles] = useUpdateValuationTitlesMutation()

  const { address, valuationTitles } = useGetValuationSummaryQuery(
    valuationId,
    {
      selectFromResult: ({ data: result }) => {
        return {
          address: result?.address,
          valuationTitles: result?.titles,
        }
      },
    }
  )

  const [currentTab, setCurrentTab] = useState<TabOption>('titleSearch')

  const valuationTitlesMap = useMemo(() => {
    return (
      valuationTitles?.features?.reduce(
        (accum: { [fid: string]: TitleFeature }, title: TitleFeature) => {
          accum[title.properties.fid] = title
          return accum
        },
        {}
      ) ?? {}
    )
  }, [valuationTitles])

  const {
    searchElem,
    radius: searchRadius,
    searchResults,
    page,
    setPage,
    setLatLng,
  } = useTitleSearch()

  useEffect(() => {
    if (address) {
      setLatLng({ lat: address.lat, lng: address.lng })
    }
  }, [address, setLatLng])

  useEffect(() => {
    const newTitleEdits: AddressTitleEditMap = {}
    for (const [fid, title] of Object.entries(valuationTitlesMap)) {
      newTitleEdits[fid] = {
        state: 'EXISTING',
        title,
      }
    }

    setTitleEdits((existingEdits) => {
      return {
        ...existingEdits,
        ...newTitleEdits,
      }
    })
  }, [valuationTitlesMap])

  useEffect(() => {
    setTitleEdits((prevTitleEdits) => {
      const newResultsMap: AddressTitleEditMap = {}
      if (searchResults) {
        for (const title of searchResults.results) {
          newResultsMap[title.id] = {
            state: 'NEW',
            title,
          }
        }
      }
      for (const [fid, titleEdit] of Object.entries(prevTitleEdits)) {
        if (titleEdit.state === 'NEW') {
          delete prevTitleEdits[fid]
        }
      }
      return {
        ...newResultsMap,
        ...prevTitleEdits,
      }
    })
  }, [searchResults])

  const toggleTitle = useCallback((title: TitleFeature) => {
    setTitleEdits((prevTitleEdits) => {
      const newTitleEdits = {
        ...prevTitleEdits,
      }
      if (
        !newTitleEdits[title.properties.fid] ||
        newTitleEdits[title.properties.fid].state === 'NEW'
      ) {
        newTitleEdits[title.properties.fid].state = 'ADD'
      } else if (newTitleEdits[title.properties.fid].state === 'ADD') {
        newTitleEdits[title.properties.fid].state = 'NEW'
      } else if (newTitleEdits[title.properties.fid].state === 'DELETE') {
        newTitleEdits[title.properties.fid].state = 'EXISTING'
      } else {
        newTitleEdits[title.properties.fid].state = 'DELETE'
      }

      return newTitleEdits
    })
  }, [])

  const hasTitleEdits = useCallback(() => {
    const arrLen = Object.values(titleEdits)
      .map((x) => (x.state === 'ADD' || x.state === 'DELETE' ? x : null))
      .filter((x) => x !== null)
    return arrLen.length > 0
  }, [titleEdits])

  function isDefined<T>(val: T | null): val is T {
    return val !== null
  }

  const saveValuationTitles = useCallback(async () => {
    if (!address) {
      return
    }

    const titles: TitleFeature[] = Object.values(titleEdits)
      .map((titleEdit) => {
        if (titleEdit.state === 'ADD' || titleEdit.state === 'EXISTING') {
          return titleEdit.title
        }
        return null
      })
      .filter(isDefined)

    try {
      await updateValuationTitles({
        valuationId,
        titleIds: titles.map((title) => title?.id),
      }).unwrap()
      dispatch(
        gisApi.endpoints.getPhysicalLayer.initiate(
          { layerType: 'titles', valuationId },
          { subscribe: false, forceRefetch: true }
        )
      )
      navigate(-1)
    } catch (_) {
      void message.error('Error occurred saving titles')
    }
  }, [
    address,
    dispatch,
    navigate,
    // biome-ignore lint/correctness/useExhaustiveDependencies: Can't change with confidence
    isDefined,
    titleEdits,
    updateValuationTitles,
    valuationId,
  ])

  const renderControls = useMemo(() => {
    return (
      <Space style={{ marginRight: '1em' }}>
        <Button
          type="default"
          onClick={() => {
            navigate(-1)
          }}
        >
          Back
        </Button>
        {hasTitleEdits() ? (
          <Button type="primary" onClick={saveValuationTitles}>
            Save Changes
          </Button>
        ) : null}
      </Space>
    )
  }, [hasTitleEdits, saveValuationTitles, navigate])

  const renderSearchCards = useMemo(() => {
    const searchTab = (
      <span style={{ paddingRight: '1em' }}>
        <SearchOutlined />
        Title Search
      </span>
    )
    const savedTab = (
      <span style={{ paddingRight: '1em' }}>
        <SaveOutlined />
        Saved Titles
      </span>
    )

    return (
      <Tabs
        activeKey={currentTab}
        onChange={(k: string) => setCurrentTab(k as TabOption)}
      >
        <Tabs.TabPane tab={searchTab} key="titleSearch">
          <div className="tab-container">
            {searchElem}
            {searchResults && searchResults.results.length > 0 ? (
              <TitleTable
                selectedCol={['titleNo', 'owners', 'estateDescription']}
                showHeader={false}
                titleEdits={titleEdits}
                onRowClick={toggleTitle}
                mode="searchResults"
                pagination={{
                  simple: true,
                  total: searchResults?.count ?? 0,
                  pageSize: 15,
                  current: page,
                  onChange: (e) => setPage(e),
                }}
              />
            ) : null}
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane tab={savedTab} key="savedTitles">
          <Widget title="Saved Titles">
            <div className="agrigis-table">
              <TitleTable
                selectedCol={['titleNo', 'owners', 'estateDescription']}
                className="no-header-table"
                showHeader={false}
                titleEdits={titleEdits}
                mode="existing"
                onRowClick={toggleTitle}
                pagination={false}
              />
            </div>
          </Widget>
        </Tabs.TabPane>
      </Tabs>
    )
  }, [
    currentTab,
    page,
    searchElem,
    searchResults,
    setPage,
    titleEdits,
    toggleTitle,
  ])

  if (!address) {
    // TODO: 404?
    return <div data-testid="address-edit-page" />
  }

  return (
    <FormPage data-testid="address-edit-page">
      <ResizableContainer>
        <ResizablePanel id="address-edit">
          <Widget
            title="Edit Address"
            icon={<EditOutlined />}
            extra={<ButtonWidget>{renderControls}</ButtonWidget>}
          >
            {renderSearchCards}
          </Widget>
        </ResizablePanel>
        <MapContainer variant="valuation">
          {valuationId === undefined ? (
            <AgriMap
              center={{ lat: address.lat, lng: address.lng }}
              height={undefined}
            />
          ) : (
            <>
              <AgriEditableMap
                address={address}
                circleRadius={searchRadius}
                titleEdits={titleEdits}
                toggleSelectedTitle={toggleTitle}
              />
            </>
          )}
        </MapContainer>
      </ResizableContainer>
    </FormPage>
  )
}

export default AddressEditView
