import { Select, type SelectProps } from 'antd'
import { useParams } from 'react-router-dom'
import { useAppDispatch, useSelector } from '@store'
import {
  type KpiMetricType,
  actions,
  getCustomerBenchmarkingState,
} from '@store/features/customer'

type Props = Omit<SelectProps, 'options' | 'value' | 'onChange'>

const CustomerMetricTypeSelector = (props: Props) => {
  const dispatch = useAppDispatch()

  const { customerId } = useParams<{ customerId: string }>()

  const { metric } = useSelector((state) =>
    getCustomerBenchmarkingState(state, Number(customerId))
  )
  return (
    <Select
      style={{ width: '64px' }}
      options={[
        {
          label: '$',
          value: '$',
        },
        {
          label: 'x',
          value: 'x',
        },
        { label: '%', value: '%' },
      ]}
      value={metric}
      onChange={(e) =>
        dispatch(
          actions.setKpiMetric({
            metric: e as KpiMetricType,
            customerId: Number(customerId),
          })
        )
      }
      {...props}
    />
  )
}

export default CustomerMetricTypeSelector
