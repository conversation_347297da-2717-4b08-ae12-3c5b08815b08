import type React from 'react'
import { useMemo } from 'react'
import { ExportBreakdownTableItem } from './ExportBreakdownTableItem'

interface ExportBreakdownTableProps
  extends React.HTMLAttributes<HTMLDivElement> {
  data?: Array<{ label: string; value: number | undefined; hidden?: boolean }>
  totalLabel?: string
}

const ExportBreakdownTable = (props: ExportBreakdownTableProps) => {
  const { data, totalLabel, ...divProps } = props
  const total = useMemo(() => {
    return data?.map((x) => x.value).reduce((a = 0, b = 0) => a + b, 0)
  }, [data])
  return (
    <div className="ExportBreakdownTable" {...divProps}>
      <ExportBreakdownTableItem label="Description" secondaryLabel="Amount" />
      {data?.map((item) => {
        if (item?.hidden !== false) {
          return (
            <ExportBreakdownTableItem
              key={item?.label}
              value={item.value}
              label={item.label}
            />
          )
        }
        return null
      })}
      <ExportBreakdownTableItem
        label={totalLabel ?? 'Total'}
        value={total ?? 0}
      />
    </div>
  )
}

export default ExportBreakdownTable
