import type { Feature, Geometry } from 'geojson'
import { useMemo } from 'react'
import type { TitleProperties } from '@models/title/TitleFeatureCollection'

export const usePropertyAreas = (
  titles: Feature<Geometry | null, TitleProperties>[]
) => {
  return useMemo(() => {
    const surveyAreas = titles
      ?.map((x: Feature<Geometry | null, TitleProperties>) =>
        Number(x.properties.surveyArea)
      )
      .reduce((a: number, b: number) => a + b, 0)
    const geomAreas = titles
      ?.map((x: Feature<Geometry | null, TitleProperties>) => x.properties.area)
      .reduce((a: number, b: number) => a + b, 0)
    return {
      totalGeoAreaM2: geomAreas,
      totalSurveyAreaM2: surveyAreas,
    }
  }, [titles])
}
