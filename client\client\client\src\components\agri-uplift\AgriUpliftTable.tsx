import {
  Empty,
  Input,
  type InputRef,
  Table,
  type TablePaginationConfig,
} from 'antd'
import { NavLink } from 'react-router-dom'
import {
  type AgriUplift,
  type FinanceAgriUpliftListApiArg,
  useFinanceAgriUpliftListQuery,
  useFinanceAgriUpliftProductRetrieveQuery,
} from '@store/services/finance/codegen'
import StatusTag from './StatusTag'
import {
  useTradingGroupRegionListQuery,
  useUsernamesListQuery,
} from '@store/services/sdk'
import type { FilterValue, SorterResult } from 'antd/lib/table/interface'
import { sorterKeyToOrderBy } from '@util/sorterKeyToOrderBy'
import { capitalize } from 'lodash'
import { useMemo, useRef } from 'react'
import useAgriUpliftTableFiltersSearchParams from './useAgriUpliftTableFilters'
import { SearchOutlined } from '@ant-design/icons'
import styles from './AgriUpliftTable.module.css'
import { getColumnSortOrder } from '@util/table'

const validateAufPath = (key: keyof AgriUplift) => key
const validateCustomerPath = (key: keyof AgriUplift['tradingGroup']) => key

function AgriUpliftTable() {
  const [filterState, setFilterState] = useAgriUpliftTableFiltersSearchParams()
  const { data, isLoading } = useFinanceAgriUpliftListQuery(filterState)

  const filterSearchInput = useRef<InputRef>(null)

  const { data: regionFilters = [] } = useTradingGroupRegionListQuery(
    undefined,
    {
      selectFromResult: ({ data }) => ({
        data: data?.map((region) => ({
          text: region.regionName,
          value: region.regionId,
        })),
      }),
    }
  )

  const { data: creatorFilters = [] } = useUsernamesListQuery(
    {},
    {
      selectFromResult: ({ data }) => ({
        data: data?.map((username) => ({
          text: username.name,
          value: username.pk,
        })),
      }),
    }
  )

  const { data: pathwayFilters = [] } =
    useFinanceAgriUpliftProductRetrieveQuery(undefined, {
      selectFromResult: ({ data }) => ({
        data: data?.reportTypes?.map((reportType) => ({
          text: reportType.name,
          value: reportType.pk,
        })),
      }),
    })

  const paginationConfig = useMemo(
    () => ({
      total: data?.count ?? 0,
      pageSize: filterState.size,
      current: filterState.page,
      showTotal: (total: number) =>
        total ? `Total ${total} items` : undefined,
    }),
    [filterState, data]
  )

  const onTableChange = (
    pagination: TablePaginationConfig,
    tableFilters: Record<string, FilterValue | null>,
    sorter: SorterResult<AgriUplift> | SorterResult<AgriUplift>[]
  ) => {
    // `key` field in columns maps to the orderBy / Arg names used to filter / sort response
    const orderBy = (
      Array.isArray(sorter)
        ? sorter.map(sorterKeyToOrderBy)
        : [sorterKeyToOrderBy(sorter)]
    ) as FinanceAgriUpliftListApiArg['orderBy']
    setFilterState((prev) => {
      return {
        ...prev,
        ...tableFilters,
        orderBy,
        page: pagination.current,
        size: pagination.pageSize,
      }
    })
  }

  if (!isLoading && !data) {
    return <Empty />
  }

  return (
    <Table
      loading={isLoading}
      dataSource={data?.results}
      rowKey="pk"
      columns={[
        {
          dataIndex: [
            validateAufPath('tradingGroup'),
            validateCustomerPath('tradingGroupName'),
          ],
          key: 'tradingGroup',
          title: 'Trading Group Name',
          render: (tradingGroupName, record) => (
            <NavLink to={record.pk.toString()}>{tradingGroupName}</NavLink>
          ),
          filterDropdown: ({ setSelectedKeys, confirm, clearFilters }) => {
            return (
              <div
                className={styles.filterSearch}
                onKeyDown={(e) => e.stopPropagation()}
              >
                <Input
                  placeholder={'Search by Name'}
                  ref={filterSearchInput}
                  value={filterState.tradingGroup}
                  onChange={(e) => {
                    const value = e.target.value
                    setSelectedKeys(value ? [value] : [])
                    if (!value) {
                      setFilterState((prev) => ({
                        ...prev,
                        tradingGroup: undefined,
                      }))
                      clearFilters?.()
                    }
                    confirm({ closeDropdown: !value })
                  }}
                  onPressEnter={() => {
                    confirm({ closeDropdown: true })
                  }}
                  allowClear={true}
                />
              </div>
            )
          },
          filteredValue: filterState.tradingGroup
            ? [filterState.tradingGroup]
            : null,
          filterIcon: () => (
            <SearchOutlined
              style={{
                color: filterState.tradingGroup ? '#1890ff' : 'inherit',
              }}
            />
          ),
          onFilterDropdownVisibleChange: (visible) => {
            if (visible) {
              setTimeout(() => filterSearchInput.current?.select(), 100)
            }
          },
        },
        {
          dataIndex: [
            validateAufPath('tradingGroup'),
            validateCustomerPath('tradingGroupNumber'),
          ],
          title: 'Trading Group Number',
        },
        {
          dataIndex: [
            validateAufPath('tradingGroup'),
            validateCustomerPath('regionName'),
          ],
          key: 'tradingGroupRegion',
          filters: regionFilters,
          filteredValue: filterState.tradingGroupRegion
            ? filterState.tradingGroupRegion?.map((id) => id.toString())
            : null,
          title: 'Region',
        },
        {
          dataIndex: [validateAufPath('creatorName')],
          title: 'Creator',
          key: 'creator',
          filterSearch: true,
          filters: creatorFilters,
          filteredValue: filterState.creator
            ? filterState.creator?.map((id) => id.toString())
            : null,
        },
        {
          dataIndex: [validateAufPath('emissionsSources')],
          title: 'Pathway',
          key: 'reportType',
          filters: pathwayFilters,
          filteredValue: filterState.reportType
            ? filterState.reportType?.map((id) => id.toString())
            : null,
          render: (emissionsSources: AgriUplift['emissionsSources']) => {
            return emissionsSources
              .map((source) => source.emissions?.reportTypeName)
              .filter((name) => name !== 'None')
              .filter(Boolean)
              .join(', ')
          },
        },
        {
          dataIndex: [validateAufPath('loanAmount')],
          key: 'loanAmount',
          sorter: true,
          sortOrder: getColumnSortOrder(filterState.orderBy, 'loanAmount'),
          title: 'Loan Amount',
        },
        {
          dataIndex: [validateAufPath('status')],
          key: 'status',
          title: 'Status',
          filters: ['draft', 'completed'].map((status) => ({
            text: capitalize(status),
            value: status,
          })),
          filteredValue: filterState.status
            ? Array.isArray(filterState.status)
              ? filterState.status
              : [filterState.status]
            : null,
          render: (status: AgriUplift['status']) => {
            return <StatusTag status={status} />
          },
        },
        {
          dataIndex: [validateAufPath('expiryDate')],
          key: 'expiryDate',
          title: 'Expiration Date',
          sortOrder: getColumnSortOrder(filterState.orderBy, 'expiryDate'),
          sorter: true,
        },
        {
          dataIndex: [validateAufPath('createdDatetime')],
          key: 'createdDatetime',
          title: 'Created',
          sortOrder: getColumnSortOrder(filterState.orderBy, 'createdDatetime'),
          sorter: true,
        },
        {
          dataIndex: [validateAufPath('updatedDatetime')],
          key: 'updatedDatetime',
          title: 'Updated',
          sortOrder: getColumnSortOrder(filterState.orderBy, 'updatedDatetime'),
          sorter: true,
        },
      ]}
      pagination={paginationConfig}
      className="unstyle"
      onChange={onTableChange}
      sticky
    />
  )
}

export default AgriUpliftTable
