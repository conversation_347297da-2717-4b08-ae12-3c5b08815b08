import { Form, Select } from 'antd'
import React, { useCallback } from 'react'
import type {
  CustomerEmissionsMetric,
  CustomerEmissionsMetricTypeGroup,
} from '@store/services/finance/codegen'
import {
  type CustomerEmissionsMetricMap,
  emissionsMetricTypeIdToName,
} from '@components/emissions-metrics/util'
import styles from './EmissionsMetricGroupedFormItem.module.css'
import EmissionsMetricFormItem from './EmissionsMetricFormItem'

/*
 * This component is used to render a grouped form item for emissions metrics.
 * It allows the user to select a metric type from a dropdown and then enter the corresponding value,
 *
 * To avoid storing the selected metric type for the group in the report itself this component sets the selected metric type
 * to the metric type in the group that has a value set. Then on selection change, it clears the metric value for the current selected type
 * before setting a default value for the new selection.
 *
 */
const EmissionsMetricGroupedFormItem = ({
  reportTypeId,
  metricGroup,
  fieldPath,
}: {
  reportTypeId: number
  metricGroup: CustomerEmissionsMetricTypeGroup
  fieldPath: (string | number)[]
}) => {
  return (
    <Form.Item label={metricGroup.name} required={true}>
      <div className={styles.container}>
        <Form.Item name={fieldPath}>
          <EmissionsMetricGroupInput
            reportTypeId={reportTypeId}
            metricGroup={metricGroup}
          />
        </Form.Item>
        <EmissionsMetricGroupEmissionsFormItem
          reportTypeId={reportTypeId}
          metricGroup={metricGroup}
          fieldPath={fieldPath}
        />
      </div>
    </Form.Item>
  )
}

const useSelectedMetricType = ({
  metricGroup,
  metrics,
}: {
  metricGroup: CustomerEmissionsMetricTypeGroup
  metrics?: CustomerEmissionsMetricMap
}) => {
  const existingMetricTypes = new Set(
    Object.values(metrics ?? {})
      .map((metricValue) => metricValue?.metricTypeId)
      .filter((id) => id != null)
  )
  return (
    metricGroup.metricTypes.find((metricType) =>
      existingMetricTypes.has(metricType.pk)
    ) ?? metricGroup.metricTypes[0]
  )
}

const EmissionsMetricGroupInput = ({
  reportTypeId,
  metricGroup,
  value,
  onChange,
}: {
  reportTypeId: number
  metricGroup: CustomerEmissionsMetricTypeGroup
  value?: CustomerEmissionsMetricMap
  onChange?: (metricMap: CustomerEmissionsMetricMap) => void
}) => {
  const selectedMetricType = useSelectedMetricType({
    metricGroup,
    metrics: value,
  })

  const onSelectChange = useCallback(
    (newMetricTypeId: number) => {
      const newValue = { ...value }
      delete newValue[emissionsMetricTypeIdToName(selectedMetricType.pk)]

      newValue[emissionsMetricTypeIdToName(newMetricTypeId)] = {
        metricTypeId: newMetricTypeId,
        reportTypeId: reportTypeId,
        value: '',
      } as CustomerEmissionsMetric
      onChange?.(newValue)
    },
    [onChange, reportTypeId, selectedMetricType.pk, value]
  )

  return (
    <Select
      options={metricGroup.metricTypes.map((metricType) => ({
        label: metricType.name,
        value: metricType.pk,
      }))}
      value={selectedMetricType.pk}
      onChange={onSelectChange}
    />
  )
}

const EmissionsMetricGroupEmissionsFormItem = ({
  reportTypeId,
  metricGroup,
  fieldPath,
}: {
  reportTypeId: number
  metricGroup: CustomerEmissionsMetricTypeGroup
  fieldPath: (string | number)[]
}) => {
  const form = Form.useFormInstance()
  const metrics = Form.useWatch(fieldPath, form)
  const selectedMetricType = useSelectedMetricType({ metricGroup, metrics })
  return (
    <EmissionsMetricFormItem
      reportTypeId={reportTypeId}
      metricType={selectedMetricType}
      fieldPath={fieldPath}
      includeLabel={false}
    />
  )
}

export default EmissionsMetricGroupedFormItem
