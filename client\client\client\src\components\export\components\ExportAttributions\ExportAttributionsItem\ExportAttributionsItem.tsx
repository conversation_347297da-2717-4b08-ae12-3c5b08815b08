import React from 'react'

interface ExportAttributionsItemProps {
  type: 'linz' | 'valocity' | 'lrisSoil' | 'lrisLuc' | 'lrisVegetation'
}

const DISCLAIMERS = {
  linz: (
    <p>
      This section contains data sourced from the LINZ Data Service licensed for
      reuse under the Licence for Personal Data.  See{' '}
      <a href="https://data.linz.govt.nz/license/linz-licence-personal-data-21">
        https://data.linz.govt.nz/license/linz-licence-personal-data-21
      </a>
      .
    </p>
  ),
  valocity: <p>Property Data supplied by Valocity.</p>,
  lrisLuc: (
    <p>
      Land Use Classification data reproduced with the permission of Manaaki
      Whenua – Landcare Research
    </p>
  ),
  lrisSoil: (
    <p>
      Soil data reproduced with the permission of Manaaki Whenua – Landcare
      Research
    </p>
  ),
  lrisVegetation: (
    <p>
      Vegetation data reproduced with the permission of Manaaki <PERSON> –
      Landcare Research
    </p>
  ),
}

const ExportAttributionsItem = (props: ExportAttributionsItemProps) => {
  return (
    <div className="ExportAttributionsItem">{DISCLAIMERS[props?.type]}</div>
  )
}

export default ExportAttributionsItem
