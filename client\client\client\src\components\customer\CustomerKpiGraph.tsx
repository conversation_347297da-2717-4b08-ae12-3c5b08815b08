import { DualAxes } from '@ant-design/charts'
import { useCallback, useMemo } from 'react'
import type { BenchmarkData } from '@store/hooks/useReport'
import type { Benchmark } from '@store/services/sdk'
import { CURRENCY } from '@util/const'
import {
  type NumberFormatterOptions,
  formatNumber,
  formatPercent,
} from '@util/labels'

type KpiGraphData = {
  customerData: {
    year: string
    customer: number | null
  }[]
  benchmarkData: BenchmarkData
}

type Props = {
  print?: boolean
  data: KpiGraphData
  metric?: Benchmark['measureType']
} & Omit<React.ComponentProps<typeof DualAxes>, 'data' | 'xField' | 'yField'>

const numberOptions: NumberFormatterOptions = {
  maximumFractionDigits: 2,
  trailingZeroDisplay: 'stripIfInteger',
}

const CustomerKpiGraph = ({ data, print, metric: unit, ...props }: Props) => {
  const metric = unit || data.benchmarkData[0]?.measureType

  const { customerData, benchmarkData } = useMemo(() => {
    return {
      customerData: data.customerData.map(({ year, customer }) => ({
        year,
        customer: Number(customer) === 0 ? null : Number(customer),
      })),
      benchmarkData: data.benchmarkData.map(({ year, value, category }) => ({
        year,
        value: Number(value) === 0 ? null : Number(value),
        category,
      })),
    }
  }, [data])

  const renderValue = useCallback(
    (value: string | number | undefined, _metric: typeof metric) => {
      if (!value) {
        return ''
      }

      switch (_metric) {
        case '%':
          return formatPercent(value, numberOptions)
        case '$':
          return formatNumber(value, {
            ...numberOptions,
            style: 'currency',
            currency: CURRENCY,
          })
        default:
          return formatNumber(value, numberOptions)
      }
    },
    []
  )

  return (
    <DualAxes
      data={[benchmarkData, customerData]}
      xField={'year'}
      tooltip={
        !print && {
          formatter: (datum) => {
            return {
              name: datum.category ?? 'Customer',
              value: renderValue(
                Number(datum.value ?? datum.customer),
                metric ?? '$'
              ),
            }
          },
        }
      }
      appendPadding={10}
      yField={['value', 'customer']}
      meta={{
        value: { sync: true },
        customer: { sync: 'value', alias: 'Customer' },
      }}
      yAxis={{
        customer: false,
        value: {
          label: {
            formatter: (text: string) => {
              return renderValue(text, metric)
            },
          },
        },
      }}
      {...{
        legend: { position: 'bottom' },
      }}
      geometryOptions={[
        {
          geometry: 'line',
          seriesField: 'category',
          color: ['rgba(253, 200, 47, 1)', 'rgba(223, 122, 0, 1)', '#004168'],
          point: {
            shape: 'circle',
          },
        },
        { geometry: 'column', color: '#007dbabf' },
      ]}
      {...{
        ...(print && {
          animation: false,
          pixelRatio: 3.125,
          tooltip: false,
          legend: false,
        }),
        ...props,
      }}
    />
  )
}

export default CustomerKpiGraph
