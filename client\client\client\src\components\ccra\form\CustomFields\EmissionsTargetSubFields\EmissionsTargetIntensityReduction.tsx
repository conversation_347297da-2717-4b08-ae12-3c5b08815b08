import { Form, InputNumber, Select } from 'antd'
import type { InputRef } from 'antd'
import type React from 'react'
import { type Dispatch, type SetStateAction, useRef, useState } from 'react'
import { SelectWithOptionInput } from '@/components/form/SelectWithOptionInput'

export const EmissionsTargetIntensityReduction = ({
  index,
  targetUnitOptions,
  setTargetUnitOptions,
}: {
  index: number
  targetUnitOptions: { value: string; label: string }[]
  setTargetUnitOptions: Dispatch<
    SetStateAction<{ value: string; label: string }[]>
  >
}) => {
  const form = Form.useFormInstance()
  const emissionsTargetUnits = Form.useWatch(
    ['emissionsTargets', index, 'emissionsTargetIntensityUnits'],
    form
  )

  return (
    <>
      <Form.Item
        name={['emissionsTargets', index, 'emissionsTargetIntensityFrom']}
      >
        <InputNumber
          min={1}
          style={{ width: 260, marginRight: 6 }}
          controls={false}
          placeholder="Enter value"
          addonAfter={
            <Form.Item
              name={[
                'emissionsTargets',
                index,
                'emissionsTargetIntensityUnits',
              ]}
              style={{ marginBottom: -2 }}
            >
              <SelectWithOptionInput
                options={targetUnitOptions}
                setOptions={setTargetUnitOptions}
              />
            </Form.Item>
          }
        />
      </Form.Item>
      <p>to</p>
      <Form.Item
        name={['emissionsTargets', index, 'emissionsTargetIntensityTo']}
      >
        <InputNumber
          min={1}
          style={{ width: 200 }}
          controls={false}
          placeholder="Enter value"
          addonAfter={emissionsTargetUnits}
        />
      </Form.Item>
      <p>per unit of</p>
      <Form.Item
        name={['emissionsTargets', index, 'emissionsTargetIntensityPerUnitOf']}
      >
        <Select
          options={targetUnitOptions}
          style={{ width: '120px' }}
          placeholder="Select option"
        />
      </Form.Item>
    </>
  )
}

export default EmissionsTargetIntensityReduction
