import { getMeasureFormattedValue } from './helpers'

describe('Formatted numbers using denominator', () => {
  const dataValue = '0.2'
  const dataUnit = '%'

  it('Use unit from data', () => {
    const value = getMeasureFormattedValue('NONE')(dataValue, dataUnit)
    expect(value).toBe('20%')
  })

  it('Use unit from denominator', () => {
    const value = getMeasureFormattedValue('SOMETHING')(dataValue, dataUnit)
    expect(value).toBe('$0.20')
  })
})
