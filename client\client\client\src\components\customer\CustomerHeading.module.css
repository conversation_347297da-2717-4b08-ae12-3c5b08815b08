.container {
  composes: body from "@styles/font.module.css";

  --gap: var(--space-0);

  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
  column-gap: var(--gap);
  line-height: 1;
}

.group {
  composes: heading4 from "@styles/font.module.css";
}

.container .group,
.container .select {
  line-height: 1;
}

.categories {
  composes: bodySmall from "@styles/font.module.css";

  display: flex;
  column-gap: var(--gap);
  align-items: baseline;
  line-height: 1;
}
