import React from 'react'
import SelectedTabButtons from '../SelectedTabButtons'
import { SelectedValocitySaleItem } from '../SelectedValocitySaleItem'

interface SelectedValocitySalesProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode | React.ReactNode[]
  valocitySaleIds?: string[]
}

const SelectedValocitySales = ({
  valocitySaleIds,
  ...divProps
}: SelectedValocitySalesProps) => {
  return (
    <div {...divProps}>
      <SelectedTabButtons layer="valocitySale" />
      {valocitySaleIds?.length ? (
        <React.Fragment>
          <table className="map-container-modal-table">
            <thead>
              <tr>
                <th>Address</th>
                <th>Sale Date</th>
                <th>Area (ha)</th>
                <th>FMV</th>
              </tr>
            </thead>
            <tbody>
              {valocitySaleIds
                ?.filter((x: string) => x !== null)
                ?.map((valocitySaleId: string) => {
                  return (
                    <SelectedValocitySaleItem
                      valocitySaleId={Number(valocitySaleId)}
                      key={`selected-valocity-sale-${valocitySaleId}`}
                    />
                  )
                })}
            </tbody>
          </table>
        </React.Fragment>
      ) : (
        <p style={{ textAlign: 'center' }}>
          No Valocity sales have been selected yet...
        </p>
      )}
    </div>
  )
}

export default SelectedValocitySales
