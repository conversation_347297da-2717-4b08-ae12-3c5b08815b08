import classNames from 'classnames'
import type React from 'react'

interface ExportDetailTableItemProps
  extends React.HTMLAttributes<HTMLDivElement> {
  label: string | React.ReactNode | React.ReactNode[] | boolean | undefined
  value?: string | React.ReactNode | React.ReactNode[] | boolean | undefined
  secondaryLabel?: string
  secondaryValue?:
    | string
    | React.ReactNode
    | React.ReactNode[]
    | boolean
    | undefined
  tertiaryValue?: string
}

const ExportDetailTableItem = (props: ExportDetailTableItemProps) => {
  const {
    label,
    value,
    secondaryValue,
    secondaryLabel,
    tertiaryValue,
    ...divProps
  } = props
  return (
    <div
      className={classNames(
        'ExportDetailTableItem',
        secondaryValue ? 'secondaryValue' : null,
        tertiaryValue ? 'tertiaryValue' : null
      )}
      {...divProps}
    >
      <div className="export-detail-table-item-label">{label}</div>
      <div className="export-detail-table-item-value">
        <ul>
          {secondaryLabel ?? typeof value === 'string' ? (
            <li>{value === undefined ? '*' : value}</li>
          ) : (
            <li>{value}</li>
          )}
        </ul>
      </div>
      {secondaryValue ? (
        <div className="export-detail-table-item-value">{secondaryValue}</div>
      ) : null}
      {tertiaryValue ? (
        <div className="export-detail-table-item-value">{tertiaryValue}</div>
      ) : null}
    </div>
  )
}

export default ExportDetailTableItem
