import { Bar } from '@ant-design/charts'
import React, { useMemo } from 'react'
import type { AnzUnionFeatureCollection } from '../../../models/gis/AnzUnionFeatureCollection'
import { getLegend } from '../../../types/GISLayerDescriptors'
import { GeoJSON } from '../../leaflet/GeoJSON'
import {
  ExportAttributions,
  ExportAttributionsItem,
} from '../components/ExportAttributions'
import { ExportGraphContainer } from '../components/ExportGraphContainer'
import { ExportLegend, ExportLegendItem } from '../components/ExportLegend'
import { ExportMap } from '../components/ExportMap'
import { ExportSection } from '../components/ExportSection'

export interface ExportPSSectionProps {
  ps: { [key: string]: number } | undefined
  center: { lat: number; lng: number }
  anzUnion: AnzUnionFeatureCollection | undefined
}

export const ExportPSSection = (props: ExportPSSectionProps) => {
  const { ps = {}, center, anzUnion } = props

  const chartData = useMemo(() => {
    return Object.keys(ps).map((k) => {
      return { label: k, value: ps[k] }
    })
  }, [ps])

  const legend = getLegend('ps')

  return (
    <React.Fragment>
      <ExportSection>
        <div className="export-section-inner flex">
          <ExportGraphContainer>
            <Bar
              color={({ label }) =>
                label && legend ? legend?.getColor(label) : '#ccc'
              }
              data={chartData}
              xField="value"
              yField="label"
            />
          </ExportGraphContainer>
          <ExportLegend direction="vertical">
            {Object.keys(legend?.legendEntries ?? {})?.map((k) => {
              const entry = legend?.legendEntries[k]
              if (
                !Object.keys(ps).includes(k) ||
                entry?.label === 'Undefined' ||
                entry?.label === undefined
              ) {
                return null
              }
              return (
                <ExportLegendItem
                  key={k}
                  fillColor={entry?.color}
                  borderColor={entry?.color}
                  label={entry?.label ?? ''}
                />
              )
            })}
          </ExportLegend>
        </div>
      </ExportSection>
      <ExportSection title="Particle Size Visualised">
        <ExportMap type="road" center={center} size="half">
          {anzUnion?.features?.map((feature, index) => {
            const color = legend?.getColor(feature?.properties?.ps)
            return (
              <GeoJSON
                key={`ps-${index}-${feature?.id}-${color}`}
                data={feature}
                style={{
                  color: color,
                  weight: 2,
                  fillOpacity: 0.75,
                }}
              />
            )
          })}
        </ExportMap>
      </ExportSection>
      <ExportAttributions>
        <ExportAttributionsItem type="lrisSoil" />
      </ExportAttributions>
    </React.Fragment>
  )
}
