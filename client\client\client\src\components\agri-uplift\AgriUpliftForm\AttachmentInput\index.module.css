.container {
  display: flex;
  flex-direction: column;
  min-height: var(--space-7);
  padding: var(--space-1);
  background-color: var(--color-background--alt-0);
  border: 1px solid var(--border-color);
  border-radius: 3px;
}

.input {
  flex: 1;
  width: 100%;
  cursor: pointer;

  &:hover {
    border-color: var(--border-color--hover);
  }

  &:before {
    display: block;
    content: "Drag and drop or click to choose files:";
    margin-right: var(--space-1);
  }
}

.input::file-selector-button {
  display: none;
}
