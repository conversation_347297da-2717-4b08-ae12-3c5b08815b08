.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-4) 0 0;
}

.nested {
  padding: 0;
  border: none;
  margin-top: var(--space-2);
}

.dropdown {
  --border-offset: var(--space-0);

  position: relative;
  padding: var(--space-2) 0 var(--space-1);

  &:before,
  &:after {
    content: "";
    position: absolute;
    width: calc(
      var(--menu-width) - (var(--menu-inset) * 2) + (var(--border-offset) * 2)
    );
    top: 2px;
    left: calc(var(--menu-inset) - var(--border-offset));
    border-top: 1px solid var(--color-grey-1);
  }

  &:after {
    top: calc(100%);
  }

  &:last-child:after {
    display: none;
  }
}

.dropdown + .dropdown {
  padding-top: 0;

  &:before {
    display: none;
  }
}
