import React, { type ReactNode } from 'react'
import { <PERSON><PERSON>, use<PERSON>eaflet } from 'react-leaflet'
import { shallowEqual } from 'react-redux'
import {
  MapAddressesLayer,
  MapAnzUnionLayer,
  MapResourceConsentsLayer,
  MapSelectedSaleTitleLayer,
  MapTitlesLayer,
  MapValocityListingsLayer,
  MapValocitySalesLayer,
} from '@components/map'
import { type RootState, useSelector } from '@store'
import { getLayerOrder, getTitleMode } from '@store/features/explorer'
import type { ExplorerLayer } from '@store/features/map/types'
import { useGetViewportTitlesQuery } from '@store/services/map'
import { uiSelectors } from '@store/ui'
import { truthy } from '@util/guards'
import styles from './ExplorerLayers.module.scss'
import ExplorerSalesLayer from './ExplorerSalesLayer'
import { ExplorerSelectedAddressPane } from './ExplorerSelectedAddressesPane'
import ExplorerTitlePolygonsLayer from './ExplorerTitlePolygonsLayer'
import { withPosition } from './helpers'

// Move these to explorer state?
const selector = (state: RootState) => ({
  saleIds: uiSelectors.getSelectedSaleIds(state),
  listingIds: uiSelectors.getSelectedListingIds(state),
  enabledLayers: uiSelectors.getEnabledLayers(state),
  layerOrder: getLayerOrder(state),
  titleMode: getTitleMode(state),
  filters: uiSelectors.getExplorerFilters(state),
})

const layers: Record<ExplorerLayer, ReactNode> = {
  addresses: <MapAddressesLayer />,
  anzUnion: <MapAnzUnionLayer />,
  consents: <MapResourceConsentsLayer />,
  sales: <ExplorerSalesLayer />,
  listings: <ExplorerSalesLayer isListing />,
  titles: <MapTitlesLayer />,
  valocityListings: <MapValocityListingsLayer />,
  valocitySales: <MapValocitySalesLayer />,
}

const ExplorerLayers = () => {
  const { enabledLayers, saleIds, listingIds, layerOrder, titleMode } =
    useSelector(selector, shallowEqual)

  const { map } = useLeaflet()

  const bounds = map?.getBounds()
  const zoom = map?.getZoom()

  const { data } = useGetViewportTitlesQuery(
    { bounds, zoom },
    { skip: !(bounds && zoom) || !enabledLayers.includes('titles') }
  )

  return (
    <>
      <>
        {layerOrder
          .filter((layer) => enabledLayers.includes(layer))
          .map((layer, index) => (
            <React.Fragment key={layer + String(index)}>
              {layer === 'titles' ? (
                <MapTitlesLayer position={index} titles={data} />
              ) : (
                withPosition(layers[layer], index)
              )}
            </React.Fragment>
          ))}
      </>
      {titleMode && <ExplorerTitlePolygonsLayer />}
      <ExplorerSelectedAddressPane />

      <Pane name="selectedSales" className={styles.selectedSalesContainer}>
        {[...saleIds, ...listingIds]?.filter(truthy)?.map((x) => (
          <MapSelectedSaleTitleLayer saleId={x} key={x} />
        ))}
      </Pane>
    </>
  )
}

export default ExplorerLayers
