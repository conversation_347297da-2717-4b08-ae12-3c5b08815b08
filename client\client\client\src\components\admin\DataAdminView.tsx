import { Layout, Menu } from 'antd'
import { useDispatch } from 'react-redux'
import { useSelector } from '@store'
import {
  type SelectedModel,
  actions,
  getSelectedModel,
} from '@store/features/admin'
import DataAdminSales from './DataAdminSales'

const { Content, Sider } = Layout
const DataAdminView = () => {
  const dispatch = useDispatch()
  const selectedModel = useSelector(getSelectedModel)

  return (
    <Layout>
      <Sider theme="light">
        <Menu
          onSelect={({ key }) =>
            dispatch(actions.setSelectedModel(key as SelectedModel))
          }
          items={[
            {
              key: 'sales',
              label: 'Sales',
            },
            {
              key: 'projects',
              label: 'Projects',
            },
          ]}
        />
      </Sider>
      <Content style={{ padding: '1em', overflowY: 'auto' }}>
        {selectedModel === 'sales' && <DataAdminSales />}
      </Content>
    </Layout>
  )
}

export default DataAdminView
