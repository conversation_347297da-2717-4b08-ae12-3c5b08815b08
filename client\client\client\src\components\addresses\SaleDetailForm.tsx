import { DownOutlined, LoadingOutlined, UpOutlined } from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import { But<PERSON>, Card } from 'antd'
import classNames from 'classnames'
import type React from 'react'
import { useState } from 'react'
import FrontlineSaleForm from '@components/sales/FrontlineSaleForm'
import { useGetSaleQuery } from '@store/services/sale'
import { formatAddress } from '@util/labels'
import styles from './AddressDetailCard.module.scss'

interface Props {
  id: number
}

// add ability to add extra markers to title mode (e.g. sold)
// layer menu changes

const ExplorerSelectedSaleForm = ({ id }: Props) => {
  const [open, setOpen] = useState(true)

  const { data: sale, isFetching } = useGetSaleQuery(
    id?.toString() ?? skipToken
  )

  let title: React.ReactNode = 'No Property Selected'
  if (sale) title = formatAddress(sale.properties.fullAddress ?? undefined)
  if (isFetching) title = <LoadingOutlined />

  return (
    <Card
      size="small"
      title={title}
      extra={
        <Button
          type="link"
          size="small"
          onClick={() => setOpen((v) => !v)}
          title={open ? 'Close' : 'Open'}
        >
          {open ? <DownOutlined /> : <UpOutlined />}
        </Button>
      }
      className={classNames(styles.container, {
        [styles.open]: open,
      })}
    >
      <div className={classNames(styles.content)}>
        <FrontlineSaleForm pk={id} />
      </div>
    </Card>
  )
}

export default ExplorerSelectedSaleForm
