import React from 'react'
import { PdfHeading, PdfList, PdfParagraph } from '@components/pdf'
import type { CustomerFinancialsType } from '@store/features/customer'
import { DISCLAIMERS, hasDisclaimers } from './scv/static'

type Props = {
  dataType: CustomerFinancialsType
}

const CustomerPdfDisclaimers = ({ dataType }: Props) => {
  if (!hasDisclaimers(dataType)) return null

  const dataTypeDisclaimers = DISCLAIMERS[dataType]

  return (
    <>
      <PdfHeading>{DISCLAIMERS.title}:</PdfHeading>
      {Boolean(dataTypeDisclaimers.title) && (
        <PdfParagraph>{dataTypeDisclaimers.title}</PdfParagraph>
      )}
      <PdfList items={dataTypeDisclaimers.items} />
    </>
  )
}

export default CustomerPdfDisclaimers
