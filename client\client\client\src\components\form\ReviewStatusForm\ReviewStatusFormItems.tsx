import { Form, Input, Select } from 'antd'
import React, { useEffect } from 'react'

export const VALIDATION_OPTIONS = [
  { value: 1, label: 'Reviewed – No Concerns' },
  { value: 2, label: 'Reviewed – With Concerns' },
]

export const COMMENT_OPTION = VALIDATION_OPTIONS[VALIDATION_OPTIONS.length - 1]

const FormReviewStatusItems = () => {
  const reviewStatusName = 'status'
  const reviewCommentsName = 'comments'

  const form = Form.useFormInstance()
  const reviewStatusValue = Form.useWatch(reviewStatusName, form)
  const commentOptionSelected = reviewStatusValue === COMMENT_OPTION.value

  useEffect(() => {
    if (reviewStatusValue != null && !commentOptionSelected) {
      form.setFieldValue(reviewCommentsName, '')
    }
  }, [commentOptionSelected, reviewStatusValue, form.setFieldValue])

  return (
    <>
      <Form.Item
        label="Status"
        name={reviewStatusName}
        rules={[{ required: true }]}
      >
        <Select options={VALIDATION_OPTIONS} placeholder="Not Reviewed" />
      </Form.Item>
      <Form.Item
        name={reviewCommentsName}
        label="Comments"
        rules={[{ required: commentOptionSelected }]}
      >
        <Input.TextArea
          rows={3}
          disabled={!commentOptionSelected}
          required={commentOptionSelected}
        />
      </Form.Item>
    </>
  )
}

export default FormReviewStatusItems
