import { PrinterOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import React from 'react'

interface ExportPrintButtonProps {
  isFetching?: boolean
}

const ExportPrintButton = (props: ExportPrintButtonProps) => {
  return (
    <Button
      disabled={props?.isFetching}
      onClick={() => {
        window.print()
        window.close()
      }}
      icon={<PrinterOutlined />}
      className="ExportPrintButton"
      type="primary"
    >
      Print PDF
    </Button>
  )
}

export default ExportPrintButton
