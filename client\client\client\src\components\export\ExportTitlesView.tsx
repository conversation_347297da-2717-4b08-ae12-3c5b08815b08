import centroid from '@turf/centroid'
import type { FeatureCollection } from '@turf/turf'
import React, { useMemo } from 'react'
import {
  ExportAttributions,
  ExportControlOverlay,
  ExportGrid,
  ExportPage,
  ExportParagraph,
  ExportSection,
  ExportTable,
  ExportWrapper,
} from '@components/export/components'
import { ExportAttributionsItem } from '@components/export/components/ExportAttributions'
import { CoverPage } from '@components/export/pages/CoverPage'
import { ExportContourProfilePage } from '@components/export/pages/ExportContourProfilePage'
import { ExportLUCPage } from '@components/export/pages/ExportLUCPage'
import { ExportPSPage } from '@components/export/pages/ExportPSPage'
import { ExportPropertySatelitePage } from '@components/export/pages/ExportPropertySatelitePage'
import { ExportTitlePages } from '@components/export/pages/ExportTitlePage'
import { ExportVegetationPage } from '@components/export/pages/ExportVegetationPage'
import { LegalDisclaimerPage } from '@components/export/pages/LegalDisclaimerPage'
import { ExportPropertyBoundariesSection } from '@components/export/sections/ExportPropertyBoundariesSection'
import { ExportPropertyTitlesSection } from '@components/export/sections/ExportPropertyTitlesSection'
import { LoadingOverlay } from '@components/generic'
import { useQuery } from '@store/hooks/useQuery'
import {
  useSelectedTitleExportPdfQuery,
  type DistrictValuationRoll,
} from '@store/services/sdk'
import { formatDollarValue } from '@util'
import type { TitleFeatureCollection } from '@models/title/TitleFeatureCollection'

export function formatArea(area: number | undefined) {
  if (!area) {
    return '0.000'
  }
  if (area > 10000) {
    return `${(area / 1e4).toLocaleString('en-NZ', {
      maximumFractionDigits: 3,
    })} ha`
  }
  return `${area.toLocaleString('en-NZ', {
    maximumFractionDigits: 3,
  })} sqm`
}

const DEFAULT_CENTER = { lat: -41.2930187, lng: 173.2412197 }

export const ExportTitlesView = () => {
  const query = useQuery()
  const selected: number[] = useMemo(() => {
    return (query.get('titleIds') ?? '').split(',').map((id) => Number(id))
  }, [query])

  const { data, isFetching } = useSelectedTitleExportPdfQuery({
    titleIds: selected,
  })

  const center = useMemo(() => {
    if (data?.titles?.features && data.titles.features.length > 0) {
      const [lng, lat] = centroid(data.titles as FeatureCollection).geometry
        .coordinates
      return {
        lat,
        lng,
      }
    }
    return DEFAULT_CENTER
  }, [data])

  if (!data) {
    return <LoadingOverlay data-testid="export-titles-page" />
  }

  return (
    <React.Fragment>
      <ExportControlOverlay />
      <ExportWrapper data-testid="export-titles-page">
        <CoverPage
          title={data?.documentTitle ?? ''}
          isFetching={isFetching}
          subtitle={data?.titles?.features
            ?.map(({ properties }) => properties?.titleNo)
            .join(', ')}
        />
        <LegalDisclaimerPage />
        <ExportPage
          title="Property Overview"
          subtitle="The titles of a section of land have been simplified
                            into broad categories to summarise the overall
                            profile of a given property."
        >
          <ExportGrid>
            <ExportSection title="Elevation Profile">
              <ExportParagraph html={data?.summary?.elevation} />
            </ExportSection>
            <ExportSection title="Land Description">
              <ExportParagraph html={data?.summary?.landDescription} />
            </ExportSection>
          </ExportGrid>
          <ExportSection title="Valuation Summary">
            <ExportTable
              dataSource={data?.districtValuationRoll}
              pagination={false}
              loading={isFetching}
              rowKey="dvrId"
              columns={[
                {
                  dataIndex: 'fullAddress',
                  title: 'Address',
                },
                {
                  dataIndex: 'valDate',
                  title: 'Valuation Date',
                },
                {
                  dataIndex: 'landUseDesc',
                  title: 'Land Use',
                },
                {
                  dataIndex: 'lv',
                  title: 'Land',
                  render: (text) => formatDollarValue(text),
                },
                {
                  dataIndex: 'iv',
                  title: 'Improvements',
                  render: (text) => formatDollarValue(text),
                },
                {
                  dataIndex: 'cv',
                  title: 'Capital',
                  render: (text) => formatDollarValue(text),
                },
                {
                  dataIndex: 'linzTitles',
                  title: 'Titles',
                  render: (_, record) => {
                    return (
                      record as unknown as DistrictValuationRoll
                    )?.linzTitles.map((feature) => (
                      <div key={feature.titleNo}>{feature.titleNumber}</div>
                    ))
                  },
                },
              ]}
            />
          </ExportSection>
          <ExportAttributions>
            <ExportAttributionsItem type="valocity" />
            <ExportAttributionsItem type="linz" />
          </ExportAttributions>
        </ExportPage>
        <ExportPage
          title="Legal Definition and Boundaries"
          subtitle="This section summarises the legal definition of the property in regards to the property's legal titles and legal boundaries. "
        >
          <ExportPropertyTitlesSection
            isFetching={isFetching}
            titles={data?.titles as unknown as TitleFeatureCollection}
          />
          <ExportPropertyBoundariesSection
            center={center}
            isFetching={isFetching}
            titles={data?.titles as unknown as TitleFeatureCollection}
          />
        </ExportPage>
        <ExportTitlePages
          titles={data?.titles as unknown as TitleFeatureCollection}
        />
        <ExportPropertySatelitePage
          center={center}
          isFetching={isFetching}
          titles={data?.titles as unknown as TitleFeatureCollection}
        />
        <ExportLUCPage
          // biome-ignore lint: Mangled source type
          anzUnion={data?.anzUnion as any}
          luc={data?.summary?.anzUnion?.luc}
          center={center}
        />
        <ExportPSPage
          // biome-ignore lint: Mangled source type
          anzUnion={data?.anzUnion as any}
          ps={data?.summary?.anzUnion?.ps}
          center={center}
        />
        <ExportVegetationPage
          // biome-ignore lint: Mangled source type
          anzUnion={data?.anzUnion as any}
          vegetation={data?.summary?.anzUnion?.vegetation}
          center={center}
        />
        <ExportContourProfilePage
          center={center}
          isFetching={isFetching}
          titles={data?.titles as unknown as TitleFeatureCollection}
        />
      </ExportWrapper>
    </React.Fragment>
  )
}

export default ExportTitlesView
