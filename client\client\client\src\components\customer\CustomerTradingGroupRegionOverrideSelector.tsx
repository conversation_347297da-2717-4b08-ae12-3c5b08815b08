import { Select } from 'antd'
import { useParams } from 'react-router-dom'
import { useAppDispatch, useSelector } from '@store'
import { actions, getCustomerBenchmarkingState } from '@store/features/customer'
import { useTradingGroupRegionListQuery } from '@store/services/sdk'

const CustomerTradingGroupRegionOverrideSelector = ({
  regionName,
}: {
  regionName: string
}) => {
  const { data } = useTradingGroupRegionListQuery()

  const selectData = [
    { value: -1, label: 'All New Zealand' },
    ...(data || [])
      .map((x) => ({
        value: x.regionId,
        label: x.regionName.trim(),
      }))
      .sort((a, b) => a.label.localeCompare(b.label)),
  ]

  const dispatch = useAppDispatch()

  const { customerId } = useParams<{ customerId: string }>()

  const { region } = useSelector((state) =>
    getCustomerBenchmarkingState(state, Number(customerId))
  )

  return (
    <Select
      allowClear
      style={{ width: '100%', minWidth: '250px' }}
      onChange={(e) => {
        // this sends through no region override if the selected region is the same as the actual region.
        // this makes the backend query less complicated
        const selectedRegionName = selectData.find((x) => x.value === e)?.label
        const sameRegion = selectedRegionName === regionName
        dispatch(
          actions.setBenchmarkRegion({
            region: sameRegion ? undefined : e,
            customerId: Number(customerId),
          })
        )
      }}
      value={region}
      options={selectData}
    />
  )
}

export default CustomerTradingGroupRegionOverrideSelector
