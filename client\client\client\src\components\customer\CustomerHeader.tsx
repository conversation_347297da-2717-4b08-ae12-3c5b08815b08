import { FilePdfOutlined } from '@ant-design/icons'
import { Button, Skeleton } from 'antd'
import ButtonGroup from 'antd/lib/button/button-group'
import type React from 'react'
import { useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { useParams } from 'react-router-dom'
import ShareButton from '@components/generic/ShareButton'
import { actions } from '@store/features/customer'
import {
  useCustomerGroupRetrieveQuery,
  useCustomerRetrieveQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import styles from './CustomerHeader.module.scss'
import CustomerTabSelector from './CustomerTabSelector'
import { useEntitled } from '@components/EntitledComponent'

export const CustomerHeader = ({
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  const dispatch = useDispatch()
  const { customerId } = useParams<{ customerId: string }>()
  const { data: customer, isLoading: customerLoading } =
    useCustomerRetrieveQuery(skipArgObject({ pk: Number(customerId) }))
  const hasBenchmarkingPermission = useEntitled([
    'client:customer_portal:benchmarking:view',
  ])

  const {
    data: group,
    isLoading: groupLoading,
    error,
  } = useCustomerGroupRetrieveQuery(skipArgObject({ pk: Number(customerId) }))

  const dispatchDrawerMode = useCallback(
    (mode: string) => {
      dispatch(actions.openCustomerDrawer({ mode: mode as 'data' | 'report' }))
    },
    [dispatch]
  )
  return (
    <div className={styles.CustomerHeader} {...props}>
      <div className={styles.title}>
        <Skeleton loading={customerLoading || groupLoading}>
          <div>
            {!error && <h1>{group?.name}</h1>}
            <h2>{customer?.entityName}</h2>
            <h3>{customer?.customerNumber}</h3>
          </div>
        </Skeleton>
        {customer && (
          <ButtonGroup>
            <Button
              icon={<FilePdfOutlined />}
              onClick={() => dispatchDrawerMode('report')}
              disabled={!hasBenchmarkingPermission}
            >
              Create Report
            </Button>
            <ShareButton />
          </ButtonGroup>
        )}
      </div>
      <CustomerTabSelector />
    </div>
  )
}
