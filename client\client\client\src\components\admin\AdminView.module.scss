.AdminView {
    display: flex;
    flex-direction: column;
    overflow-y: scroll;
    flex-grow: 1;
    padding: 2em;
}

.paneContainer {
    display: flex;
    flex-direction: row;
}

h1 {
    color: var(--primary);
}

.filterButtons {
    margin: 5px;
}

.pane {
    display: flex;
    flex: 50%;
    flex-direction: column;
}

.pane[id=graph] {
    max-width: 600px;
}

.pane[id=table] {
    flex-wrap: wrap;
    align-content: flex-start;
}