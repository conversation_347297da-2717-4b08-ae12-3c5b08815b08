import React, { type ReactNode } from 'react'

interface ExportLegendProps {
  children: ReactNode | ReactNode[]
  direction?: 'horizontal' | 'vertical'
}

export const ExportLegend = ({ children, direction }: ExportLegendProps) => {
  return (
    <div
      className="ExportLegend"
      style={{
        flexDirection:
          direction === 'horizontal' || !direction ? 'row' : 'column',
      }}
    >
      {children}
    </div>
  )
}

export default ExportLegend
