import { Button, Card, Descriptions, Table } from 'antd'
import DescriptionsItem from 'antd/lib/descriptions/Item'
import { Link, useParams } from 'react-router-dom'
import ErrorBounds from '@components/ErrorBounds'
import type { Customer, CustomerGroup } from '@store/services/sdk'
import styles from './CcraCustomerGroupCard.module.scss'
import type { FetchBaseQueryError } from '@reduxjs/toolkit/dist/query'
import type { SerializedError } from '@reduxjs/toolkit'

type CcraCustomerGroupCardProps = {
  selectedRows: Customer[]
  setSelectedRows: (selectedRows: Customer[]) => void
  setView: (view: string) => void
  group?: CustomerGroup
  isFetching: boolean
  isLoading: boolean
  error: FetchBaseQueryError | SerializedError | undefined
}

const CcraCustomerGroupCard = ({
  selectedRows,
  setSelectedRows,
  setView,
  group,
  isFetching,
  isLoading,
  error,
}: CcraCustomerGroupCardProps) => {
  const { customerId } = useParams<{ customerId: string }>()

  const rowSelection = {
    onChange: (_selectedRowKeys: React.Key[], selectedRows: Customer[]) => {
      setSelectedRows(selectedRows)
    },
  }

  const descriptions = [
    { label: 'Group Name', value: group?.name },
    { label: 'Source System', value: group?.segment ?? '-' },
  ]
  return (
    <Card
      title="Select the customer entity/s:"
      loading={isFetching || isLoading}
    >
      <ErrorBounds error={error}>
        <Descriptions
          bordered={true}
          column={1}
          size="small"
          colon={false}
          labelStyle={{ fontWeight: 'bold', color: '#444' }}
        >
          {descriptions.map((description) => {
            return (
              <DescriptionsItem
                key={description.label}
                label={description.label}
              >
                {description.value}
              </DescriptionsItem>
            )
          })}
        </Descriptions>
        <Table
          style={{ marginTop: '16px' }}
          pagination={false}
          className={'unstyle'}
          size="small"
          dataSource={group?.customers ?? []}
          rowKey={({ customerId }) => Number(customerId)}
          key={'customerId'}
          rowClassName={(row) =>
            row?.customerId === Number(customerId)
              ? styles.selectedCustomer
              : 'null'
          }
          columns={[
            {
              dataIndex: 'entityName',
              title: 'Name',
              render: (value, record) => (
                <Link to={`/customer/${record.customerId || ''}/`}>
                  {value}
                </Link>
              ),
            },
            { dataIndex: 'customerNumber', title: 'Customer Number' },
            { dataIndex: 'anzsic', title: 'ANZSIC' },
            { dataIndex: 'customerSetCode', title: 'Set Code' },
          ]}
          rowSelection={rowSelection}
        />
        <Button
          type="primary"
          style={{ marginTop: '16px' }}
          disabled={selectedRows.length < 1}
          onClick={() => setView('form')}
        >
          Enter Customer Climate Information
        </Button>
      </ErrorBounds>
    </Card>
  )
}

export default CcraCustomerGroupCard
