import { Card, type CardProps } from 'antd'
import classNames from 'classnames'
import { Link } from 'react-router-dom'
import LinkButton, {
  type LinkButtonProps,
} from '@components/generic/LinkButton'
import font from '@styles/font.module.css'
import styles from './AgriUpliftLayout.module.css'

type Props = CardProps & {
  link?: LinkButtonProps
}

const AgriUpliftLayout = ({
  children,
  className,
  extra,
  link = {
    delta: -1,
    children: 'Back',
    type: 'default',
  },
  ...props
}: Props) => {
  return (
    <div className={classNames(styles.container, className)}>
      <Card
        title={
          <h1 className={classNames(font.heading3, styles.title)}>
            <Link to="/finance/agri-uplift">Agri Uplift Finance</Link>
          </h1>
        }
        extra={
          <div className={styles.extra}>
            {extra}
            {typeof link !== 'undefined' && (
              <LinkButton href={link.href} delta={link.delta} type={link.type || 'primary'}>
                <span style={{ paddingTop: 5 }}>{link.children}</span>
              </LinkButton>
            )}
          </div>
        }
        className={styles.inner}
        {...props}
      >
        {children}
      </Card>
    </div>
  )
}

export default AgriUpliftLayout
