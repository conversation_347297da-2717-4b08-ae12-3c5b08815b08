.ExportPage[data-variant='legal-disclaimers'] .export-disclaimer-text {
  margin-bottom: 0.5cm;
  font-size: 12px;
  color: #888;
  flex-grow: 0;
}

.ExportPage[data-variant='marketing'] .export-page-title {
  color: var(--export-blue);
  font-family: gotham;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 0.5px;
  margin-bottom: 0.75cm;
  padding-bottom: 0.75cm;
  border-bottom: 1px solid var(--secondary-50);
}

.ExportPage[data-variant='marketing'] .export-page-subtitle {
  color: var(--export-blue);
  font-family: MyriadPro;
  font-weight: 100;
  font-size: 22px;
  letter-spacing: 0.5px;
  margin-bottom: 1cm;
}

.ExportPage .tbc {
  color: red;
}

.ExportPage[data-variant='marketing'] .ExportSection,
.ExportPage[data-variant='marketing'] .export-section-title,
.ExportPage[data-variant='marketing'] .export-section-content {
  border-bottom: none;
}

.ExportPage[data-variant='marketing'] .export-section-title {
  font-family: Gotham;
  text-transform: uppercase;
  color: var(--export-blue);
  letter-spacing: 0.25px;
  font-size: 18px;
  margin-bottom: 0.25cm;
  padding-bottom: 0;
}

.ExportPage[data-variant='marketing'] {
  font-family: MyriadPro;
  font-weight: 100;
  font-size: 18px;
}

.ExportPage[data-variatn='marketing'] b {
  font-family: MyriadPro;
  font-weight: semibold;
}

.ProjectIconContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  width: 100%;
  text-align: center;
}

.ProjectIcon img {
  height: 100px;
  margin-bottom: 0.25cm;
  padding: 0.5cm;
}

.ProjectIcon .project-icon-label {
  font-family: MyriadPro;
  font-weight: semibold;
}

.ExportPage[data-variant='form'] .export-page-header {
  display: grid;
  grid-template-columns: auto 1fr;
  margin-bottom: 1cm;
  padding-bottom: 0;
}

.ExportPage[data-variant='form'] .export-page-title {
  font-family: Gotham;
  text-transform: uppercase;
  color: var(--export-blue);
  letter-spacing: 0.25px;
  font-size: 24px;
  font-weight: bold;
  margin-right: 0.25cm;
}

.ExportPage[data-variant='form'] .export-page-subtitle {
  font-family: Gotham;
  text-transform: uppercase;
  color: #888;
  letter-spacing: 0.25px;
  font-size: 24px;
  font-weight: light;
}

.ExportPage .asterix-disclaimer {
  color: #888;
  font-style: italic;
}

.ExportPage[data-variant='marketing']
  .ExportDetailTable
  .export-detail-table-item-value
  ul {
  list-style-type: square;
}
