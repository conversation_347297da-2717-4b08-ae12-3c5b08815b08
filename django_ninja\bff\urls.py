from django.contrib import admin
from .api import api
from django.urls import path, re_path
from . import views

urlpatterns = [
    # Original API routes
    path("api/", api.urls),

    # OAuth2 Authentication endpoints
    path("auth/login/", views.bff_auth_login, name="bff_auth_login"),
    path("auth/callback/", views.bff_auth_callback, name="bff_auth_callback"),
    path("auth/refresh/", views.bff_auth_refresh, name="bff_auth_refresh"),

    # BFF endpoints
    path("health/", views.bff_health_check, name="bff_health"),
    path("metrics/", views.bff_metrics, name="bff_metrics"),
    path("config/", views.bff_config, name="bff_config"),
    path("aggregate/", views.bff_aggregate_api, name="bff_aggregate"),

    # Main BFF proxy endpoint - catches all other paths
    re_path(r"^proxy/(?P<path>.*)$", views.bff_api_proxy, name="bff_proxy"),
    path("proxy/", views.bff_api_proxy, name="bff_proxy_root"),
]