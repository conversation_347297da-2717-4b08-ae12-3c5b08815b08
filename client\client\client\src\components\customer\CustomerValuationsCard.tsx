import { Card, Table } from 'antd'
import { useParams } from 'react-router-dom'
import { useCustomerValuationsListQuery } from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'

export const CustomerValuationsCard = () => {
  const { customerId } = useParams<{ customerId: string }>()

  const { data, isFetching, isLoading } = useCustomerValuationsListQuery(
    skipArgObject({ pk: Number(customerId) })
  )
  return (
    <Card title="Valuations" loading={isLoading || isFetching}>
      <Table
        size="small"
        dataSource={data}
        columns={[
          { dataIndex: 'address', title: 'Address' },
          { dataIndex: 'valuationType', title: 'Type' },
          {
            dataIndex: 'highestAndBestUse',
            title: 'Highest & Best Use',
          },
        ]}
        rowKey={(row) => row.valuationId ?? ''}
      />
    </Card>
  )
}
