import classNames from 'classnames'
import { format } from 'date-fns'
import Markdown from 'react-markdown'
import { useEsstReportItemsSummaryRetrieveQuery } from '@store/services/esst/codegen'
import type { FieldNode } from '@store/services/fields/codegen'
import font from '@styles/new/font.module.css'
import styles from './EsstReportItemSummary.module.css'
import { Highlight, SchemaHeading, SectionHeading } from './UI'
import { getFlaggedFields } from './util'
import { LANGUAGE } from './const'

function formatValue(fieldType: FieldNode['fieldType'], value: string) {
  switch (fieldType) {
    case 'DATE':
      return format(new Date(value), 'dd/MM/yyyy')
    default:
      return value
  }
}

function Descendants({ fields }: { fields: FieldNode[] }) {
  return (
    <>
      {fields.map((descendant) => (
        <SummaryField key={descendant.id} field={descendant} />
      ))}
    </>
  )
}

function DocumentSummary({ field }: { field: FieldNode }) {
  const populatedSections = field.descendants
    .map((descendant) => ({
      ...descendant,
      descendants: descendant.descendants.filter(
        (descendant) => !!descendant.value
      ),
    }))
    .filter((descendant) => descendant.descendants.length > 0)

  if (!populatedSections.length)
    return <p className={font.bodyLarge}>No questions answered yet.</p>

  return <Descendants fields={populatedSections} />
}

function SummarySection({ field }: { field: FieldNode }) {
  return (
    <>
      {field.description && <SchemaHeading>{field.description}</SchemaHeading>}
      <dl className={styles.list}>
        <Descendants fields={field.descendants} />
      </dl>
    </>
  )
}

function SummaryValue({
  field,
  highlight,
}: { field: FieldNode; highlight?: boolean }) {
  const value = formatValue(field.fieldType, field.value)

  if (!value) return null

  return (
    <>
      <div className={styles.summaryValue}>
        <div>
          <dt className={classNames(font.body, styles.question)}>
            <Markdown linkTarget="_blank">{field.description}</Markdown>
          </dt>
          <dd className={styles.answer}>
            {!!highlight && field.metadata?.status === 'FLAG' ? (
              <Highlight>{value}</Highlight>
            ) : (
              value
            )}
          </dd>
        </div>
      </div>
      <Descendants fields={field.descendants} />
    </>
  )
}

function SummaryField({
  field,
}: {
  field: FieldNode
}): JSX.Element | null {
  switch (field.fieldType) {
    case 'DOCUMENT':
      return <DocumentSummary field={field} />
    case 'SECTION':
    case 'ANY_SELECT':
      return <SummarySection field={field} />
    default:
      return <SummaryValue field={field} highlight />
  }
}

// TODO: use for errors

function SummaryCard({
  title,
  children,
  extra,
}: { title: string; children: React.ReactNode; extra?: React.ReactNode }) {
  return (
    <>
      <div className={styles.summaryCardHeading}>
        <SectionHeading style={{ margin: 0 }}>{title}</SectionHeading>
        {extra}
      </div>
      {children}
    </>
  )
}

export default function EsstReportItemSummary({
  esstReportItemId,
}: { esstReportItemId: number }) {
  const { data, isLoading } = useEsstReportItemsSummaryRetrieveQuery({
    pk: esstReportItemId,
  })

  const flaggedFields = getFlaggedFields(data)

  if (isLoading) return <div>Loading...</div>

  return (
    <>
      {!!data && (
        <>
          <SummaryCard title={LANGUAGE.IDENTIFIED_RISK}>
            {flaggedFields.length ? (
              <dl className={styles.list}>
                {flaggedFields.map((field) => (
                  <SummaryValue key={`risk-${field.id}`} field={field} />
                ))}
              </dl>
            ) : (
              'None'
            )}
          </SummaryCard>
          <SummaryCard
            title="Summary"
            extra={
              <div className={styles.meta}>
                <div className={styles.legend}>
                  <h5 className={styles.legendTitle}>Legend:</h5>
                  <Highlight>Risk Identified</Highlight>
                </div>
              </div>
            }
          >
            <SummaryField field={data} />
          </SummaryCard>
        </>
      )}
    </>
  )
}
