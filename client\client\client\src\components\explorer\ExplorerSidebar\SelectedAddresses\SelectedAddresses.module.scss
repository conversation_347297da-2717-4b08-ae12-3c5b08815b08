.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  // overflow: hidden;
  container-type: inline-size;
}

.addresses {
  flex: 1;
  overflow-x: hidden;
  overflow-y: scroll;
  min-height: 35%;
}

.buttons {
  position: sticky;
  top: 0;
  z-index: 3;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  padding: 0 12px 12px;
  border-bottom: 1px solid lightgrey;
  background: white;

  > *:last-child {
    margin-left: auto;
  }

  @container (max-width: 460px) {
    :global .ant-btn {
      > span:last-child:not([role='img']) {
        display: none;
      }
    }
  }
}

.list {
  width: 100%;

  :global(.ant-list-footer) {
    position: sticky;
    bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
}
