import { But<PERSON>, Col, Descriptions, Divider, Form, Row, Typography } from 'antd'

import moment from 'moment'
import React from 'react'
import {
  StaticTableFieldPhysicalRisks,
  StaticTableFieldRevenueOrOutput,
  StaticTableFieldTopContributors,
  StaticTableFieldTransitionRisks,
} from '../form/CustomFields'
import FormattedValue from './CustomFields/StaticFields/FormattedValue'
import styles from './CcraCustomerSummary.module.scss'
import {
  getField,
  getLabelForField,
  getFieldNamesByCategory,
  showTargets,
} from './helpers'
import { COLUMN_PROPS, ROW_PROPS } from './const'
import type { DescriptionsRendererProps, TableFieldLabelProps } from './types'
import { pickKeys } from '@util/helpers'
import EmissionsReportUpload from './EmissionsReportUpload'
import StaticTableFieldCustomerTarget from './CustomFields/StaticFields/StaticTableFieldCustomerTarget'
import type { CcraEmissionsTarget, Ccra } from '@store/services/sdk'
import StatusTag from '../StatusTag'
import { emissionsMetricsArrayToMap } from '@components/emissions-metrics/util'
import type { CustomerEmissionsMetric } from '@store/services/finance/codegen'
import CcraSelectedCustomers from './CcraSelectedCustomers'

const { Text } = Typography

const DescriptionsRenderer = ({
  section,
  data,
  businessUnit,
}: DescriptionsRendererProps) => {
  if (!data || Object.keys(data).length === 0) {
    return null
  }

  const showField = (
    field: string,
    value: unknown,
    businessUnit: string | undefined
  ) => {
    const fieldData = getField(field)

    if (!fieldData) return false
    if (fieldData.businessUnit && fieldData.businessUnit !== businessUnit) {
      return false
    }
    if (
      (!value && value !== false) ||
      (Array.isArray(value) && value.length === 0)
    ) {
      return false
    }

    return !fieldData.isCustomField
  }

  return (
    <>
      <h2>{section}</h2>
      <Descriptions
        bordered
        size="small"
        layout="vertical"
        className={styles.descriptions}
        column={2}
      >
        {Object.entries(data).map(
          ([field, value]) =>
            showField(field, value, businessUnit) && (
              <Descriptions.Item
                key={field}
                label={getLabelForField(field, true)}
                labelStyle={{ fontWeight: 'bold' }}
              >
                <FormattedValue field={field} value={value} />
              </Descriptions.Item>
            )
        )}
      </Descriptions>
    </>
  )
}

const TableFieldLabel = ({ field, label }: TableFieldLabelProps) => {
  let text = ''

  if (label) {
    text = label
  } else if (field) {
    text = getLabelForField(field)
  }
  return (
    <p style={{ fontWeight: 'bold', color: 'rgba(0, 0, 0, 0.85)' }}>{text}</p>
  )
}

const CcraCustomerSummary = ({
  providedValues,
  setView,
}: {
  providedValues?: Ccra
  setView: (view: string) => void
}) => {
  const form = Form.useFormInstance()

  let values = Form.useWatch([], form)

  if (providedValues) {
    values = providedValues
  }

  let emissionsReportMetrics: { [key: string]: string } = {}
  if (values?.emissionsReport?.metrics) {
    let metrics = values.emissionsReport.metrics

    if (providedValues) {
      metrics = emissionsMetricsArrayToMap(metrics)
    }

    emissionsReportMetrics = Object.fromEntries(
      Object.values(metrics as Record<string, CustomerEmissionsMetric>)
        .filter((metric) => metric !== undefined)
        .map(({ metricType, value }) => [metricType.name, value])
    )
  }

  const businessUnit = values?.businessUnit

  const handleEdit = () => {
    setView('editForm')
  }

  const emissionsData = pickKeys(
    values,
    ...getFieldNamesByCategory('emissionsData')
  )
  const {
    tradingGroupRevenueReportingPercentage,
    hasCarbonCredits,
    carbonCreditsDetail,
    ...restEmissionsData
  } = emissionsData

  return (
    <div className={styles.summaryContainer}>
      <h2>Summary</h2>
      {providedValues ? (
        <>
          <div>
            <StatusTag status={values.status} />
          </div>
          <Text strong>
            Last Updated:{' '}
            {moment(providedValues.updatedDatetime).format('DD/MM/YYYY')}
          </Text>
        </>
      ) : (
        <Text strong>{moment().format('DD/MM/YYYY')}</Text>
      )}
      <Divider />
      {providedValues && (
        <>
          <Row {...ROW_PROPS}>
            <Col {...COLUMN_PROPS}>
              <EmissionsReportUpload
                ccraId={providedValues.id}
                canEdit={providedValues.canEdit}
              />
            </Col>
          </Row>
          <Divider />
        </>
      )}
      <DescriptionsRenderer
        section="Compliance"
        data={pickKeys(values, ...getFieldNamesByCategory('compliance'))}
        businessUnit={businessUnit}
      />
      <Divider />
      <DescriptionsRenderer
        section="Emissions Data"
        data={{
          ...restEmissionsData,
          ...emissionsReportMetrics,
          tradingGroupRevenueReportingPercentage,
          hasCarbonCredits,
          carbonCreditsDetail,
        }}
        businessUnit={businessUnit}
      />
      <Row {...ROW_PROPS}>
        {values?.revenueOrOutput && values?.revenueOrOutput.length > 0 && (
          <Col span={24}>
            <TableFieldLabel field="revenueOrOutput" />
            <StaticTableFieldRevenueOrOutput
              providedValues={values?.revenueOrOutput}
            />
          </Col>
        )}
        {values?.topContributors && values?.topContributors.length > 0 && (
          <Col span={24}>
            <TableFieldLabel field="topContributors" />
            <StaticTableFieldTopContributors
              providedValues={values?.topContributors}
            />
          </Col>
        )}
      </Row>
      <Divider />
      <DescriptionsRenderer
        section="Emissions Strategy"
        data={pickKeys(values, ...getFieldNamesByCategory('emissionsStrategy'))}
        businessUnit={businessUnit}
      />
      <Row {...ROW_PROPS}>
        {showTargets(
          values?.emissionsTargets,
          values?.emissionsReductionPlan
        ) && (
          <Col span={24}>
            <TableFieldLabel label="Summary of customers emissions targets" />
            {values.emissionsTargets.map(
              (target: CcraEmissionsTarget, index: number) => (
                <StaticTableFieldCustomerTarget
                  providedValues={target}
                  displayMode
                  targetIndex={index}
                  key={target.id}
                />
              )
            )}
          </Col>
        )}
      </Row>
      <Divider />
      <DescriptionsRenderer
        section="Climate Risks"
        data={pickKeys(values, ...getFieldNamesByCategory('climateRisks'))}
      />
      <Row {...ROW_PROPS}>
        {values?.transitionRisks && values?.transitionRisks.length > 0 && (
          <Col span={24}>
            <TableFieldLabel field="transitionRisks" />
            <StaticTableFieldTransitionRisks
              providedValues={values?.transitionRisks}
            />
          </Col>
        )}
        {values?.physicalRisks && values?.physicalRisks.length > 0 && (
          <Col span={24}>
            <TableFieldLabel field="physicalRisks" />
            <StaticTableFieldPhysicalRisks
              providedValues={values?.physicalRisks}
            />
          </Col>
        )}
      </Row>
      {providedValues && (
        <div className={styles.actions}>
          <div className={styles.buttonGroup}>
            <Button type="primary" onClick={() => setView('overview')}>
              Back
            </Button>
            {providedValues.canEdit && (
              <Button type="primary" onClick={handleEdit}>
                Edit
              </Button>
            )}
            <CcraSelectedCustomers customerIds={providedValues.customers} />
          </div>
        </div>
      )}
    </div>
  )
}

export default CcraCustomerSummary
