import type { NamePath } from 'rc-field-form/es/interface'
import React, { useMemo } from 'react'
import { useFinanceAgriUpliftProductRetrieveQuery } from '@store/services/finance/codegen'
import { getResourcePath } from './helpers'
import EmissionsMetricGroupedFormItem from './EmissionsMetricGroupedFormItem'
import EmissionsMetricFormItem from './EmissionsMetricFormItem'

type Props = {
  path?: NamePath
  reportTypeId?: number
}

const EmissionsMetrics = ({ path = [], reportTypeId }: Props) => {
  const getFieldPath = useMemo(() => getResourcePath(path), [path])

  const { data: product = { reportTypes: [] } } =
    useFinanceAgriUpliftProductRetrieveQuery()

  const reportType = product?.reportTypes?.find(({ pk }) => pk === reportTypeId)

  if (!reportTypeId) return null

  return (
    <>
      {reportType?.metricTypes?.map(
        ({ metricType, metricGroup, description }) => {
          if (metricType) {
            return (
              <EmissionsMetricFormItem
                key={`${reportTypeId}-${metricType.pk}`}
                reportTypeId={reportTypeId}
                metricType={metricType}
                tooltip={description ?? undefined}
                fieldPath={getFieldPath('metrics')}
              />
            )
          }
          return (
            <EmissionsMetricGroupedFormItem
              reportTypeId={reportTypeId}
              key={`${reportTypeId}-group-${metricGroup.pk}`}
              fieldPath={getFieldPath('metrics')}
              metricGroup={metricGroup}
            />
          )
        }
      )}
    </>
  )
}

export default EmissionsMetrics
