import { Tabs } from 'antd'
import AgriUpliftTable from './AgriUpliftTable'
import useAgriUpliftTableFiltersSearchParams from './useAgriUpliftTableFilters'

const TAB_ITEMS = [
  {
    label: 'My Deals',
    key: 'my-deals',
    children: <AgriUpliftTable />,
  },
  {
    label: 'All Deals',
    key: 'all-deals',
    children: <AgriUpliftTable />,
  },
]

function AgriUpliftList() {
  const [filterState, setFilterState] = useAgriUpliftTableFiltersSearchParams()

  const currentUser = filterState.currentUser?.toString() !== 'false'
  const activeKey = currentUser ? 'my-deals' : 'all-deals'

  return (
    <Tabs
      defaultActiveKey={TAB_ITEMS[0].key}
      activeKey={activeKey}
      onChange={(key: string) =>
        setFilterState({ currentUser: key === 'my-deals' })
      }
    >
      <Tabs.TabPane tab={TAB_ITEMS[0].label} key={TAB_ITEMS[0].key}>
        <AgriUpliftTable />
      </Tabs.TabPane>
      <Tabs.TabPane tab={TAB_ITEMS[1].label} key={TAB_ITEMS[1].key}>
        <AgriUpliftTable />
      </Tabs.TabPane>
    </Tabs>
  )
}

export default AgriUpliftList
