import { ClearOutlined, DeleteOutlined } from '@ant-design/icons'
import { Button, Card, InputNumber, Popconfirm, Table } from 'antd'
import ButtonGroup from 'antd/lib/button/button-group'
import { useState } from 'react'
import { useDispatch } from 'react-redux'
import { Link } from 'react-router-dom'
import {
  useAdminSalesDeleteCreateMutation,
  useAdminSalesListQuery,
} from '@store/services/sdk'
import styles from './DataAdminSales.module.scss'
import SalesTerritoryAssignmentPanel from './SalesTerritoryAssignmentPanel'

const DataAdminSales = () => {
  const { data, isLoading } = useAdminSalesListQuery()
  const dispatch = useDispatch()
  const [deleteSale, { isLoading: deleteLoading }] =
    useAdminSalesDeleteCreateMutation()

  const renderActions = <T extends { pk: number }>(record: T) => {
    return (
      <ButtonGroup>
        <Popconfirm
          title={'Are you sure you want to delete this record?'}
          okText={'Confirm'}
          okButtonProps={{ loading: deleteLoading }}
          onConfirm={() =>
            dispatch(
              deleteSale({
                pk: record.pk,
                adminSale: {
                  pk: record.pk,
                },
              })
            )
          }
        >
          <Button icon={<DeleteOutlined />} />
        </Popconfirm>
      </ButtonGroup>
    )
  }

  const [id, setId] = useState<number | undefined>()

  return (
    <div className={styles.DataAdminSalesPane}>
      <SalesTerritoryAssignmentPanel />
      <Card title={'Sales'} style={{ maxWidth: '800px' }}>
        <Table
          loading={isLoading || deleteLoading}
          rowKey={(row) => row.pk}
          size={'small'}
          bordered={true}
          dataSource={data?.filter(({ pk }) => (id ? pk === id : pk))}
          columns={[
            {
              dataIndex: 'pk',
              title: (
                <div
                  style={{
                    width: '200px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <span>ID</span>
                  <ButtonGroup>
                    <InputNumber
                      min={1}
                      value={id}
                      style={{ width: '100px' }}
                      size={'small'}
                      onChange={(value) => setId(Number(value))}
                    />
                    <Button
                      icon={<ClearOutlined />}
                      onClick={() => setId(undefined)}
                    />
                  </ButtonGroup>
                </div>
              ),
              render: (value: number) => (
                <Link to={`/sale/${value}`}>{value}</Link>
              ),
            },
            { dataIndex: 'fullAddress', title: 'Address' },
            { dataIndex: 'status', title: 'Status ' },
            { key: 'actions', title: 'Actions', render: renderActions },
          ]}
        />
      </Card>
    </div>
  )
}

export default DataAdminSales
