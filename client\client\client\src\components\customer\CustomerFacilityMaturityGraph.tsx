import { Column } from '@ant-design/charts'
import type useFacilitySummary from '@store/hooks/useFacilitySummary'
import { formatDollarValue } from '@util'

type Props = {
  maturitySummary: ReturnType<typeof useFacilitySummary>['maturitySummary']
  print?: boolean
} & Omit<React.ComponentProps<typeof Column>, 'data' | 'xField' | 'yField'>

const CustomerFacilityMaturityGraph = ({
  maturitySummary,
  print,
  ...props
}: Props) => {
  return (
    <Column
      key={'maturity'}
      data={maturitySummary ?? []}
      xField={'year'}
      yField={'value'}
      color={'#007dbabf'}
      yAxis={{
        label: {
          formatter: (text) => formatDollarValue(Number(text)),
        },
      }}
      tooltip={{
        title: 'Balance (NZD)',
        formatter: (datum) => {
          return {
            name: datum.year,
            value: formatDollarValue(Number(datum.value)),
          }
        },
      }}
      {...(print && {
        animation: false,
        pixelRatio: 3.125,
        tooltip: false,
      })}
      {...props}
    />
  )
}

export default CustomerFacilityMaturityGraph
