import { Form } from 'antd'
import { getFieldName, getLabelForField, getTooltipForField } from '../helpers'
import type { FormItemOptions } from '../types'

const CcraFormItem = ({
  fieldName,
  children,
  isRequired = true,
  customRules = [],
  ...props
}: FormItemOptions) => {
  return (
    <Form.Item
      {...props}
      name={getFieldName(fieldName)}
      label={getLabelForField(fieldName)}
      tooltip={getTooltipForField(fieldName)}
      rules={[
        ...(isRequired
          ? [{ required: true, message: 'This field is required' }]
          : []),
        ...customRules,
      ]}
    >
      {children}
    </Form.Item>
  )
}

export default CcraFormItem
