import type React from 'react'
import font from '@styles/new/font.module.css'

export default function EsstSectionHeading({
  children,
  style = {},
}: { children: React.ReactNode; style?: React.CSSProperties }) {
  return (
    <h2
      className={font.headingFive}
      style={{
        color: 'var(--color-pacific-blue)',
        marginBottom: 'var(--space-4)',
        ...style,
      }}
    >
      {children}
    </h2>
  )
}
