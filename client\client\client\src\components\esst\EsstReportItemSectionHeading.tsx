import classNames from 'classnames'
import {
  useEsstReportItemsFilteredSchemaRetrieveQuery,
  useEsstReportItemsRetrieveQuery,
} from '@store/services/esst/codegen'
import font from '@styles/new/font.module.css'
import EsstLoading from './EsstLoading'
import EsstSchemaNav from './EsstSchemaNav'
import useSchemaRoutes from './useSchemaRoutes'
import styles from './EsstReportItemEditView.module.css'
import { reportableNames } from './util'

type Props = {
  esstReportItemId: number
  forForm?: boolean
}

export default function EsstReportItemSectionHeading({
  esstReportItemId,
  forForm = false,
}: Props) {
  const { data: esstReportItem, isLoading: esstReportItemLoading } =
    useEsstReportItemsRetrieveQuery({ pk: esstReportItemId })

  // TODO: Select from result
  const { data: filteredSchema, isLoading: schemaLoading } =
    useEsstReportItemsFilteredSchemaRetrieveQuery({ pk: esstReportItemId })

  const routes = useSchemaRoutes(filteredSchema?.descendants)

  if (esstReportItemLoading || schemaLoading) return <EsstLoading />
  if (!esstReportItem) return null

  return (
    <EsstSchemaNav routes={routes} pathPrefix="../" forForm={forForm}>
      {Boolean(esstReportItem.reportables.length) && (
        <h2 className={styles.title}>
          <span className={classNames(font.body, font.medium)}>
            {esstReportItem.name}&nbsp;
          </span>
          <span className={font.body}>
            {reportableNames(esstReportItem.reportables)}
          </span>
          <span className={font.body}>{esstReportItem.anzsics.join(', ')}</span>
        </h2>
      )}
    </EsstSchemaNav>
  )
}
