import classNames from 'classnames'
import type React from 'react'
import { createRef, memo } from 'react'
import { createPortal } from 'react-dom'
import ExplorerExportMask from './ExplorerExportMask'
import styles from './ExplorerMapContainer.module.scss'

type Props = React.HTMLAttributes<HTMLDivElement>

const ref = createRef<HTMLDivElement>()

export const Container = ({ children }: Props) => {
  return (
    <div
      ref={ref}
      className={classNames('ExplorerMapContainer', styles.container)}
    >
      <ExplorerExportMask />
      {children}
    </div>
  )
}

const Portal = ({ children }: Props) => {
  if (!ref.current) return null
  return createPortal(children, ref.current)
}

export const ExplorerMapContainer = memo(Container)
export const ExplorerMapContainerPortal = memo(Portal)
