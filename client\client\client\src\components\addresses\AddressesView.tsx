import {
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>ush<PERSON>Outlined,
} from '@ant-design/icons'
import { <PERSON><PERSON>, But<PERSON> } from 'antd'
import React, { useCallback, useMemo } from 'react'
import Control from 'react-leaflet-control'
import { useDispatch } from 'react-redux'
import { Dashboard } from '@components/generic/Layout/Dashboard'
import { ResizablePane } from '@components/generic/Pane'
import { useSelector } from '@store'
import { useGetAddressesQuery } from '@store/services/address'
import { uiSelectors } from '@store/ui'
import { setLayoutValue } from '@store/ui/actions'
import { Widget } from '../generic/Widget'
import { AddressesSearchFilters } from './AddressesSearchFilters'
import { AddressesSearchTable } from './AddressesSearchTable'
import { SelectedAddressMap } from './SelectedAddressMap'
import { SelectedAddressWidget } from './SelectedAddressWidget'

export interface AddressesPageRouteParams {
  addressId: string
}

const AddressesView = () => {
  const dispatch = useDispatch()

  const filterState = useSelector((state) =>
    uiSelectors.getFilterState(state, 'addressDashboard')
  )
  const layoutState = useSelector((state) =>
    uiSelectors.getLayoutState(state, 'addressDashboard')
  )

  const skip = useMemo(() => {
    return (
      Object.values(filterState).filter((x) => x !== undefined).length === 0
    )
  }, [filterState])

  const {
    data: addresses,
    isFetching: addressesIsFetching,
    error,
  } = useGetAddressesQuery(filterState, { skip })

  const layoutDispatch = useCallback(
    (payload: { type: string; value: unknown }) => {
      dispatch(
        setLayoutValue({
          pageName: 'addressDashboard',
          layoutKey: payload?.type,
          layoutValue: payload?.value,
        })
      )
    },
    [dispatch]
  )

  const onPrimaryResize = useCallback(
    (_, __, ___, delta) => {
      const width = layoutState.leftPaneWidth + delta.width
      layoutDispatch({ type: 'leftPaneWidth', value: width })
    },
    [layoutState.leftPaneWidth, layoutDispatch]
  )

  const onSecondaryResize = useCallback(
    (_, __, ___, delta) => {
      const width = layoutState.rightPaneWidth + delta.width
      layoutDispatch({ type: 'rightPaneWidth', value: width })
    },
    [layoutState.rightPaneWidth, layoutDispatch]
  )

  const searchElem = useMemo(() => {
    return (
      <>
        <Widget>
          <AddressesSearchFilters />
        </Widget>
        <Widget>
          {!error ? (
            <AddressesSearchTable
              isLoading={addressesIsFetching}
              addresses={addresses}
            />
          ) : (
            <Alert
              className="agrigis-alert"
              message="Not enough search parameters"
              description="There wasn't enough filters to provide you search results, please add an additional filter to help narrow down results."
              type="warning"
              icon={<AlertOutlined />}
              showIcon={true}
            />
          )}
        </Widget>
      </>
    )
  }, [addresses, addressesIsFetching, error])

  return (
    <Dashboard data-testid="addresses-page">
      <ResizablePane
        size={{
          width: layoutState.leftPaneWidth,
          height: 'auto',
        }}
        onResizeStop={onPrimaryResize}
        minimised={layoutState.minimised}
        type="left"
      >
        <Widget
          title="Addresses Dashboard"
          icon={<PushpinOutlined />}
          type="page-header"
        >
          {searchElem}
        </Widget>
      </ResizablePane>
      <SelectedAddressMap
        addresses={addresses?.results}
        addressesLoading={addressesIsFetching}
      >
        <Control position="topleft">
          <Button
            icon={
              layoutState.minimised ? (
                <ArrowRightOutlined />
              ) : (
                <ArrowLeftOutlined />
              )
            }
            onClick={() => {
              layoutDispatch({
                type: 'minimised',
                value: !layoutState.minimised,
              })
              dispatchEvent(new Event('resize'))
            }}
          />
        </Control>
      </SelectedAddressMap>
      <ResizablePane
        size={{
          width: layoutState.rightPaneWidth,
          height: 'auto',
        }}
        onResizeStop={onSecondaryResize}
        type="right"
      >
        <SelectedAddressWidget />
      </ResizablePane>
    </Dashboard>
  )
}

export default AddressesView
