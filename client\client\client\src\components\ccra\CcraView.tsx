import { Col } from 'antd'
import { useEffect, useState } from 'react'
import { COL_PROPS } from '@components/customer/helpers'
import {
  type Customer,
  useCcraCcraRetrieveQuery,
  useCustomerGroupRetrieveQuery,
} from '@store/services/sdk'
import CcraCustomerGroupCard from './CcraCustomerGroupCard'
import CcraCustomerGroupHistoryCard from './CcraCustomerGroupHistoryCard'
import CcraForm<PERSON>ie<PERSON> from './CcraFormView'
import { CcraCustomerSummary } from './form'
import { skipArgObject } from '@util/helpers'
import { useParams } from 'react-router-dom'

const CcraView = () => {
  const [pk, setPk] = useState<number>()
  const [view, setView] = useState('overview')
  const [selectedRows, setSelectedRows] = useState<Customer[]>([])

  const { data: ccraData } = useCcraCcraRetrieveQuery(skipArgObject({ pk }))
  const { customerId } = useParams<{ customerId: string }>()
  const {
    data: group,
    isFetching: isFetchingGroup,
    isLoading: isLoadingGroup,
    error: errorGroup,
  } = useCustomerGroupRetrieveQuery(skipArgObject({ pk: Number(customerId) }))

  if (ccraData && view === 'editForm') {
    const groupCustomers = group?.customers ?? []
    const ccraCustomerIds = ccraData.customers ?? []
    const ccraCustomers = groupCustomers.filter((customer: Customer) =>
      ccraCustomerIds.includes(customer.customerId)
    )
    if (ccraCustomers.length !== selectedRows.length) {
      setSelectedRows(ccraCustomers)
    }
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: Can't change with confidence
  useEffect(() => {
    setView('overview')
  }, [customerId])

  return (
    <>
      {view === 'overview' && (
        <>
          <Col {...COL_PROPS}>
            <CcraCustomerGroupCard
              selectedRows={selectedRows}
              setSelectedRows={setSelectedRows}
              setView={setView}
              group={group}
              isFetching={isFetchingGroup}
              isLoading={isLoadingGroup}
              error={errorGroup}
            />
          </Col>
          <Col {...COL_PROPS}>
            <CcraCustomerGroupHistoryCard setPk={setPk} setView={setView} />
          </Col>
        </>
      )}
      {view === 'form' && (
        <CcraFormView selectedCustomers={selectedRows} setView={setView} />
      )}
      {view === 'editForm' && (
        <CcraFormView
          selectedCustomers={selectedRows}
          setView={setView}
          ccraData={ccraData}
          pk={pk}
        />
      )}
      {view === 'detail' && (
        <>
          <CcraCustomerSummary providedValues={ccraData} setView={setView} />
        </>
      )}
    </>
  )
}

export default CcraView
