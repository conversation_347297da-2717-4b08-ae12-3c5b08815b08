import { useState, useMemo, useEffect } from 'react'
import { Form, message } from 'antd'
import { getFieldsForStep } from '../form/helpers'
import type { CcraFormStatus, CCRAFormValues } from '../form/types'
import type { Ccra } from '@store/services/sdk'
import {
  type CustomerEmissionsMetricMap,
  emissionsMetricsMapToArray,
} from '@components/emissions-metrics/util'

export const useCcraForm = (ccraData?: Ccra | CCRAFormValues) => {
  const [current, setCurrent] = useState(0)
  const [form] = Form.useForm()
  const status = useMemo(() => ccraData?.status || 'incomplete', [ccraData])

  const next = () => {
    const fieldsToValidate = getFieldsForStep(current)
    form
      .validateFields(fieldsToValidate)
      .then(() => setCurrent(current + 1))
      .catch((e) => {
        message.error('Please complete the remaining fields before proceeding')
        console.error(e)
      })
  }

  const prev = () => setCurrent(current - 1)

  const handleFinish = (
    values: unknown,
    formStatus: CcraFormStatus,
    onFinish: (data: unknown) => void,
    pk?: number
  ) => {
    if (
      !(values != null && typeof values === 'object' && 'customers' in values)
    ) {
      console.error('Malformed CCRA form payload', values)
      return
    }
    const ccra = values as Ccra
    ccra.status = ccraData?.status === 'complete' ? 'complete' : formStatus
    const payload: { ccra: Ccra; pk?: number } = { ccra }
    if (pk) {
      payload.pk = pk
    }

    // TODO: type shenanigans due to the way the emissions are defined in the form vs the API
    const formattedMetrics = emissionsMetricsMapToArray(
      (payload.ccra.emissionsReport
        ?.metrics as unknown as CustomerEmissionsMetricMap) ?? {}
    )

    if (payload.ccra.emissionsReport?.metrics) {
      payload.ccra.emissionsReport.metrics = formattedMetrics.filter(
        (metric) => metric !== undefined
      )
    }

    onFinish(payload)
  }

  useEffect(() => {
    if (ccraData) {
      console.log('ccraData', ccraData)
      form.setFieldsValue(ccraData)
    }
  }, [ccraData, form])

  return { form, next, prev, handleFinish, current, status }
}
