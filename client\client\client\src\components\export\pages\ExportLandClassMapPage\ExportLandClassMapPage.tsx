import React from 'react'
import { useGetAddressQuery } from '@store/services/address'
import { ExportPage } from '../../components/ExportPage'
import {
  ExportLandClassMapSection,
  type ExportLandClassMapSectionProps,
} from '../../sections/ExportLandClassMapSection'

const ExportLandClassMapPage = (
  props: Omit<ExportLandClassMapSectionProps, 'center'>
) => {
  // TODO: HACK
  const { center } = useGetAddressQuery(props.valuation.addressId, {
    selectFromResult: ({ data }) => ({
      center: data ? { lat: data?.lat, lng: data?.lng } : undefined,
    }),
  })

  if (!center) {
    return null
  }

  return (
    <ExportPage
      title={`${props.valuation.fullAddress} (${props.valuation.valuationId})`}
      className="ExportLandClassMapPage"
    >
      <ExportLandClassMapSection {...props} center={center} />
    </ExportPage>
  )
}

export default ExportLandClassMapPage
