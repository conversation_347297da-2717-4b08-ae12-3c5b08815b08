import json
import jwt
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone
from functools import wraps

import redis
import requests
from django.conf import settings
from django.http import JsonResponse, HttpRequest, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.contrib.auth.models import User, AnonymousUser
from django.core.cache import cache

# Configure logging
logger = logging.getLogger(__name__)

# Redis connection for rate limiting
try:
    redis_client = redis.Redis(
        host=getattr(settings, 'REDIS_HOST', 'localhost'),
        port=getattr(settings, 'REDIS_PORT', 6379),
        db=getattr(settings, 'REDIS_DB', 0),
        password=getattr(settings, 'REDIS_PASSWORD', ''),
        decode_responses=True
    )
except Exception as e:
    logger.warning(f"Redis connection failed: {e}")
    redis_client = None

# PingFederate Configuration
PINGFEDERATE_CONFIG = {
    'ISSUER': getattr(settings, 'PINGFEDERATE_ISSUER', 'https://pingfederate.example.com'),
    'AUDIENCE': getattr(settings, 'PINGFEDERATE_AUDIENCE', 'esgis-api'),
    'JWKS_URL': getattr(settings, 'PINGFEDERATE_JWKS_URL', 'https://pingfederate.example.com/.well-known/jwks'),
    'ALGORITHM': getattr(settings, 'PINGFEDERATE_ALGORITHM', 'RS256'),
    'LEEWAY': getattr(settings, 'PINGFEDERATE_LEEWAY', 30),  # seconds
}

# Rate limiting configuration
RATE_LIMIT_CONFIG = {
    'DEFAULT_LIMIT': getattr(settings, 'BFF_DEFAULT_RATE_LIMIT', 100),  # requests per window
    'DEFAULT_WINDOW': getattr(settings, 'BFF_DEFAULT_RATE_WINDOW', 3600),  # seconds (1 hour)
    'BURST_LIMIT': getattr(settings, 'BFF_BURST_RATE_LIMIT', 20),  # requests per burst window
    'BURST_WINDOW': getattr(settings, 'BFF_BURST_RATE_WINDOW', 60),  # seconds (1 minute)
}

# RiskRadar API configuration (BFF only handles RiskRadar endpoints)
RISKRADAR_API_CONFIG = {
    'base_url': getattr(settings, 'RISKRADAR_API_URL', 'http://localhost:8000/bff/api/riskradar'),
    'timeout': getattr(settings, 'RISKRADAR_API_TIMEOUT', 30),
    'retry_attempts': getattr(settings, 'RISKRADAR_API_RETRIES', 3),
    'health_endpoint': '/health',
}


class PingFederateAuthenticationError(Exception):
    """Custom exception for PingFederate authentication errors"""
    pass


class RateLimitExceededError(Exception):
    """Custom exception for rate limit exceeded errors"""
    pass


def get_jwks_keys():
    """Fetch and cache JWKS keys from PingFederate"""
    cache_key = 'pingfederate_jwks_keys'
    keys = cache.get(cache_key)

    if keys is None:
        try:
            response = requests.get(PINGFEDERATE_CONFIG['JWKS_URL'], timeout=10)
            response.raise_for_status()
            jwks_data = response.json()
            keys = jwks_data.get('keys', [])
            # Cache for 1 hour
            cache.set(cache_key, keys, 3600)
            logger.info("JWKS keys fetched and cached successfully")
        except Exception as e:
            logger.error(f"Failed to fetch JWKS keys: {e}")
            raise PingFederateAuthenticationError(f"Failed to fetch JWKS keys: {e}")

    return keys


def validate_jwt_token(token: str) -> Dict[str, Any]:
    """Validate JWT token from PingFederate"""
    try:
        # Get JWKS keys
        keys = get_jwks_keys()

        # Decode token header to get key ID
        unverified_header = jwt.get_unverified_header(token)
        kid = unverified_header.get('kid')

        if not kid:
            raise PingFederateAuthenticationError("Token missing key ID")

        # Find the matching key
        key = None
        for jwks_key in keys:
            if jwks_key.get('kid') == kid:
                key = jwt.algorithms.RSAAlgorithm.from_jwk(json.dumps(jwks_key))
                break

        if not key:
            raise PingFederateAuthenticationError("No matching key found for token")

        # Validate and decode token
        payload = jwt.decode(
            token,
            key,
            algorithms=[PINGFEDERATE_CONFIG['ALGORITHM']],
            audience=PINGFEDERATE_CONFIG['AUDIENCE'],
            issuer=PINGFEDERATE_CONFIG['ISSUER'],
            leeway=PINGFEDERATE_CONFIG['LEEWAY']
        )

        logger.info(f"JWT token validated successfully for user: {payload.get('sub', 'unknown')}")
        return payload

    except jwt.ExpiredSignatureError:
        raise PingFederateAuthenticationError("Token has expired")
    except jwt.InvalidAudienceError:
        raise PingFederateAuthenticationError("Invalid token audience")
    except jwt.InvalidIssuerError:
        raise PingFederateAuthenticationError("Invalid token issuer")
    except jwt.InvalidSignatureError:
        raise PingFederateAuthenticationError("Invalid token signature")
    except jwt.InvalidTokenError as e:
        raise PingFederateAuthenticationError(f"Invalid token: {e}")
    except Exception as e:
        logger.error(f"JWT validation error: {e}")
        raise PingFederateAuthenticationError(f"Token validation failed: {e}")


def extract_user_from_token(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Extract user information from JWT payload"""
    return {
        'user_id': payload.get('sub'),
        'username': payload.get('preferred_username', payload.get('sub')),
        'email': payload.get('email'),
        'first_name': payload.get('given_name', ''),
        'last_name': payload.get('family_name', ''),
        'groups': payload.get('groups', []),
        'roles': payload.get('roles', []),
        'permissions': payload.get('permissions', []),
        'exp': payload.get('exp'),
        'iat': payload.get('iat'),
    }


def authenticate_request(request: HttpRequest) -> Dict[str, Any]:
    """Authenticate request using PingFederate JWT token"""
    # Get Authorization header
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')

    if not auth_header.startswith('Bearer '):
        raise PingFederateAuthenticationError("Missing or invalid Authorization header")

    token = auth_header[7:]  # Remove 'Bearer ' prefix

    # Validate token
    payload = validate_jwt_token(token)

    # Extract user information
    user_info = extract_user_from_token(payload)

    return user_info


def get_client_ip(request: HttpRequest) -> str:
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
    return ip


def create_rate_limit_key(identifier: str, window_type: str = 'default') -> str:
    """Create a global rate limit key for Redis"""
    timestamp = int(time.time())

    if window_type == 'burst':
        window = RATE_LIMIT_CONFIG['BURST_WINDOW']
    else:
        window = RATE_LIMIT_CONFIG['DEFAULT_WINDOW']

    # Create time bucket - global rate limiting (no endpoint-specific keys)
    bucket = timestamp // window
    return f"bff_rate_limit:{window_type}:{identifier}:{bucket}"


def check_rate_limit(request: HttpRequest, user_info: Optional[Dict[str, Any]] = None) -> bool:
    """Check if request is within global rate limits"""
    if not redis_client:
        logger.warning("Redis not available, skipping rate limiting")
        return True

    # Create identifiers for rate limiting
    ip = get_client_ip(request)
    user_id = user_info.get('user_id', 'anonymous') if user_info else 'anonymous'

    # Check both IP-based and user-based rate limits globally
    identifiers = [f"ip:{ip}", f"user:{user_id}"]

    for identifier in identifiers:
        # Check burst rate limit (short window)
        burst_key = create_rate_limit_key(identifier, 'burst')
        burst_count = redis_client.get(burst_key)

        if burst_count is None:
            burst_count = 0
        else:
            burst_count = int(burst_count)

        if burst_count >= RATE_LIMIT_CONFIG['BURST_LIMIT']:
            logger.warning(f"Global burst rate limit exceeded for {identifier}")
            raise RateLimitExceededError(f"Global burst rate limit exceeded for {identifier}")

        # Check default rate limit (longer window)
        default_key = create_rate_limit_key(identifier, 'default')
        default_count = redis_client.get(default_key)

        if default_count is None:
            default_count = 0
        else:
            default_count = int(default_count)

        if default_count >= RATE_LIMIT_CONFIG['DEFAULT_LIMIT']:
            logger.warning(f"Global default rate limit exceeded for {identifier}")
            raise RateLimitExceededError(f"Global rate limit exceeded for {identifier}")

    return True


def increment_rate_limit(request: HttpRequest, user_info: Optional[Dict[str, Any]] = None) -> None:
    """Increment global rate limit counters"""
    if not redis_client:
        return

    # Create identifiers for rate limiting
    ip = get_client_ip(request)
    user_id = user_info.get('user_id', 'anonymous') if user_info else 'anonymous'

    # Increment counters for both IP-based and user-based rate limits globally
    identifiers = [f"ip:{ip}", f"user:{user_id}"]

    for identifier in identifiers:
        # Increment burst counter
        burst_key = create_rate_limit_key(identifier, 'burst')
        pipe = redis_client.pipeline()
        pipe.incr(burst_key)
        pipe.expire(burst_key, RATE_LIMIT_CONFIG['BURST_WINDOW'])
        pipe.execute()

        # Increment default counter
        default_key = create_rate_limit_key(identifier, 'default')
        pipe = redis_client.pipeline()
        pipe.incr(default_key)
        pipe.expire(default_key, RATE_LIMIT_CONFIG['DEFAULT_WINDOW'])
        pipe.execute()


def global_rate_limit_decorator():
    """Decorator to apply global rate limiting to views"""
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            try:
                # Get user info if available
                user_info = getattr(request, 'user_info', None)

                # Check global rate limits
                check_rate_limit(request, user_info)

                # Execute the view
                response = func(request, *args, **kwargs)

                # Increment global counters after successful request
                increment_rate_limit(request, user_info)

                return response

            except RateLimitExceededError as e:
                return JsonResponse({
                    'error': 'Rate limit exceeded',
                    'message': str(e),
                    'retry_after': RATE_LIMIT_CONFIG['BURST_WINDOW']
                }, status=429)

        return wrapper
    return decorator


class BackendAPIError(Exception):
    """Custom exception for backend API errors"""
    pass


def forward_request_to_riskradar(path: str, method: str,
                               headers: Dict[str, str], data: Any = None,
                               params: Dict[str, str] = None) -> Dict[str, Any]:
    """Forward request to RiskRadar API"""
    api_config = RISKRADAR_API_CONFIG
    base_url = api_config['base_url']
    timeout = api_config['timeout']

    # Construct full URL
    url = f"{base_url.rstrip('/')}/{path.lstrip('/')}"

    # Prepare headers (remove host-specific headers)
    forward_headers = {}
    for key, value in headers.items():
        if key.lower() not in ['host', 'content-length', 'connection']:
            forward_headers[key] = value

    try:
        logger.info(f"Forwarding {method} request to RiskRadar: {url}")

        if method.upper() == 'GET':
            response = requests.get(url, headers=forward_headers, params=params, timeout=timeout)
        elif method.upper() == 'POST':
            response = requests.post(url, headers=forward_headers, json=data, params=params, timeout=timeout)
        elif method.upper() == 'PUT':
            response = requests.put(url, headers=forward_headers, json=data, params=params, timeout=timeout)
        elif method.upper() == 'PATCH':
            response = requests.patch(url, headers=forward_headers, json=data, params=params, timeout=timeout)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=forward_headers, params=params, timeout=timeout)
        else:
            raise BackendAPIError(f"Unsupported HTTP method: {method}")

        # Parse response
        try:
            response_data = response.json() if response.content else {}
        except json.JSONDecodeError:
            response_data = {'content': response.text}

        result = {
            'status_code': response.status_code,
            'data': response_data,
            'headers': dict(response.headers),
            'api_name': 'riskradar',
            'url': url,
            'success': 200 <= response.status_code < 300
        }

        if not result['success']:
            logger.warning(f"RiskRadar API returned error: {response.status_code}")

        return result

    except requests.exceptions.Timeout:
        raise BackendAPIError("Timeout calling RiskRadar API")
    except requests.exceptions.ConnectionError:
        raise BackendAPIError("Connection error calling RiskRadar API")
    except requests.exceptions.RequestException as e:
        raise BackendAPIError(f"Request error calling RiskRadar API: {e}")
    except Exception as e:
        logger.error(f"Unexpected error calling RiskRadar API: {e}")
        raise BackendAPIError(f"Unexpected error calling RiskRadar API: {e}")


def route_request_to_riskradar(request: HttpRequest, path: str) -> Dict[str, Any]:
    """Route request to RiskRadar API"""
    # All requests go to RiskRadar, so we use the full path
    # Remove any leading 'riskradar/' if present for backward compatibility
    if path.startswith('riskradar/'):
        path = path[10:]  # Remove 'riskradar/' prefix

    # Prepare request data
    headers = {}
    for key, value in request.META.items():
        if key.startswith('HTTP_'):
            header_name = key[5:].replace('_', '-').title()
            headers[header_name] = value

    # Get request data
    data = None
    if request.method in ['POST', 'PUT', 'PATCH']:
        try:
            if request.content_type == 'application/json':
                data = json.loads(request.body) if request.body else {}
            else:
                data = dict(request.POST)
        except json.JSONDecodeError:
            data = {}

    # Get query parameters
    params = dict(request.GET)

    # Forward request to RiskRadar
    return forward_request_to_riskradar(
        path=path,
        method=request.method,
        headers=headers,
        data=data,
        params=params
    )


def aggregate_responses(responses: List[Dict[str, Any]],
                       aggregation_type: str = 'merge') -> Dict[str, Any]:
    """Aggregate multiple API responses into a single response"""
    if not responses:
        return {'data': {}, 'errors': [], 'metadata': {'apis_called': 0}}

    successful_responses = [r for r in responses if r.get('success', False)]
    failed_responses = [r for r in responses if not r.get('success', False)]

    if aggregation_type == 'merge':
        # Merge all successful responses
        merged_data = {}
        errors = []
        metadata = {
            'apis_called': len(responses),
            'successful_apis': len(successful_responses),
            'failed_apis': len(failed_responses),
            'api_details': []
        }

        for response in responses:
            api_name = response.get('api_name', 'unknown')
            metadata['api_details'].append({
                'api': api_name,
                'status_code': response.get('status_code'),
                'success': response.get('success', False),
                'url': response.get('url', '')
            })

            if response.get('success', False):
                # Merge data under API name
                merged_data[api_name] = response.get('data', {})
            else:
                errors.append({
                    'api': api_name,
                    'status_code': response.get('status_code'),
                    'error': response.get('data', {})
                })

        return {
            'data': merged_data,
            'errors': errors,
            'metadata': metadata,
            'success': len(successful_responses) > 0
        }

    elif aggregation_type == 'list':
        # Return responses as a list
        return {
            'data': {
                'responses': [
                    {
                        'api': r.get('api_name', 'unknown'),
                        'success': r.get('success', False),
                        'status_code': r.get('status_code'),
                        'data': r.get('data', {})
                    }
                    for r in responses
                ]
            },
            'metadata': {
                'apis_called': len(responses),
                'successful_apis': len(successful_responses),
                'failed_apis': len(failed_responses)
            },
            'success': len(successful_responses) > 0
        }

    elif aggregation_type == 'first_success':
        # Return first successful response
        if successful_responses:
            first_success = successful_responses[0]
            return {
                'data': first_success.get('data', {}),
                'metadata': {
                    'api_used': first_success.get('api_name', 'unknown'),
                    'apis_tried': len(responses),
                    'status_code': first_success.get('status_code')
                },
                'success': True
            }
        else:
            return {
                'data': {},
                'errors': [r.get('data', {}) for r in failed_responses],
                'metadata': {
                    'apis_tried': len(responses),
                    'all_failed': True
                },
                'success': False
            }

    else:
        raise ValueError(f"Unknown aggregation type: {aggregation_type}")


def process_response_for_frontend(response_data: Dict[str, Any],
                                 user_info: Dict[str, Any]) -> Dict[str, Any]:
    """Process aggregated response to make it frontend-compatible"""
    # Add user context
    processed_response = {
        'data': response_data.get('data', {}),
        'success': response_data.get('success', False),
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'user': {
            'id': user_info.get('user_id'),
            'username': user_info.get('username'),
            'permissions': user_info.get('permissions', [])
        },
        'metadata': response_data.get('metadata', {})
    }

    # Include errors if any
    if 'errors' in response_data:
        processed_response['errors'] = response_data['errors']

    # Add pagination info if present
    if 'data' in response_data and isinstance(response_data['data'], dict):
        for api_name, api_data in response_data['data'].items():
            if isinstance(api_data, dict) and 'count' in api_data:
                # This looks like paginated data
                if 'pagination' not in processed_response:
                    processed_response['pagination'] = {}
                processed_response['pagination'][api_name] = {
                    'count': api_data.get('count', 0),
                    'next': api_data.get('next'),
                    'previous': api_data.get('previous')
                }

    return processed_response


# Main BFF Views

@csrf_exempt
@require_http_methods(["GET", "POST", "PUT", "PATCH", "DELETE"])
@global_rate_limit_decorator()
def bff_api_proxy(request: HttpRequest, path: str = '') -> JsonResponse:
    """
    Main BFF API proxy endpoint that handles:
    1. PingFederate authentication
    2. Rate limiting
    3. Request routing to backend APIs
    4. Response aggregation
    """
    try:
        # Step 1: Authenticate request
        try:
            user_info = authenticate_request(request)
            request.user_info = user_info  # Store for rate limiting
            logger.info(f"Authenticated user: {user_info.get('username', 'unknown')}")
        except PingFederateAuthenticationError as e:
            logger.warning(f"Authentication failed: {e}")
            return JsonResponse({
                'error': 'Authentication failed',
                'message': str(e)
            }, status=401)

        # Step 2: Route request to RiskRadar API
        try:
            backend_response = route_request_to_riskradar(request, path)
            logger.info(f"RiskRadar response received: {backend_response.get('status_code')}")
        except BackendAPIError as e:
            logger.error(f"RiskRadar API error: {e}")
            return JsonResponse({
                'error': 'RiskRadar API error',
                'message': str(e)
            }, status=502)

        # Step 3: Process response for frontend
        processed_response = process_response_for_frontend(
            {'data': backend_response.get('data', {}), 'success': backend_response.get('success', False)},
            user_info
        )

        # Return response with appropriate status code
        status_code = backend_response.get('status_code', 200)
        return JsonResponse(processed_response, status=status_code)

    except RateLimitExceededError as e:
        logger.warning(f"Rate limit exceeded: {e}")
        return JsonResponse({
            'error': 'Rate limit exceeded',
            'message': str(e),
            'retry_after': RATE_LIMIT_CONFIG['BURST_WINDOW']
        }, status=429)
    except Exception as e:
        logger.error(f"Unexpected error in BFF proxy: {e}")
        return JsonResponse({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@global_rate_limit_decorator()
def bff_aggregate_api(request: HttpRequest) -> JsonResponse:
    """
    BFF endpoint for aggregating multiple RiskRadar API calls into a single response

    Expected request body:
    {
        "endpoints": [
            {"path": "locations", "method": "GET", "params": {...}},
            {"path": "perils", "method": "GET", "params": {...}}
        ],
        "aggregation_type": "merge"  // or "list" or "first_success"
    }
    """
    try:
        # Step 1: Authenticate request
        try:
            user_info = authenticate_request(request)
            request.user_info = user_info
            logger.info(f"Authenticated user for aggregation: {user_info.get('username', 'unknown')}")
        except PingFederateAuthenticationError as e:
            logger.warning(f"Authentication failed: {e}")
            return JsonResponse({
                'error': 'Authentication failed',
                'message': str(e)
            }, status=401)

        # Step 2: Parse request body
        try:
            request_data = json.loads(request.body) if request.body else {}
        except json.JSONDecodeError:
            return JsonResponse({
                'error': 'Invalid JSON',
                'message': 'Request body must be valid JSON'
            }, status=400)

        endpoints_to_call = request_data.get('endpoints', [])
        aggregation_type = request_data.get('aggregation_type', 'merge')

        if not endpoints_to_call:
            return JsonResponse({
                'error': 'No endpoints specified',
                'message': 'Request must include "endpoints" array'
            }, status=400)

        # Step 3: Call multiple RiskRadar endpoints
        responses = []
        for endpoint_call in endpoints_to_call:
            try:
                path = endpoint_call.get('path', '')
                method = endpoint_call.get('method', 'GET')
                params = endpoint_call.get('params', {})
                data = endpoint_call.get('data')

                if not path:
                    continue

                # Prepare headers
                headers = {}
                for key, value in request.META.items():
                    if key.startswith('HTTP_'):
                        header_name = key[5:].replace('_', '-').title()
                        headers[header_name] = value

                response = forward_request_to_riskradar(
                    path=path,
                    method=method,
                    headers=headers,
                    data=data,
                    params=params
                )
                responses.append(response)

            except Exception as e:
                logger.error(f"Error calling RiskRadar endpoint {endpoint_call.get('path', 'unknown')}: {e}")
                responses.append({
                    'api_name': 'riskradar',
                    'success': False,
                    'status_code': 500,
                    'data': {'error': str(e)},
                    'path': endpoint_call.get('path', 'unknown')
                })

        # Step 4: Aggregate responses
        aggregated_response = aggregate_responses(responses, aggregation_type)

        # Step 5: Process for frontend
        processed_response = process_response_for_frontend(aggregated_response, user_info)

        return JsonResponse(processed_response)

    except RateLimitExceededError as e:
        logger.warning(f"Rate limit exceeded: {e}")
        return JsonResponse({
            'error': 'Rate limit exceeded',
            'message': str(e),
            'retry_after': RATE_LIMIT_CONFIG['BURST_WINDOW']
        }, status=429)
    except Exception as e:
        logger.error(f"Unexpected error in BFF aggregate: {e}")
        return JsonResponse({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def bff_health_check(request: HttpRequest) -> JsonResponse:
    """
    BFF health check endpoint
    """
    health_status = {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'components': {}
    }

    # Check Redis connection
    try:
        if redis_client:
            redis_client.ping()
            health_status['components']['redis'] = {'status': 'healthy'}
        else:
            health_status['components']['redis'] = {'status': 'unavailable', 'message': 'Redis not configured'}
    except Exception as e:
        health_status['components']['redis'] = {'status': 'unhealthy', 'error': str(e)}
        health_status['status'] = 'degraded'

    # Check RiskRadar API
    try:
        health_url = f"{RISKRADAR_API_CONFIG['base_url']}/health"
        response = requests.get(health_url, timeout=5)
        if response.status_code == 200:
            health_status['components']['riskradar'] = {'status': 'healthy'}
        else:
            health_status['components']['riskradar'] = {
                'status': 'unhealthy',
                'status_code': response.status_code
            }
            health_status['status'] = 'degraded'
    except Exception as e:
        health_status['components']['riskradar'] = {'status': 'unhealthy', 'error': str(e)}
        health_status['status'] = 'degraded'

    # Check PingFederate JWKS
    try:
        get_jwks_keys()
        health_status['components']['pingfederate'] = {'status': 'healthy'}
    except Exception as e:
        health_status['components']['pingfederate'] = {'status': 'unhealthy', 'error': str(e)}
        health_status['status'] = 'degraded'

    status_code = 200 if health_status['status'] == 'healthy' else 503
    return JsonResponse(health_status, status=status_code)


@csrf_exempt
@require_http_methods(["GET"])
def bff_metrics(request: HttpRequest) -> JsonResponse:
    """
    BFF metrics endpoint (basic implementation)
    """
    try:
        # Authenticate request for metrics access
        try:
            user_info = authenticate_request(request)
            # Check if user has admin permissions
            if 'admin' not in user_info.get('roles', []) and 'metrics' not in user_info.get('permissions', []):
                return JsonResponse({
                    'error': 'Insufficient permissions',
                    'message': 'Admin role or metrics permission required'
                }, status=403)
        except PingFederateAuthenticationError as e:
            return JsonResponse({
                'error': 'Authentication failed',
                'message': str(e)
            }, status=401)

        metrics = {
            'timestamp': datetime.now().isoformat(),
            'rate_limiting': {},
            'backend_apis': {}
        }

        # Get rate limiting metrics from Redis
        if redis_client:
            try:
                # Get sample rate limit keys
                keys = redis_client.keys('rate_limit:*')
                metrics['rate_limiting']['active_limits'] = len(keys)

                # Get some sample values
                sample_keys = keys[:10] if keys else []
                sample_data = {}
                for key in sample_keys:
                    value = redis_client.get(key)
                    ttl = redis_client.ttl(key)
                    sample_data[key] = {'count': value, 'ttl': ttl}

                metrics['rate_limiting']['samples'] = sample_data
            except Exception as e:
                metrics['rate_limiting']['error'] = str(e)

        # RiskRadar API metrics
        metrics['backend_apis'] = {
            'riskradar': {
                'configured': True,
                'base_url': RISKRADAR_API_CONFIG['base_url'],
                'timeout': RISKRADAR_API_CONFIG['timeout']
            }
        }

        return JsonResponse(metrics)

    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        return JsonResponse({
            'error': 'Internal server error',
            'message': 'Failed to retrieve metrics'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def bff_config(request: HttpRequest) -> JsonResponse:
    """
    BFF configuration endpoint (for debugging)
    """
    try:
        # Authenticate request for config access
        try:
            user_info = authenticate_request(request)
            # Check if user has admin permissions
            if 'admin' not in user_info.get('roles', []) and 'config' not in user_info.get('permissions', []):
                return JsonResponse({
                    'error': 'Insufficient permissions',
                    'message': 'Admin role or config permission required'
                }, status=403)
        except PingFederateAuthenticationError as e:
            return JsonResponse({
                'error': 'Authentication failed',
                'message': str(e)
            }, status=401)

        config_info = {
            'pingfederate': {
                'issuer': PINGFEDERATE_CONFIG['ISSUER'],
                'audience': PINGFEDERATE_CONFIG['AUDIENCE'],
                'algorithm': PINGFEDERATE_CONFIG['ALGORITHM'],
                'leeway': PINGFEDERATE_CONFIG['LEEWAY']
                # Don't expose JWKS_URL for security
            },
            'rate_limiting': RATE_LIMIT_CONFIG,
            'backend_apis': {
                'riskradar': {
                    'base_url': RISKRADAR_API_CONFIG['base_url'],
                    'timeout': RISKRADAR_API_CONFIG['timeout']
                }
            },
            'redis_configured': redis_client is not None
        }

        return JsonResponse(config_info)

    except Exception as e:
        logger.error(f"Error getting config: {e}")
        return JsonResponse({
            'error': 'Internal server error',
            'message': 'Failed to retrieve configuration'
        }, status=500)
