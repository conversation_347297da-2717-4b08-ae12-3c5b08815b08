import { LoadingOutlined } from '@ant-design/icons'
import type React from 'react'
import type { AgriUpliftEmissionsSource } from '@store/services/finance/codegen'
import { useAgriUpliftReportTypeMap } from '../useAgriUpliftReportTypes'
import { INVALID_SOURCE_NAME } from '../const'

type Props = React.HTMLProps<HTMLSpanElement> & {
  source: Partial<AgriUpliftEmissionsSource>
}

const EmissionsSourceTitle = ({ source, ...props }: Props) => {
  const { data: reportTypes, isLoading } = useAgriUpliftReportTypeMap()

  const sourceReportType = source?.emissions?.reportType?.toString() || ''
  const reportType = reportTypes[sourceReportType]

  const name = source.name || 'Untitled'
  const reportTypeName = reportType?.name || 'New'

  if (isLoading) return <LoadingOutlined />

  return (
    <span {...props}>
      {name !== INVALID_SOURCE_NAME
        ? `${name} – ${reportTypeName}`
        : 'Ineligible'}
    </span>
  )
}

export default EmissionsSourceTitle
