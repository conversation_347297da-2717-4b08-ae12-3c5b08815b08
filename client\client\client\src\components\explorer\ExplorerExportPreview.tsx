import { FilePdfOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import React from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from '@store'
import { actions } from '@store/features/explorer'

export const ExplorerExportPreviewToggleButton = () => {
  const dispatch = useDispatch()

  const active = useSelector((state) => state.explorer.showExportPreview)

  const togglePreview = () => dispatch(actions.toggleShowExportPreview())

  return (
    <Button
      className="ExplorerExportPreviewToggleButton"
      icon={<FilePdfOutlined />}
      type={active ? 'primary' : 'default'}
      onClick={togglePreview}
    >
      Export
    </Button>
  )
}
