import type { Feature, Geometry, GeometryCollection } from 'geojson'
import React from 'react'
import type {
  TitleFeatureCollection,
  TitleProperties,
} from '../../../models/title/TitleFeatureCollection'
import { ExportDetailTable } from '../components'
import {
  ExportAttributions,
  ExportAttributionsItem,
} from '../components/ExportAttributions'
import { ExportDetails, ExportDetailsItem } from '../components/ExportDetails'
import { ExportPage } from '../components/ExportPage'
import { ExportSection } from '../components/ExportSection'

interface ExportTitlePageProps {
  title: Feature<
    Geometry | GeometryCollection<Geometry> | null,
    TitleProperties
  >
  index: number
}

interface ExportTitlePagesProps {
  titles: TitleFeatureCollection | undefined
}

export const ExportTitlePage = (props: ExportTitlePageProps) => {
  const { title, index } = props

  return (
    <ExportPage
      key={`${title?.id}-${index}`}
      title={`Title Report - ${title?.properties?.titleNo}`}
      subtitle={title?.properties?.estateDescription}
    >
      <ExportSection key={`${title?.id}-${index}`}>
        <ExportDetailTable
          data={[
            { label: 'Type', value: title?.properties?.type },
            {
              label: 'Land District',
              value: title?.properties?.landDistrict,
            },
            {
              label: 'Issue Date',
              value: title?.properties?.issueDate
                ? new Date(title?.properties?.issueDate)?.toDateString()
                : '',
            },
            { label: 'Owners', value: title?.properties?.owners },
            {
              label: 'Survey Area',
              value: title?.properties?.surveyAreaHa,
            },
            {
              label: 'Mortgagee',
              value: title?.properties?.mortgagee,
            },
          ]}
        />
      </ExportSection>
      <ExportSection
        key={`${title?.id}-memorials`}
        title="Memorials & Encumbrances"
      >
        <ExportDetails type="memorials">
          {title?.properties?.memorials?.map((memorial, index) => {
            return (
              <ExportDetailsItem
                key={`${memorial?.instrumentNumber}-${index}`}
                label={memorial?.instrumentNumber ?? undefined}
              >
                {memorial?.memorialText}
              </ExportDetailsItem>
            )
          })}
        </ExportDetails>
      </ExportSection>
      <ExportAttributions>
        <ExportAttributionsItem type="linz" />
      </ExportAttributions>
    </ExportPage>
  )
}

export const ExportTitlePages = (props: ExportTitlePagesProps) => {
  const { titles } = props

  if (!titles) return null

  return (
    <React.Fragment>
      {titles?.features?.map((title, index) => {
        return <ExportTitlePage key={title?.id} title={title} index={index} />
      })}
    </React.Fragment>
  )
}
