import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import { Table, Tooltip } from 'antd'
import type { ColumnsType } from 'antd/lib/table'
import React, { useCallback, useMemo } from 'react'
import {
  type AgriUpliftEligibleFacility,
  useFinanceAgriUpliftEligibleFacilitiesForListQuery,
  type AgriUplift,
  type AgriUpliftEmissionsSource,
  type AgriUpliftFacility,
} from '@store/services/finance/codegen'
import { PLACEHOLDER_CHAR } from '@util/const'
import { not } from '@util/helpers'
import { equalsLoanId } from './helpers'
import { INVALID_SOURCE_NAME } from '../const'
import EmissionsSourceSelect from './EmissionsSourceSelect'

type AgriUpliftFacilityRow = AgriUpliftEligibleFacility & AgriUpliftFacility

type Props = {
  recordId?: number
  tradingGroupId: AgriUplift['tradingGroupId']
  emissionsSources?: AgriUpliftEmissionsSource[]
  onChange?: (value: AgriUpliftEmissionsSource[]) => void
}

const isInvalidSource = (source: AgriUpliftEmissionsSource) =>
  source.name === INVALID_SOURCE_NAME

function Facilities({
  tradingGroupId,
  emissionsSources = [],
  onChange,
}: Props) {
  const { data: facilities = [], isLoading: customerLendingLoading } =
    useFinanceAgriUpliftEligibleFacilitiesForListQuery({
      pk: tradingGroupId,
    })

  const getMatchingEmissionsSource = useCallback(
    (loanId: number) =>
      emissionsSources.find((source) =>
        // HACK: Shouldn't happen, needs investigation
        source?.facilities?.some(equalsLoanId(loanId))
      ),
    [emissionsSources]
  )

  // Remove facility from all sources' facilities and append to matching source facilities
  const toggleEmissionsSourceFacility = useCallback(
    (row: AgriUpliftFacilityRow) => (emissionsSourceId: number) => {
      const newValue = emissionsSources.map((source) => {
        const updatedSource = source
        const facilities = updatedSource.facilities || []
        updatedSource.facilities = facilities.filter(
          not(equalsLoanId(row.loanId))
        )
        if (updatedSource.pk === emissionsSourceId) {
          updatedSource.facilities.push({
            ...row,
            eligible: !isInvalidSource(updatedSource),
          })
        }
        return updatedSource
      })
      onChange?.(newValue)
    },
    [onChange, emissionsSources]
  )

  const columns: ColumnsType<AgriUpliftFacilityRow> = useMemo(
    () => [
      {
        dataIndex: 'key',
        title: (
          <Tooltip title="Confirmed">
            <CheckOutlined />
          </Tooltip>
        ),
        render: (_, { loanId }) => {
          const matchingSource = getMatchingEmissionsSource(loanId)
          if (matchingSource?.name === INVALID_SOURCE_NAME)
            return <CloseOutlined />
          if (matchingSource) return <CheckOutlined />
          return null
        },
      },
      {
        key: 'emissionsSource',
        title: 'Eligibility Pathway',
        render: (_, row) => {
          const sourceOptions = row.applicable
            ? emissionsSources.filter(not(isInvalidSource))
            : emissionsSources.filter(isInvalidSource)
          return (
            // TODO:
            // - store initial value somewhere, or invalidate all expirations?
            // - new component
            // - popconfirm
            // - on change, if value will affect expiration, store value
            //    - if no stored value, continue to toggle facility
            //    - if stored value, show popconfirm
            //      - on ok, togglefacility with stored value
            //        - unset stored value
            //      - on cancel, unset stored value
            <EmissionsSourceSelect
              allowClear
              emissionsSources={sourceOptions}
              value={getMatchingEmissionsSource(row.loanId)?.pk}
              onChange={toggleEmissionsSourceFacility(row)}
            />
          )
        },
        width: 200,
      },
      { dataIndex: 'entityName', title: 'Customer' },
      { dataIndex: 'accountNumber', title: 'Account #' },
      {
        key: 'productName',
        title: 'Product',
        render: (_, { productName, salesChannel }) =>
          `${productName} ${salesChannel ? `(${salesChannel})` : ''}`,
      },
      {
        dataIndex: 'maturityDate',
        title: 'Maturity',
        render: (value: string, record) =>
          // TODO: Server side
          record?.accountType === 'Limit' ? 'On Demand' : value,
      },
      {
        dataIndex: 'interestType',
        title: 'Interest Type',
      },
      {
        dataIndex: 'limitDollars',
        title: 'Limit / Balance',
      },
      {
        dataIndex: 'drawdownDate',
        title: 'Drawdown',
      },
      {
        key: 'expiration',
        title: 'Expiration',
        render: (_, { expiration }) => {
          return expiration || PLACEHOLDER_CHAR
        },
      },
    ],
    [
      emissionsSources,
      getMatchingEmissionsSource,
      toggleEmissionsSourceFacility,
    ]
  )

  const rows: AgriUpliftFacilityRow[] = facilities.map((facility) => {
    const loanId = facility.accountSk
    const matchingSource = getMatchingEmissionsSource(loanId)
    const matchingFacility = matchingSource?.facilities?.find(
      equalsLoanId(loanId)
    )
    return {
      ...facility,
      eligible: true,
      expiration: null,
      ...matchingFacility,
    }
  })

  return (
    <Table
      loading={customerLendingLoading}
      columns={columns}
      dataSource={rows}
      rowKey={'loanId'}
      pagination={false}
      className={'unstyle'}
    />
  )
}

export default Facilities
