import { PdfHeading, type PdfHeadingProps, PdfTable } from '@components/pdf'

function CustomerPdfDefinitionsTable({
  break: breakPage,
  definitions,
  title,
  titleSize = 'l',
  ...props
}: {
  break?: boolean
  definitions: { term: string; definition: string }[]
  title?: string
  titleSize?: PdfHeadingProps['size']
}) {
  return (
    <>
      {Boolean(title) && (
        <PdfHeading
          break={breakPage}
          size={titleSize}
          style={{ paddingBottom: 0 }}
          minPresenceAhead={200}
        >
          {title}
        </PdfHeading>
      )}
      <PdfTable
        striped
        rows={definitions}
        columns={[{ key: 'term' }, { key: 'definition', weighting: 2 }]} // TODO: Define this as a type
        {...props}
      />
    </>
  )
}

export default CustomerPdfDefinitionsTable
