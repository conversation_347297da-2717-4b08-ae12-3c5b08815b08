import type {
  CcraFormField,
  CcraCorporateFormField,
  CcraAgriFormField,
} from './types'

export const CCRA_CORPORATE_FORM_FIELD: CcraCorporateFormField = 'CORPORATE'
export const CCRA_AGRI_FORM_FIELD: CcraAgriFormField = 'AGRI'

const complianceFields: CcraFormField[] = [
  {
    name: ['disclosures'],
    label:
      'What, if any public climate related disclosures does the customer make?',
    tooltip: `Mandatory disclosure requirements generally apply to publicly listed companies, insurers,
              fund managers and banks. These entities are required to provide annual statements that disclose
              information on the effects of climate change on their business or funds under management.
              Some companies may choose to voluntarily produce and provide disclosures of the same form.`,
  },
  {
    name: ['reportingPeriod'],
    label: 'Most recent reporting period',
    isDateRange: true,
    tooltip: 'Typically an annual period.',
  },
  {
    name: ['participantOfEts'],
    label:
      'Is the customer required to participate in the Emissions Trading Scheme (ETS)?',
    tooltip: `The NZ Emissions Trading Scheme (ETS) was established by the Climate Change
              Response Act and is a market-based approach for reducing emissions of greenhouse gases.
              Participation in the ETS can be either mandatory or voluntary. The scheme is managed by the EPA.
              EPA website has information about ETS and links to register of participants.`,
  },
  {
    name: ['isLenzCustomer'],
    label: 'Is the customer a LENZ customer?',
    tooltip:
      'Please select if customer is identified as a large emitter and subject to the ANZ LENZ process',
    businessUnit: CCRA_CORPORATE_FORM_FIELD,
  },
]

const emissionsDataFields: CcraFormField[] = [
  {
    name: ['knowsEmissions'],
    label: 'Does the customer know their emissions?',
    tooltip: `Emissions refers to the release of greenhouse gases into the atmosphere over a specified area and period of time.
              The major gases are methane, nitrous oxide and carbon dioxide.`,
  },
  {
    name: ['emissionsMeasurementPlan'],
    label: 'Does the customer plan to measure their emissions?',
    tooltip: `Measurement and reduction planning is typically achieved by gathering data on emissions from business activities
              to set a baseline, and setting reduction targets. In farming, emissions baselines are typically provided by the processors.`,
  },
  {
    name: ['emissionsMeasurementMethod'],
    label: 'How were the emissions measured?',
  },
  {
    name: ['emissionsVerificationMethod'],
    label: 'Emissions verified by',
    tooltip: `Verification is provided by an independent third party such as an auditor or farm environment advisor who is provided with
              evidence that the information is correct and required actions are being performed. If you are not sure, select not verified`,
  },
  {
    name: ['newAttachments'],
    label: 'Upload the Customer Emissions Report',
  },
  {
    name: ['periodStartEndDate'],
    label: 'Period start and finish date',
    tooltip: 'Typically an annual period.',
    isDateRange: true,
  },
  {
    name: ['emissionsScope1'],
    label: 'Scope 1',
    unit: 'tCO₂e',
    tooltip:
      'Emissions from customers direct operations and activities. Tonnes of Carbon dioxide equivalent',
  },
  {
    name: ['emissionsScope2'],
    label: 'Scope 2',
    unit: 'tCO₂e',
    tooltip:
      'Emissions occurring in the electricity purchased by the customer. Tonnes of Carbon dioxide equivalent',
  },
  {
    name: ['emissionsScope3'],
    label: 'Scope 3',
    unit: 'tCO₂e',
    tooltip:
      "Emissions from the customers' supply chain for example transportation and products used by a company. Tonnes of Carbon dioxide equivalent",
  },
  {
    name: ['emissionsScope3SupplyChainAspects'],
    label: 'What aspect(s) of supply chain do the Scope 3 emissions relate to?',
  },
  {
    name: ['kgms'],
    label: 'KgMS Produced',
    unit: 'KgMS',
  },
  {
    name: ['biologicalIntensity'],
    label: 'Biological Intensity',
    unit: 'CO2e/KgMS',
  },
  {
    name: ['nonBiologicalIntensity'],
    label: 'Non Biological Intensity',
    unit: 'CO2e/KgMS',
  },
  {
    name: ['methane'],
    label: 'Methane',
    unit: 'tCO₂e',
  },
  {
    name: ['nitrogen'],
    label: 'Nitrogen Oxide (N2O)',
    unit: 'tCO₂e',
  },
  {
    name: ['carbon'],
    label: 'Carbon (CO2e)',
    unit: 'tCO₂e',
    tooltip: 'Tonnes of Carbon dioxide equivalent',
  },
  {
    name: ['tradingGroupRevenueReportingPercentage'],
    label:
      'Percentage of trading group revenue included in emissions reporting',
    unit: '%',
  },
  {
    name: ['revenueOrOutput'],
    label: 'What revenue or ouput does this Emissions Data apply to?',
    tooltip: `Units of production that the above emissions figures relate to.
              If unit of production is not listed, please select 'revenue'.`,
    isCustomField: true,
    businessUnit: CCRA_AGRI_FORM_FIELD,
  },
  {
    name: ['co2Mt'],
    label: 'CO2e (mT)',
    unit: 'C0₂e',
  },
  {
    name: ['co2eKg'],
    label: 'CO2e (kg)',
    unit: 'C0₂e',
  },
  {
    name: ['synlaitCo2eKgms'],
    label: 'CO₂e/KgMS',
    unit: 'CO₂e/KgMS',
  },
  {
    name: ['synlaitKgms'],
    label: 'KgMS',
    unit: 'KgMS',
    tooltip: '',
  },
  {
    name: ['hasCarbonCredits'],
    label:
      'Has the customer obtained carbon credits to reduce their carbon footprint?',
    tooltip:
      'Through the purchase of carbon credits, or allocation received for activity, for example the planting of forests.',
  },
  {
    name: ['carbonCreditsDetail'],
    label: 'Please provide details of the amount and source of carbon credits',
  },
  {
    name: ['Methane Dairy Herd'],
    label: 'Dairy Herd',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Methane - Dairy Herd',
  },
  {
    name: ['Methane Replacements'],
    label: 'Replacements',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Methane - Replacements',
  },
  {
    name: ['Methane Effluent'],
    label: 'Effluent',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Methane - Effluent',
  },
  {
    name: ['Carbon Dioxide Imported Feed'],
    label: 'Imported Feed',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Carbon Dioxide - Imported Feed',
  },
  {
    name: ['Carbon Dioxide Fertiliser'],
    label: 'Fertiliser',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Carbon Dioxide - Fertiliser',
  },
  {
    name: ['Carbon Dioxide Other'],
    label: 'Other',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Carbon Dioxide - Other',
  },
  {
    name: ['Nitrous Oxide Fertiliser'],
    label: 'Fertiliser',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Nitrous Oxide - Fertiliser',
  },
  {
    name: ['Nitrous Oxide Livestock'],
    label: 'Livestock',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Nitrous Oxide - Livestock',
  },
  {
    name: ['Nitrous Oxide Manure and Soil'],
    label: 'Manure and Soil',
    unit: 'CO₂e/KgMS',
    summaryLabel: 'Nitrous Oxide - Manure and Soil',
  },
  {
    name: ['Dairy Cattle'],
    label: 'Dairy Cattle',
    unit: 'kgCO2e',
    summaryLabel: 'Livestock emissions - Dairy Cattle',
  },
  {
    name: ['Beef Cattle'],
    label: 'Beef Cattle',
    unit: 'kgCO2e',
    summaryLabel: 'Livestock emissions - Beef Cattle',
  },
  {
    name: ['Sheep'],
    label: 'Sheep',
    unit: 'kgCO2e',
    summaryLabel: 'Livestock emissions - Sheep',
  },
  {
    name: ['Deer'],
    label: 'Deer',
    unit: 'kgCO2e',
    summaryLabel: 'Livestock emissions - Deer',
  },
  {
    name: ['Non-Urea Nitrogen Fertiliser'],
    label: 'non-urea nitrogen fertiliser',
    unit: 'kgCO2e',
    summaryLabel: 'Fertiliser & lime use - non-urea nitrogen fertiliser',
  },
  {
    name: ['Urea Without Urease Inhibitor'],
    label: 'Urea without urease inhibitor',
    unit: 'kgCO2e',
    summaryLabel: 'Fertiliser & lime use - urea without urease inhibitor',
  },
  {
    name: ['Urea With Urease Inhibitor'],
    label: 'Urea with urease inhibitor',
    unit: 'kgCO2e',
    summaryLabel: 'Fertiliser & lime use - urea with urease inhibitor',
  },
  {
    name: ['Limestone'],
    label: 'Limestone',
    unit: 'kgCO2e',
    summaryLabel: 'Fertiliser & lime use - Limestone',
  },
  {
    name: ['Dolomite'],
    label: 'Dolomite',
    unit: 'kgCO2e',
    summaryLabel: 'Fertiliser & lime use - Dolomite',
  },
  {
    name: ['Total Deforestation Emissions'],
    label: 'Total deforestation emissions',
    unit: 'kgCO2e',
    summaryLabel: 'Other emissions - Total deforestation emissions',
  },
  {
    name: ['Total Vegetation Offsets'],
    label: 'total vegetation offsets',
    unit: 'kgCO2e',
    summaryLabel: 'Other emissions - Total vegetation offsets',
  },
  {
    name: ['processFuels'],
    label:
      'Does the customer currently use any of the following fuels for heating/cooling or manufacturing processes? Select all answers that apply',
    businessUnit: CCRA_AGRI_FORM_FIELD,
  },
  {
    name: ['topContributors'],
    label:
      'Order the below contributors to their operational (Scope 1 + 2) emissions.',
    isCustomField: true,
  },
]

const emissionsStrategyFields: CcraFormField[] = [
  {
    name: ['emissionsReductionPlan'],
    label: 'What is the Customers emissions reduction plan',
    tooltip:
      'Click to view the provided Emissions Plan Notes below for additional information.',
  },
  {
    name: ['emissionsReductionPlanTargetMeasureExplanation'],
    label:
      'Complete for each emission target the customer has. Please provide an explanation of target measure and applicable units.',
    isCustomField: true,
  },
  {
    name: ['emissionsStrategyFurtherCommentary'],
    label: 'Further commentary on emissions strategy',
  },
  {
    name: ['hasCustomerBeenAskedAboutStrategy'],
    label:
      'Has your customer been asked by a client about their emissions or climate strategy, within the last 12 months?',
    businessUnit: CCRA_CORPORATE_FORM_FIELD,
  },
  {
    name: ['customerBeenAskedAboutStrategyExplanation'],
    label: 'Further commentary on the client request',
    businessUnit: CCRA_CORPORATE_FORM_FIELD,
  },
  {
    name: ['hasStrategyProvidedCompetitiveAdvantage'],
    label:
      'Has their emissions or climate strategy provided any competitive advantage within the last 12 months?',
    businessUnit: CCRA_CORPORATE_FORM_FIELD,
  },
  {
    name: ['strategyProvidedCompetitiveAdvantageExplanation'],
    label: 'Further commentary on the competitive advantage provided',
    businessUnit: CCRA_CORPORATE_FORM_FIELD,
  },
  {
    name: ['climateRiskManagement'],
    label:
      'How does the customer manage climate-related risks, opportunities and strategic planning? Select all answers that apply',
    businessUnit: CCRA_CORPORATE_FORM_FIELD,
  },
]

const climateRisksFields: CcraFormField[] = [
  {
    name: ['hasAssessedClimateTransitionRisks'],
    label:
      'Has the customer assessed the climate transition risks to their business?',
  },
  {
    name: ['plansToAssessClimateTransitionRisks'],
    label: 'Are they planning to assess this within the next 12 months?',
  },
  {
    name: ['transitionRisks'],
    label:
      'Select the transition risks the customer considers material to their business and indicate how they mitigate these, if at all',
    isCustomField: true,
  },
  {
    name: ['hasAssessedPhysicalRisks'],
    label: 'Has the customer assessed the physical risks to their business?',
    tooltip: `Physical risks resulting from climate change can be acute (driven by an event such as a flood or storm) or
      chronic (arising from longer-term shifts in climate patterns), presenting increasing financial risks including damage to assets,
      interruption of operations, and disruption to supply chains.`,
  },
  {
    name: ['plansToAssessPhysicalRisks'],
    label: 'Are they planning to assess this within the next 12 months?',
  },
  {
    name: ['physicalRisks'],
    label:
      'Select the physical risks the customer considers material to their business and indicate how they mitigate these, if at all',
    isCustomField: true,
  },
  {
    name: ['confirmtransitionRisks'],
    label: 'Confirm the ranked ordering is correct',
    isCustomField: true,
  },
  {
    name: ['confirmphysicalRisks'],
    label: 'Confirm the ranked ordering is correct',
    isCustomField: true,
  },
]

export const fields = {
  compliance: complianceFields,
  emissionsData: emissionsDataFields,
  emissionsStrategy: emissionsStrategyFields,
  climateRisks: climateRisksFields,
}

export const DISCLOSURE_OPTIONS = [
  { value: 'Mandatory public disclosure' },
  { value: 'Voluntary public disclosure' },
  { value: 'None' },
]

export const REQUIRES_REPORTING_PERIOD = [
  'Mandatory public disclosure',
  'Voluntary public disclosure',
]

export const EMISSIONS_MEASUREMENT_PLAN = [
  { value: 'Customer has no plans to measure emissions' },
  {
    value:
      'Customer has work underway to measure emissions within the next 12 months',
  },
  {
    value:
      'The customer knows their emissions data but does not want to divulge',
  },
]

export const EMISSIONS_MEASUREMENT_METHOD = [
  { value: 'Self-calculated (customer)' },
  { value: 'Fonterra' },
  { value: 'Toitu' },
  { value: 'Synlait' },
  { value: 'Beef + Lamb' },
  { value: 'Alltech ECO2' },
  { value: 'Climate Action Toolbox' },
  { value: 'Farmax' },
  { value: 'MfE' },
  { value: 'MyImprint' },
  { value: 'NZFAP+' },
  { value: 'Other third party' },
  { value: 'Overseer' },
  { value: 'PigGas' },
]

export const EMISSIONS_VERIFICATION_METHOD = [
  { value: 'Not verified' },
  { value: 'AsureQuality' },
  { value: 'BCorp' },
  { value: 'BraveGen' },
  { value: 'Carbon Trail' },
  { value: 'CoGo' },
  { value: 'Deloitte' },
  { value: 'Ekos' },
  { value: 'EY' },
  { value: 'Farm Environmental Consultant' },
  { value: 'KPMG' },
  { value: 'PWC' },
  { value: 'Toitu' },
  { value: 'Other' },
]

export const EMISSIONS_REDUCTION_PLAN = [
  { value: 'No current plan' },
  { value: 'Industry plan only' },
  { value: 'Partly formed plan' },
  { value: 'Fully formed plan' },
  { value: 'Fully formed and science aligned plan' },
]

export const EMISSIONS_REDUCTION_PLAN_HELP_TEXT = [
  {
    value: 'Industry plan only',
    helpText: [
      {
        id: 1,
        text: "Where a customer has no individual plan themselves, but their industry or processor has a collective action plan that they're aware of (please provide brief commentary)",
      },
    ],
    id: 1,
  },
  {
    value: 'Partly formed plan',
    helpText: [
      { id: 2, text: 'Developed by the customer' },
      { id: 3, text: 'May include targets for some scope 1 or 2 emissions' },
      {
        id: 4,
        text: 'Includes some emissions reduction activities that have been scoped/costed',
      },
    ],
    id: 2,
  },
  {
    value: 'Fully formed plan',
    helpText: [
      {
        id: 6,
        text: 'One or more science aligned targets set for scope 1 and 2 emissions with some consideration of scope 3',
      },
      {
        id: 7,
        text: 'Implementation plan includes reductions activities that have been scoped, budgeted and scheduled to commence within the next 12 months',
      },
      {
        id: 8,
        text: 'Targets are specific, timebound and cover milestones over the short, medium and long term',
      },
      { id: 9, text: 'May have been developed with external assistance' },
      {
        id: 10,
        text: 'Has been in place for one or more years and a process of continuous improvement (tracking progress against targets)',
      },
    ],
    id: 3,
  },
  {
    value: 'Fully formed and science aligned plan',
    helpText: [
      {
        id: 11,
        text: (
          <span key={'Science Based Targets Initiative'}>
            Targets are consistent with the{' '}
            <a
              href="https://sciencebasedtargets.org/"
              target="_blank"
              rel="noopener noreferrer"
            >
              Science Based Targets Initiative (SBTi)
            </a>
            , providing a clearly-defined path to reduce emissions to meet the
            Paris Agreement goals of limiting global warming to 1.5 degrees
            Celsius above pre-industrial levels
          </span>
        ),
      },
    ],
    id: 4,
  },
]

export const EMISSIONS_DATA_OUTPUT_UNITS = [
  { label: 'Kg Milk Solids', value: 'kgMilkSolids' },
  { label: 'Kg Meat', value: 'kgMeat' },
  { label: 'Kg Fibre', value: 'kgFibre' },
  { label: 'Kg Meat & Fibre', value: 'kgMeatAndFibre' },
  { label: 'Trays', value: 'trays' },
  { label: 'Revenue', value: 'revenue' },
  { label: 'Kilometres', value: 'km' },
  { label: 'Tonnes', value: 't' },
  { label: 'Litres', value: 'l' },
]

export const RANKED_EMISSIONS_CONTRIBUTORS = [
  {
    contributor: 'Agricultural processes - enteric',
    helpText: 'Methane (CH4) produced in the gut of animals',
    index: 1,
    isCustomContributor: false,
  },
  {
    contributor: 'Agricultural processes - other',
    helpText:
      'Includes gases from urine and dung, effluent and fertiliser, and feed',
    index: 2,
    isCustomContributor: false,
  },
  {
    contributor: 'Fuel use - plant/processes',
    helpText: `Where a company is using fossil fuel for heating/cooling or manufacturing
                 processes eg. using natural gas / LPG / diesel / coal`,
    index: 3,
    isCustomContributor: false,
  },
  {
    contributor: 'Fuel use - vehicles',
    index: 4,
    isCustomContributor: false,
    helpText: 'Fossil fuel use in vehicles typically petrol/ diesel /LPG',
  },
  {
    contributor: 'Purchased electricity (Scope 2)',
    index: 5,
    isCustomContributor: false,
    helpText: 'Electricity sourced from a network provider',
  },
]

export const RANKED_TRANSITION_RISKS = [
  {
    risk: 'New technology',
    helpText: 'Technological improvements or innovations',
    index: 1,
    measureTaken: '',
    materialToCustomer: '',
  },
  {
    risk: 'Change in regulation',
    helpText:
      'Sudden regulatory change aimed at reducing actions that contribute to climate change or promoting adaption',
    index: 2,
    measureTaken: '',
    materialToCustomer: '',
  },
  {
    risk: 'Legal action',
    helpText: `As the value of loss and damage from climate change grows litigation risk grows.
        Examples include liability for climate change, failure of organizations to mitigate impacts,
         failure to adapt to climate change, and the insufficiency of disclosure around material financial risks`,
    index: 3,
    measureTaken: '',
    materialToCustomer: '',
  },
  {
    risk: 'Market change',
    helpText:
      'Shifts in supply and demand for certain commodities, products and services. This may be an opportunity for some customers or a risk for others',
    index: 4,
    measureTaken: '',
    materialToCustomer: '',
  },
  {
    risk: 'Social impact',
    helpText:
      'Climate change may cause uneven impacts, disproportionality affecting communities and groups',
    index: 5,
    measureTaken: '',
    materialToCustomer: '',
  },
]

export const RANKED_PHYSICAL_RISKS = [
  {
    risk: 'Coastal flood & sea level rises',
    helpText: 'Caused by storm surges which will be worsened by sea level rise',
    index: 1,
    measureTaken: '',
    isInsured: false,
    materialToCustomer: '',
  },
  {
    risk: 'Extreme heat (incl. fire)',
    helpText: `Includes heat stress from consecutive days of very hot temperatures that will impact people, animals and plants.
        It also increases risk of wildfires either of which may damage assets or impact operations/production`,
    index: 2,
    measureTaken: '',
    isInsured: false,
    materialToCustomer: '',
  },
  {
    risk: 'Extreme weather (incl. inland flood)',
    helpText: 'Such as storms and cyclones cause flooding and wind damage',
    index: 3,
    measureTaken: '',
    isInsured: false,
    materialToCustomer: '',
  },
  {
    risk: 'Chronic change in climatic conditions (incl. drought)',
    helpText: `Refers to both the increasing volatility in the climate as the planet slowly warms and the general
        trend for the customer in their particular geography. This may include increased severity/frequency
        of droughts and loss of species/arrival of new pests and diseases`,
    index: 4,
    measureTaken: '',
    isInsured: false,
    materialToCustomer: '',
  },
  {
    risk: 'Loss of natural resources',
    helpText:
      'Refers to loss or damage to ecosystems and environments including agricultural areas due to weather events',
    index: 5,
    measureTaken: '',
    isInsured: false,
    materialToCustomer: '',
  },
]

export const ROW_PROPS = {
  gutter: 16,
}

export const COLUMN_PROPS = {
  span: 12,
}

export const emissionsTargetScopeOptions = [
  { value: 'Scope 1 and 2', label: 'Scope 1 and 2' },
  { value: 'Scope 1', label: 'Scope 1' },
  { value: 'Scope 2', label: 'Scope 2' },
  { value: 'Scope 3', label: 'Scope 3' },
]

export const emissionsTargetTypeOptions = [
  {
    value: 'percentageReduction',
    label: 'A percentage reduction of absolute emissions of',
  },
  {
    value: 'absoluteReduction',
    label: 'An absolute reduction in emissions from',
  },
  {
    value: 'intensityReduction',
    label: 'A reduction in emissions intensity from',
  },
]

export const emissionsTargetIntensityUnitOfOptions = [
  { value: 'Revenue', label: 'Revenue' },
  { value: 'Milk Solids', label: 'Milk Solids' },
  { value: 'Kg Meat', label: 'Kg Meat' },
  { value: 'Kg Fibre', label: 'Kg Fibre' },
  { value: 'Kilometres', label: 'Kilometres' },
  { value: 'Tonnes', label: 'Tonnes' },
  { value: 'Litres', label: 'Litres' },
]

export const emissionsTargetUnitOptions = [
  { value: 'kgCO2', label: 'kgCO2' },
  { value: 'kgCO2e', label: 'kgCO2e' },
  { value: 'tCO2', label: 'tCO2' },
  { value: 'tCO2e', label: 'tCO2e' },
]
