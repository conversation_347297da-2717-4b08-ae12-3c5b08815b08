import { memo } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import EsstReportItemSummary from './EsstReportItemSummary'
import EsstReportItemSectionForm from './EsstReportItemSectionForm'
import EsstReportItemSummaryView from './EsstReportItemSummaryView'
import type { SchemaLink, SchemaRoute } from './useSchemaRoutes'

type Props = {
  esstReportItemId: number
  esstReportItemValid?: boolean
  routes: SchemaRoute[]
}

function EsstSchemaRoutes({
  esstReportItemId,
  esstReportItemValid,
  routes,
}: Props) {
  if (!routes.length) return null

  return (
    <Routes>
      <Route
        index
        path="/"
        element={
          <>
            {esstReportItemValid ? (
              <EsstReportItemSummary esstReportItemId={esstReportItemId} />
            ) : (
              <Navigate to={routes[0].to} replace />
            )}
          </>
        }
      />
      <Route
        path="summary"
        element={
          <EsstReportItemSummaryView esstReportItemId={esstReportItemId} />
        }
      />
      {routes.map((route, i) => {
        const prev: SchemaLink | undefined = i === 0 ? undefined : routes[i - 1]
        const next: SchemaLink =
          i === routes.length - 1
            ? {
                title: 'Summary',
                to: 'summary',
              }
            : routes[i + 1]

        return (
          <Route
            key={route.to}
            path={route.to}
            element={
              <EsstReportItemSectionForm
                rootNode={route.section}
                reportItemId={esstReportItemId}
                prev={prev}
                next={next}
              />
            }
          />
        )
      })}
    </Routes>
  )
}

export default memo(EsstSchemaRoutes)
