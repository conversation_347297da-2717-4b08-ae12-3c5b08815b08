import React, { type ReactNode } from 'react'
import { useLocalStorage } from 'react-use'

const defaultState = {
  // Just putting the id here instead of its own context out of laziness,
  // the link item needs it for the URLs.
  // If the sidebar were going to be reused and abstracted,
  // this could change to something like a basepath instead.
  customerId: undefined as number | undefined,
  collapsed: false,
  disabled: false,
}

type State = typeof defaultState & {
  setCollapsed: (collapsed: boolean) => void
}

const SidebarContext = React.createContext<State | undefined>(undefined)

interface SidebarProviderProps
  extends Partial<Pick<State, 'customerId' | 'disabled'>> {
  children: ReactNode
}

export function SidebarProvider({
  children,
  customerId,
  disabled = false,
}: SidebarProviderProps) {
  const [collapsed, setCollapsed] = useLocalStorage(
    'customer-layout-sidebar',
    false
  )

  const value = {
    disabled,
    customerId,
    collapsed: !!collapsed,
    setCollapsed,
  }

  return (
    <SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>
  )
}

export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider')
  }
  return context
}
