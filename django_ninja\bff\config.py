"""
BFF Configuration Module

This module contains configuration settings for the Backend for Frontend (BFF) service.
It provides centralized configuration management for PingFederate authentication,
rate limiting, backend API routing, and other BFF-specific settings.
"""

import os
from django.conf import settings


class BFFConfig:
    """Centralized configuration for BFF service"""
    
    # PingFederate Authentication Configuration
    PINGFEDERATE = {
        'ISSUER': os.environ.get('PINGFEDERATE_ISSUER', 'https://pingfederate.example.com'),
        'AUDIENCE': os.environ.get('PINGFEDERATE_AUDIENCE', 'esgis-api'),
        'JWKS_URL': os.environ.get('PINGFEDERATE_JWKS_URL', 'https://pingfederate.example.com/.well-known/jwks'),
        'ALGORITHM': os.environ.get('PINGFEDERATE_ALGORITHM', 'RS256'),
        'LEEWAY': int(os.environ.get('PINGFEDERATE_LEEWAY', '30')),  # seconds
        'CACHE_TIMEOUT': int(os.environ.get('PINGFEDERATE_CACHE_TIMEOUT', '3600')),  # 1 hour
    }
    
    # Rate Limiting Configuration
    RATE_LIMITING = {
        'ENABLED': os.environ.get('BFF_RATE_LIMITING_ENABLED', 'true').lower() == 'true',
        'DEFAULT_LIMIT': int(os.environ.get('BFF_DEFAULT_RATE_LIMIT', '100')),  # requests per window
        'DEFAULT_WINDOW': int(os.environ.get('BFF_DEFAULT_RATE_WINDOW', '3600')),  # seconds (1 hour)
        'BURST_LIMIT': int(os.environ.get('BFF_BURST_RATE_LIMIT', '20')),  # requests per burst window
        'BURST_WINDOW': int(os.environ.get('BFF_BURST_RATE_WINDOW', '60')),  # seconds (1 minute)
        'REDIS_KEY_PREFIX': os.environ.get('BFF_RATE_LIMIT_PREFIX', 'bff_rate_limit'),
    }
    
    # Redis Configuration
    REDIS = {
        'HOST': os.environ.get('REDIS_HOST', 'localhost'),
        'PORT': int(os.environ.get('REDIS_PORT', '6379')),
        'DB': int(os.environ.get('REDIS_DB', '0')),
        'PASSWORD': os.environ.get('REDIS_PASSWORD', ''),
        'TIMEOUT': int(os.environ.get('REDIS_TIMEOUT', '5')),
        'DECODE_RESPONSES': True,
    }
    
    # Backend API Configuration
    BACKEND_APIS = {
        'riskradar': {
            'base_url': os.environ.get('RISKRADAR_API_URL', 'http://localhost:8000/bff/api/riskradar'),
            'timeout': int(os.environ.get('RISKRADAR_API_TIMEOUT', '30')),
            'retry_attempts': int(os.environ.get('RISKRADAR_API_RETRIES', '3')),
            'health_endpoint': '/health',
        },
        'green': {
            'base_url': os.environ.get('GREEN_API_URL', 'http://localhost:8000/api/green'),
            'timeout': int(os.environ.get('GREEN_API_TIMEOUT', '30')),
            'retry_attempts': int(os.environ.get('GREEN_API_RETRIES', '3')),
            'health_endpoint': '/health',
        },
        'finance': {
            'base_url': os.environ.get('FINANCE_API_URL', 'http://localhost:8000/api/finance'),
            'timeout': int(os.environ.get('FINANCE_API_TIMEOUT', '30')),
            'retry_attempts': int(os.environ.get('FINANCE_API_RETRIES', '3')),
            'health_endpoint': '/health',
        },
        'ccra': {
            'base_url': os.environ.get('CCRA_API_URL', 'http://localhost:8000/api/ccra'),
            'timeout': int(os.environ.get('CCRA_API_TIMEOUT', '30')),
            'retry_attempts': int(os.environ.get('CCRA_API_RETRIES', '3')),
            'health_endpoint': '/health',
        },
        'propertyflow': {
            'base_url': os.environ.get('PROPERTYFLOW_API_URL', 'http://localhost:8000/api/propertyflow'),
            'timeout': int(os.environ.get('PROPERTYFLOW_API_TIMEOUT', '30')),
            'retry_attempts': int(os.environ.get('PROPERTYFLOW_API_RETRIES', '3')),
            'health_endpoint': '/health',
        },
    }
    
    # Logging Configuration
    LOGGING = {
        'LEVEL': os.environ.get('BFF_LOG_LEVEL', 'INFO'),
        'FORMAT': os.environ.get('BFF_LOG_FORMAT', 
                                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        'LOG_REQUESTS': os.environ.get('BFF_LOG_REQUESTS', 'true').lower() == 'true',
        'LOG_RESPONSES': os.environ.get('BFF_LOG_RESPONSES', 'false').lower() == 'true',
    }
    
    # Security Configuration
    SECURITY = {
        'ALLOWED_ORIGINS': os.environ.get('BFF_ALLOWED_ORIGINS', '*').split(','),
        'MAX_REQUEST_SIZE': int(os.environ.get('BFF_MAX_REQUEST_SIZE', '10485760')),  # 10MB
        'REQUIRE_HTTPS': os.environ.get('BFF_REQUIRE_HTTPS', 'false').lower() == 'true',
        'CORS_ENABLED': os.environ.get('BFF_CORS_ENABLED', 'true').lower() == 'true',
    }
    
    # Response Aggregation Configuration
    AGGREGATION = {
        'DEFAULT_TYPE': os.environ.get('BFF_DEFAULT_AGGREGATION', 'merge'),
        'MAX_CONCURRENT_REQUESTS': int(os.environ.get('BFF_MAX_CONCURRENT_REQUESTS', '10')),
        'TIMEOUT_PER_REQUEST': int(os.environ.get('BFF_AGGREGATION_TIMEOUT', '30')),
        'INCLUDE_METADATA': os.environ.get('BFF_INCLUDE_METADATA', 'true').lower() == 'true',
    }
    
    # Health Check Configuration
    HEALTH_CHECK = {
        'ENABLED': os.environ.get('BFF_HEALTH_CHECK_ENABLED', 'true').lower() == 'true',
        'CHECK_BACKEND_APIS': os.environ.get('BFF_HEALTH_CHECK_BACKENDS', 'true').lower() == 'true',
        'CHECK_REDIS': os.environ.get('BFF_HEALTH_CHECK_REDIS', 'true').lower() == 'true',
        'CHECK_PINGFEDERATE': os.environ.get('BFF_HEALTH_CHECK_PINGFEDERATE', 'true').lower() == 'true',
        'TIMEOUT': int(os.environ.get('BFF_HEALTH_CHECK_TIMEOUT', '5')),
    }
    
    @classmethod
    def get_backend_api_config(cls, api_name: str) -> dict:
        """Get configuration for a specific backend API"""
        return cls.BACKEND_APIS.get(api_name, {})
    
    @classmethod
    def is_rate_limiting_enabled(cls) -> bool:
        """Check if rate limiting is enabled"""
        return cls.RATE_LIMITING['ENABLED']
    
    @classmethod
    def get_rate_limit_config(cls) -> dict:
        """Get rate limiting configuration"""
        return cls.RATE_LIMITING
    
    @classmethod
    def get_pingfederate_config(cls) -> dict:
        """Get PingFederate configuration"""
        return cls.PINGFEDERATE
    
    @classmethod
    def get_redis_config(cls) -> dict:
        """Get Redis configuration"""
        return cls.REDIS
    
    @classmethod
    def validate_config(cls) -> list:
        """Validate BFF configuration and return list of issues"""
        issues = []
        
        # Validate PingFederate config
        if not cls.PINGFEDERATE['ISSUER']:
            issues.append("PINGFEDERATE_ISSUER is not configured")
        
        if not cls.PINGFEDERATE['AUDIENCE']:
            issues.append("PINGFEDERATE_AUDIENCE is not configured")
        
        if not cls.PINGFEDERATE['JWKS_URL']:
            issues.append("PINGFEDERATE_JWKS_URL is not configured")
        
        # Validate backend APIs
        for api_name, config in cls.BACKEND_APIS.items():
            if not config.get('base_url'):
                issues.append(f"Backend API {api_name} base_url is not configured")
        
        # Validate rate limiting
        if cls.RATE_LIMITING['ENABLED']:
            if cls.RATE_LIMITING['DEFAULT_LIMIT'] <= 0:
                issues.append("Rate limiting default limit must be positive")
            
            if cls.RATE_LIMITING['DEFAULT_WINDOW'] <= 0:
                issues.append("Rate limiting default window must be positive")
        
        return issues


# Global configuration instance
bff_config = BFFConfig()
