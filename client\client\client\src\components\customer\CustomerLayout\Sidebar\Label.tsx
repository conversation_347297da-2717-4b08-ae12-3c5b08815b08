import { Tooltip } from 'antd'
import classNames from 'classnames'
import React, { type ReactNode } from 'react'
import { useSidebar } from './context'
import type { MenuItem } from './helpers'
import styles from './Label.module.css'

type Props = MenuItem & {
  active?: boolean
  disabled?: boolean
  icon: ReactNode
}

const Label = ({ active = false, disabled, icon, label }: Props) => {
  const { collapsed } = useSidebar()

  return (
    <span
      className={classNames(styles.container, {
        [styles.active]: active,
        [styles.disabled]: disabled,
        [styles.collapsed]: collapsed,
      })}
    >
      <span className={styles.icon}>
        {collapsed ? (
          <Tooltip title={label} placement="right">
            {icon}
          </Tooltip>
        ) : (
          icon
        )}
      </span>
      <div style={{ lineHeight: 1.3 }}>
        <span className={styles.text}>{label}</span>
      </div>
    </span>
  )
}

export default Label
