import { DownOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import { useLocation, useParams, NavLink } from 'react-router-dom'
import type { Customer } from '@store/services/sdk'
import { equalsProperty, not } from '@util/helpers'
import styles from './CustomerSelect.module.css'

type SelectableCustomer = Pick<Customer, 'pk' | 'entityName'>

type Props = React.HTMLProps<HTMLSpanElement> & {
  customers: SelectableCustomer[] | undefined
  selectedCustomerId?: SelectableCustomer['pk']
}

const CustomerSelect = ({
  customers = [],
  selectedCustomerId,
  className,
  ...props
}: Props) => {
  const location = useLocation()
  const { customerId } = useParams()

  const selectedCustomer =
    customers.find(equalsProperty('pk', selectedCustomerId)) || customers[0]

  const filteredCustomers = customers.filter(
    not(equalsProperty('pk', selectedCustomer?.pk))
  )

  if (!customerId)
    throw new Error('Must be used within a path with a :customerId parameter')

  return (
    <span className={classNames(styles.container, className)} {...props}>
      <button type="button" className={styles.button}>
        {selectedCustomer?.entityName}{' '}
        {Boolean(filteredCustomers.length) && (
          <DownOutlined className={styles.arrow} />
        )}
      </button>
      {Boolean(filteredCustomers.length) && (
        <div className={styles.overlay}>
          <ul className={styles.list}>
            {filteredCustomers.map((customer) => (
              <li key={customer.pk}>
                <NavLink
                  to={{
                    ...location,
                    pathname: location.pathname.replace(
                      customerId,
                      customer.pk.toString()
                    ),
                  }}
                >
                  {customer.entityName}
                </NavLink>
              </li>
            ))}
          </ul>
        </div>
      )}
    </span>
  )
}

export default CustomerSelect
