import { Button, type ButtonProps, Popover, type PopoverProps } from 'antd'
import Color from 'color'
import React from 'react'
import useLayerStyle from '@hooks/useLayerStyle'
import type { LayerContext } from '@store/features/map/types'
import { charFromIndex } from '@util/labels'
import ExplorerLayerStyleSelection from './ExplorerLayerStyleSelection'

interface Props {
  context: LayerContext
  id: number
  index: number
  buttonProps?: ButtonProps
  popoverProps?: PopoverProps
}

const ExplorerLayerStyleSelectionButton = ({
  context,
  id,
  index,
  buttonProps,
  popoverProps,
}: Props) => {
  const { layerStyle } = useLayerStyle('address', id)

  return (
    <Popover
      placement="left"
      trigger="click"
      {...popoverProps}
      content={<ExplorerLayerStyleSelection context={context} id={id} />}
    >
      <Button
        icon={charFromIndex(index)}
        shape="circle"
        size="small"
        {...buttonProps}
        style={{
          color: 'inherit',
          borderWidth: 2,
          lineHeight: 1,
          ...buttonProps?.style,
          borderColor: layerStyle.color,
          background: Color(layerStyle.color)
            .alpha(layerStyle.fillOpacity)
            .string(),
        }}
        type="default"
      />
    </Popover>
  )
}

export default ExplorerLayerStyleSelectionButton
