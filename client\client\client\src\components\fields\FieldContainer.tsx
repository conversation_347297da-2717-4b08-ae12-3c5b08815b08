import classNames from 'classnames'
import type { ReactNode } from 'react'
import React from 'react'
import Markdown from 'react-markdown'
import font from '@styles/new/font.module.css'
import styles from './FieldContainer.module.css'

type Props = {
  title: ReactNode
  children: ReactNode
  className?: string
  tagName?: 'label' | 'fieldset'
  message?: string
  guidance?: string
}

export function FieldMessage({ children }: { children: ReactNode }) {
  if (typeof children !== 'string' || !children) return null

  return (
    <div className={styles.message}>
      <Markdown linkTarget="_blank">{children}</Markdown>
    </div>
  )
}

export default function FieldContainer({
  title,
  children,
  className,
  tagName = 'label',
  message,
  guidance,
}: Props) {
  const ContainerComponent = tagName
  const TitleComponent = tagName === 'label' ? 'span' : 'legend'

  return (
    <div
      className={classNames(
        className,
        styles.container,
        font.formatting,
        'FieldContainer'
      )}
    >
      <div className={styles.inner}>
        <div className={styles.content}>
          <ContainerComponent className={styles.component}>
            <TitleComponent className={styles.title}>
              <Markdown linkTarget="_blank">{title}</Markdown>
            </TitleComponent>
            <div>{children}</div>
          </ContainerComponent>
          {!!message && <FieldMessage>{message}</FieldMessage>}
        </div>
      </div>
      <div>
        {!!guidance && (
          <div className={styles.guidance}>
            <Markdown linkTarget="_blank">{guidance}</Markdown>
          </div>
        )}
      </div>
    </div>
  )
}
