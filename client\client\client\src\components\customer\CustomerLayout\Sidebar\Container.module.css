.container {
  composes: menu from "@styles/new/font.module.css";

  --menu-width: 100%;
  --menu-inset: var(--layout-inset);
  --icon-width: 16px;

  width: var(--menu-width);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  border-right: 1px solid var(--color-border);
}

.container :global {
  a {
    text-decoration: none;
  }
}

.collapsed {
  --menu-width: calc(var(--icon-width) + var(--menu-inset) * 2);

  .title path {
    display: none;
    opacity: 0;
  }
}

.header,
.footer {
  padding: 0 var(--menu-inset);
}

.header,
.footer {
  flex: 0 0 auto;
}

.header {
  display: flex;
  height: var(--header-height);
  padding-top: var(--space-1);
  padding-bottom: var(--space-1);
  border-bottom: 1px solid var(--color-border);
}

.title {
  composes: body from "@styles/font.module.css";

  width: 0;
  margin: 0;
  overflow: visible;
  transform: translateY(2px);

  >svg {
    flex: 0 0 auto;
    width: 155px;
    height: auto;
  }
}

.menu {
  width: 200px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  border-bottom: 1px solid var(--color-border);
}

.footer {
  display: flex;
  margin-top: auto;
  padding-top: var(--space-2);
  padding-bottom: var(--space-2);
}



footer {
  @media (min-width: 600px) {

    position: fixed;
    bottom: 0;
    width: 100%;
  }

  @media (min-width: 320px) and (max-width: 480px) {
    position: relative;
    bottom: auto;
    width: auto;
  }

}