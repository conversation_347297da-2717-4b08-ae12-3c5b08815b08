import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Radio, Space, Spin, Switch } from 'antd'
import type React from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { FeatureGroup } from 'react-leaflet'
import Control from 'react-leaflet-control'
import { useParams } from 'react-router-dom'
import { MapContainer } from '@components/generic'
import { ViewportTitlesLayer } from '@components/map/layers/ViewportTitlesLayer'
import { TitleLayerProvider } from '@components/map/titleLayerContext'
import type { AddressFeature } from '@models/address/AddressFeatureCollection'
import type { AddressNeighbourFeatureCollection } from '../../models/address/AddressNeighbour'
import type { AnzUnionFeatureCollection } from '../../models/gis/AnzUnionFeatureCollection'
import type { ElevationFeatureCollection } from '../../models/gis/ElevationFeatureCollection'
import type { SmapFamilyFeatureCollection } from '../../models/gis/SmapFamilyFeatureCollection'
import type { TitleFeatureCollection } from '../../models/title/TitleFeatureCollection'
import {
  useGetAddressElevationQuery,
  useGetAddressNeighboursQuery,
  useGetAddressQuery,
  useGetAddressTitlesQuery,
  useGetAddressUnionQuery,
  useGetSmapFamilyQuery,
} from '../../store/services/address'
import type { GeoFeatureGroup } from '../../types'
import { AgriMap } from '../AgriMap'
import { MeasurementLayer } from '../map/layers/MeasurementLayer'
import { SmapDominantFamilyFeatureLayer } from '../physicalProperties/smap/SmapFeatureLayer'
import { UnionFeatureLayer } from '../physicalProperties/union/UnionFeatureLayer'
import { usePropertyAreas } from './hooks/usePropertyAreas'
import { NeighbourFeatureLayer } from './layers/NeighbourFeatureLayer'

export interface MapState {
  elevation: ElevationFeatureCollection | undefined
  union: AnzUnionFeatureCollection | undefined
  smapFamily: SmapFamilyFeatureCollection | undefined
  titles: TitleFeatureCollection | undefined
  neighbours: AddressNeighbourFeatureCollection | undefined
}

export interface SelectedAddressMapProps {
  overrideAddressId?: string
  overrideTitles?: TitleFeatureCollection
  limitedMode?: boolean
  addresses?: AddressFeature[]
  addressesLoading?: boolean
  children?: React.ReactNode | React.ReactNode[]
  doNotFitBounds?: boolean
  disableMeasurementLayer?: boolean
}

/**
 * @deprecated Will be replaced with new map implementation
 */
export const SelectedAddressMap = (props: SelectedAddressMapProps) => {
  const {
    doNotFitBounds,
    disableMeasurementLayer,
    overrideAddressId,
    overrideTitles,
    limitedMode,
  } = props

  const [selector, setSelector] = useState<string>()
  const [viewNeighbours, setViewNeighbours] = useState<boolean>(false)

  const { addressId: inputAddressId = '' } = useParams()

  const addressId = useMemo(
    () => (inputAddressId || overrideAddressId) ?? skipToken,
    [inputAddressId, overrideAddressId]
  )

  const { data: address, isFetching: addressIsLoading } = useGetAddressQuery(
    addressId,
    {
      skip: addressId === '',
    }
  )
  const { data: elevation, isFetching: elevationIsLoading } =
    useGetAddressElevationQuery(addressId, {
      skip: addressId === '' || limitedMode,
    })
  const { data: union, isFetching: unionIsLoading } = useGetAddressUnionQuery(
    addressId,
    {
      skip: addressId === '' || limitedMode,
    }
  )
  const { data: smapFamily, isFetching: smapFamilyIsLoading } =
    useGetSmapFamilyQuery(addressId, {
      skip: addressId === '' || limitedMode,
    })
  const { data: titles, isFetching: titlesIsLoading } =
    useGetAddressTitlesQuery(addressId, {
      skip: addressId === '',
    })
  const { data: neighbours, isFetching: neighboursIsLoading } =
    useGetAddressNeighboursQuery(addressId, {
      skip: addressId === '' || limitedMode || !viewNeighbours,
    })

  const [state, setState] = useState<{ [addressId: string]: MapState }>({})

  useEffect(() => {
    setState((prev) => {
      const newState = { ...prev }
      if (!newState[addressId as string]) {
        newState[addressId as string] = {
          elevation: undefined,
          union: undefined,
          smapFamily: undefined,
          titles: undefined,
          neighbours: undefined,
        }
      }
      return newState
    })
  }, [addressId])

  const updateStateValue = useCallback(
    // biome-ignore lint: Will be removed
    (key: string, value: any) => {
      setState((prev) => {
        const newState = { ...prev }
        newState[addressId as string][key as keyof MapState] = value
        return newState
      })
    },
    [addressId]
  )

  // biome-ignore lint: Will be removed
  useEffect(() => {
    updateStateValue('titles', titles)
  }, [addressId, titles, updateStateValue])

  // biome-ignore lint: Will be removed
  useEffect(() => {
    updateStateValue('neighbours', neighbours)
  }, [addressId, neighbours, updateStateValue])

  // biome-ignore lint: Will be removed
  useEffect(() => {
    updateStateValue('union', union)
  }, [addressId, union, updateStateValue])

  // biome-ignore lint: Will be removed
  useEffect(() => {
    updateStateValue('smapFamily', smapFamily)
  }, [addressId, smapFamily, updateStateValue])

  // biome-ignore lint: Will be removed
  useEffect(() => {
    updateStateValue('elevation', elevation)
  }, [addressId, elevation, updateStateValue])

  const propertyAreas = usePropertyAreas(titles?.features || [])

  const latLng = useMemo(() => {
    // Use current state extent location or current address latLng
    // If that fails, use last remembered location or default latLng
    return {
      lat: address?.lat ?? -36.8454297,
      lng: address?.lng ?? 174.764475,
    }
  }, [address])

  const isLoading = useMemo(() => {
    return (
      addressIsLoading ||
      elevationIsLoading ||
      unionIsLoading ||
      smapFamilyIsLoading ||
      titlesIsLoading ||
      neighboursIsLoading ||
      props.addressesLoading
    )
  }, [
    props.addressesLoading,
    addressIsLoading,
    elevationIsLoading,
    unionIsLoading,
    smapFamilyIsLoading,
    titlesIsLoading,
    neighboursIsLoading,
  ])

  const indicator = useMemo(() => {
    if (!props.addressesLoading) {
      return <></>
    }
    return <Spin />
  }, [props.addressesLoading])

  return (
    <TitleLayerProvider>
      <MapContainer loading={isLoading}>
        {indicator}
        <AgriMap doNotFitBounds={doNotFitBounds} center={latLng}>
          <FeatureGroup>
            {!limitedMode &&
            (selector === 'luc' ||
              selector === 'contour' ||
              selector === 'vegetation' ||
              selector === 'ps') ? (
              <UnionFeatureLayer
                features={
                  // biome-ignore lint: Will be removed
                  state[addressId as string]?.union as GeoFeatureGroup<any>
                }
                fillOpacity={0.25}
                propertyAreas={propertyAreas}
                selectedField={selector}
              />
            ) : (
              <></>
            )}
            <ViewportTitlesLayer
              forcedTitleMode
              position={1}
              titles={
                overrideTitles
                  ? overrideTitles
                  : state[addressId as string]?.titles
              }
            />
            {!limitedMode && selector === 'smap' ? (
              <SmapDominantFamilyFeatureLayer
                features={
                  // biome-ignore lint: Will be removed
                  state[addressId as string]?.smapFamily as GeoFeatureGroup<any>
                }
                fillOpacity={0.25}
                propertyAreas={propertyAreas}
              />
            ) : null}
            {!limitedMode && viewNeighbours ? (
              <NeighbourFeatureLayer
                neighbours={state[addressId as string]?.neighbours}
              />
            ) : (
              <></>
            )}
          </FeatureGroup>
          {!limitedMode ? (
            <Control position="bottomleft" className="address-layer-controls">
              <Space>
                <span>Neighbours</span>
                <Switch
                  checked={viewNeighbours}
                  onChange={() => setViewNeighbours(!viewNeighbours)}
                />
              </Space>
            </Control>
          ) : (
            <></>
          )}
          {!limitedMode ? (
            <Control position="bottomleft" className="address-layer-controls">
              <Radio.Group
                onChange={(e) => setSelector(e.target.value)}
                value={selector}
              >
                <Space direction="vertical">
                  <Radio value="titles">None</Radio>
                  <Radio value="contour">Contour</Radio>
                  <Radio value="ps">Soil</Radio>
                  {(state[addressId as string]?.smapFamily?.features ?? [])
                    .length > 0 ? (
                    <Radio value="smap">Soil Family</Radio>
                  ) : null}
                  <Radio value="luc">LUC</Radio>
                  <Radio value="vegetation">Vegetation</Radio>
                </Space>
              </Radio.Group>
            </Control>
          ) : (
            <></>
          )}
          {disableMeasurementLayer ? null : <MeasurementLayer />}
          {props?.children}
        </AgriMap>
      </MapContainer>
    </TitleLayerProvider>
  )
}
