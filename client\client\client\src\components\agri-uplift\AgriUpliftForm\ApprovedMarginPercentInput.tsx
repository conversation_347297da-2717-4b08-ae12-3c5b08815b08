import { LoadingOutlined } from '@ant-design/icons'
import { Form, InputNumber } from 'antd'
import type { HtmlHTMLAttributes } from 'react'
import InfoIconTooltip from '@components/InfoIconTooltip'
import {
  type AgriUplift,
  useFinanceAgriUpliftProductRetrieveQuery,
} from '@store/services/finance/codegen'
import { PLACEHOLDER_CHAR } from '@util/const'
import { ACRONYM, HOST_PRODUCT_IDENTIFIER } from '../const'
import styles from './ApprovedMarginPercentInput.module.css'
import { createPrecisionRule, sumDecimals } from './helpers'

const DECIMAL_PLACES = 2

const OverallMarginPercent = ({
  approvedMarginPercent,
  lessDiscountPercent,
}: {
  approvedMarginPercent?: string
  lessDiscountPercent: string | undefined
}) => {
  if (!(approvedMarginPercent && lessDiscountPercent))
    return <>{PLACEHOLDER_CHAR}</>

  return (
    <>
      {sumDecimals(Number(approvedMarginPercent), Number(lessDiscountPercent))}
    </>
  )
}

const path: keyof AgriUplift = 'approvedMarginPercent'

const ApprovedMarginPercentInput = () => {
  const { data, isLoading } = useFinanceAgriUpliftProductRetrieveQuery()

  return (
    <div className={styles.container}>
      <Form.Item
        label={
          <HelpTitle tooltipText="This is the ‘Customer Margin’ that has been approved in the ‘Corporate & Business Lending Pricing Tool' (This is the margin BEFORE the discount has been applied).">
            Approved Margin %
          </HelpTitle>
        }
        name={path}
        rules={[createPrecisionRule(DECIMAL_PLACES)]}
      >
        <InputNumber step={0.1} precision={DECIMAL_PLACES} />
      </Form.Item>
      <Form.Item label={`Less ${ACRONYM} Discount %`}>
        <div>{isLoading ? <LoadingOutlined /> : data?.lessDiscountPercent}</div>
      </Form.Item>
      <Form.Item
        name={path}
        label={
          <HelpTitle
            tooltipText={`This is the overall Customer Margin with the discount applied. The customer’s all up interest rate will be the floating ${HOST_PRODUCT_IDENTIFIER} base rate + this overall customer margin.`}
          >
            Overall Margin %
          </HelpTitle>
        }
        valuePropName="approvedMarginPercent"
      >
        {isLoading ? (
          <LoadingOutlined />
        ) : (
          <OverallMarginPercent
            lessDiscountPercent={data?.lessDiscountPercent}
          />
        )}
      </Form.Item>
    </div>
  )
}

export function HelpTitle({
  children,
  tooltipText,
  ...props
}: HtmlHTMLAttributes<HTMLDivElement> & { tooltipText: string }) {
  return (
    <div
      {...props}
      style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-0)' }}
    >
      <div style={{ flex: 1 }}>{children}</div>
      <InfoIconTooltip title={tooltipText} />
    </div>
  )
}

export default ApprovedMarginPercentInput
