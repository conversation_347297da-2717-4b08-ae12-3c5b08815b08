import { Controller, useFormContext } from 'react-hook-form'
import type { FieldNode } from '@store/services/fields/codegen'
import { not } from '@util/helpers'
import { type FieldValue, updateValue, valueByFieldId } from './util'

export default function FieldValuesController({
  field,
  component: ComponentElement,
}: {
  field: FieldNode
  component: (props: {
    field: FieldNode
    value: FieldValue | undefined
    onChange(value: FieldValue | undefined): void
  }) => React.ReactElement | null
}) {
  const { control } = useFormContext<{ values: FieldValue[] }>()

  return (
    <Controller
      control={control}
      name="values"
      render={({ field: { value: values = [], onChange } }) => {
        const selectedValue = values.find(valueByFieldId(field.id))

        return (
          <ComponentElement
            field={field}
            value={selectedValue}
            onChange={(newValue) => {
              if (!newValue) {
                onChange(values.filter(not(valueByFieldId(field.id))))
                return
              }

              let updatedValues: FieldValue[] = []

              if (selectedValue) {
                updatedValues = updateValue(values, {
                  ...selectedValue,
                  ...newValue,
                })
              } else {
                updatedValues = [...values, newValue]
              }

              onChange(updatedValues)
            }}
          />
        )
      }}
    />
  )
}
