/*TODO:*/
/**/
/*differing bottom margin per depth?*/

.container {
  --space-y: 1.5rem;

  display: grid;
  grid-template-columns: minmax(0, 3fr) minmax(0, 2fr);
  column-gap: var(--space-8);
  padding-bottom: var(--space-y);
  margin-bottom: var(--space-y);

  &:not(:last-child) {
    border-bottom: 1px solid var(--color-grey-2);
  }

  textarea {
    width: 100%;
    min-height: 6em;
    padding: .25rem .5rem;
    border: var(--border);
    border-radius: var(--border-radius);

    &:focus {
      border-color: var(--color-ui-blue);
      outline: 0;
    }
  }
}

.title {
    display: block;
    font-size: 18px;
    font-weight: 500;
    line-height: 1.5;
    color: var(--color-primary);
    max-width: 70ch;
    margin: 0 0 1rem;
}

.inner {
  display: flex;
  flex-direction: column;
  justify-content: start;
}

.content {
  position: sticky;
  top: 1rem;
}

.component {
  width: 100%;
}

.message,
.guidance {
  composes: aside from "@styles/new/font.module.css";
}

.message {
  margin-top: var(--space-4);
}

.guidance {
  max-width: 640px;
  margin: -.5em 0;
  padding: 1em var(--space-4) var(--space-4) var(--space-3);
  background: var(--color-grey-1);
  border-radius: 5px;
  overflow-x: hidden;

  > :first-child:before {
    --icon-width: 14px;

    display: inline-block;
    content: url(/icons/InformationCircled.svg);
    width: var(--icon-width);
    height: var(--icon-width);
    margin-left: -.25em;
    margin-right: 0.25em;
    transform: translateY(0.13em);
  }
}
