import { TreeSelect } from 'antd'
import { useMemo } from 'react'
import { useAdminUsersListQuery } from '@store/services/sdk'

export interface UserSelectProps {
  value?: number[]
  onChange?: (selectedUserIds: number[]) => void
}

const VALUERS = -1
const OTHERS = -2

const UserSelect = ({ value = [], onChange }: UserSelectProps) => {
  const { data: users = [] } = useAdminUsersListQuery()
  const treeData = useMemo(() => {
    return [
      {
        title: 'Valuers',
        value: VALUERS,
        children: users
          .filter((user) => user.isValuer)
          .map((user) => ({
            title: user.username,
            key: user.id,
            value: user.id,
          })),
      },
      {
        title: 'Others',
        value: OTHERS,
        children: users
          .filter((user) => !user.isValuer)
          .map((user) => ({
            title: user.username,
            key: user.id,
            value: user.id,
          })),
      },
    ]
  }, [users])

  return (
    <TreeSelect
      value={value}
      treeData={treeData}
      showCheckedStrategy={TreeSelect.SHOW_PARENT}
      treeCheckable={true}
      placeholder={'Please select'}
      style={{ width: '100%' }}
      onChange={(value: number[]) => {
        const selectedArray: number[] = []
        for (const selectedValue of value) {
          const node = treeData.find((node) => node.value === selectedValue)
          if (node) {
            selectedArray.push(...node.children.map((child) => child.value))
          } else {
            selectedArray.push(selectedValue)
          }
        }
        onChange?.(selectedArray)
      }}
    />
  )
}

export default UserSelect
