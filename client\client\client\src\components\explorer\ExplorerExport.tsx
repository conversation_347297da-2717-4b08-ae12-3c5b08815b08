import { DownloadOutlined } from '@ant-design/icons'
import { pdf } from '@react-pdf/renderer'
import {
  Button,
  Drawer,
  Input,
  Radio,
  type RadioChangeEvent,
  Select,
} from 'antd'
import { saveAs } from 'file-saver'
import React, { type ReactElement, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from '@store'
import { actions, getExportState, getZoom } from '@store/features/explorer'
import {
  PRINT_PAGE_FORMATS,
  type PrintPageFormat,
  ZOOM_SCALE,
} from '@util/const'
import type { PageOrientation } from '@util/types'
import styles from './ExplorerExport.module.scss'
import ExplorerExportPdf from './ExplorerExportPdf'
import ExplorerViewPortal from './ExplorerViewPortal'
import { expolorerToJpeg } from './helpers'

const pageFormatOptions = (
  Object.keys(PRINT_PAGE_FORMATS) as PrintPageFormat[]
).map((value) => ({ value }))

const orientationOptions: { label: string; value: PageOrientation }[] = [
  { label: 'Landscape', value: 'landscape' },
  { label: 'Portrait', value: 'portrait' },
]

export const ExplorerExport = () => {
  const dispatch = useDispatch()
  const { active, zoom, ...layout } = useSelector((state) => ({
    ...getExportState(state),
    zoom: getZoom(state),
  }))

  const [loading, setLoading] = useState(false)
  const [title, setTitle] = useState('')

  async function createAndDownloadPdf() {
    setLoading(true)

    const src = await expolorerToJpeg(layout.pageFormat, layout.isLandscape)

    if (!src) return

    const doc = (
      <ExplorerExportPdf
        format={layout.pageFormat}
        orientation={layout.pageOrientation}
        scale={`1 : ${ZOOM_SCALE[
          Math.round(zoom) as keyof typeof ZOOM_SCALE
        ].toFixed(0)}`}
        src={src}
        title={title}
      />
    )

    // Casting to provide pdf with an empty value
    const asPdf = pdf([] as unknown as ReactElement)
    asPdf.updateContainer(doc)

    const blob = await asPdf.toBlob()
    saveAs(blob, 'map.pdf')

    setLoading(false)
  }

  function handleClose() {
    dispatch(actions.toggleShowExportPreview())
  }

  const setPageFormat = (value: PrintPageFormat) =>
    dispatch(actions.setExportPageFormat(value))

  const setPageOrientation = ({ target: { value } }: RadioChangeEvent) =>
    dispatch(actions.setExportPageOrientation(value as PageOrientation))

  return (
    <ExplorerViewPortal>
      <Drawer
        closable={true}
        getContainer={false}
        keyboard
        mask={false}
        onClose={handleClose}
        placement="right"
        size="large"
        title={<h2 className={styles.title}>Export to PDF</h2>}
        visible={active}
        width={500}
      >
        <div className={styles.content}>
          <div className={styles.input}>
            <label className={styles.label}>
              Document Title:
              <Input value={title} onChange={(e) => setTitle(e.target.value)} />
            </label>
            <div className={styles.formats}>
              <Select
                defaultValue={'A3'}
                options={pageFormatOptions}
                value={layout.pageFormat}
                onChange={setPageFormat}
              />
              <Radio.Group
                defaultValue="landscape"
                options={orientationOptions}
                buttonStyle="outline"
                optionType="button"
                value={layout.pageOrientation}
                onChange={setPageOrientation}
              />
            </div>
          </div>
          <Button
            icon={<DownloadOutlined />}
            loading={loading}
            type="primary"
            onClick={createAndDownloadPdf}
          >
            Download
          </Button>
        </div>
      </Drawer>
    </ExplorerViewPortal>
  )
}

export default ExplorerExport
