import React from 'react'
import { ExportPage, ExportSection } from '@components/export/components'
import {
  RVRConclusionSection,
  RVRSummaryTable,
} from '@components/rvr/RVRSummaryTable'
import { useGetRVRFormQuery } from '@store/services/rvr'

interface ExportRVRSummaryPageProps {
  rvrId: string
}

const BREAK_AT_INDEX = 5

const ExportRVRSummaryPage = ({ rvrId }: ExportRVRSummaryPageProps) => {
  const { data: rvrSummary } = useGetRVRFormQuery(rvrId)

  if (!rvrSummary) {
    return null
  }

  return (
    <>
      <ExportPage variant="form">
        <ExportSection title="RVR Summary">
          <RVRSummaryTable rvrSummary={rvrSummary} endIndex={BREAK_AT_INDEX} />
        </ExportSection>
      </ExportPage>
      <ExportPage variant="form">
        <ExportSection title="RVR Summary (continued)">
          <RVRSummaryTable
            rvrSummary={rvrSummary}
            startIndex={BREAK_AT_INDEX}
          />
        </ExportSection>
        <ExportSection>
          <RVRConclusionSection rvrSummary={rvrSummary} />
        </ExportSection>
      </ExportPage>
    </>
  )
}

export default ExportRVRSummaryPage
