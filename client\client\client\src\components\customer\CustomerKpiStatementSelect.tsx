import { useCustomerKpiListQuery } from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import { useCustomer } from './context'
import { useMemo } from 'react'
import { formatDate } from '@util'
import { Select, type SelectProps } from 'antd'
import { keyBy } from 'lodash'
import useSelectedKpiStatements from './useSelectedKpiStatements'
import { sortDateStringDescending } from '@util/sort'

const CustomerKpiStatementSelect = (props: SelectProps) => {
  const { pk } = useCustomer()
  const [selectedStatements, setSelectedStatements] = useSelectedKpiStatements()

  const { data = [] } = useCustomerKpiListQuery(
    skipArgObject({ customerPk: pk })
  )

  const validStatements = useMemo(() => {
    /*
    KPI data is returned as an array of KPI values with a statementId, periodTo, and auditMethod - where periodTo and auditMethod are properties of the statement a KPI belongs to.
    Because the data model doesnt include a statement entity (it should), we instead group by the statement id to get the unique options for the select dropdown.

    This should be refactored when we revisit the data model - hopefully when fixing up what statements to use.
    */
    return Object.values(keyBy(data, 'statementId'))
      .sort(({ periodTo: periodToFirst }, { periodTo: periodToSecond }) =>
        sortDateStringDescending(periodToFirst, periodToSecond)
      )
      .map(({ statementId, periodTo, auditMethod }) => ({
        value: statementId,
        label: auditMethod
          ? `${formatDate(periodTo)} - ${auditMethod}`
          : `${formatDate(periodTo)}`,
      }))
  }, [data])

  return (
    <Select
      style={{ minWidth: 220 }}
      allowClear
      mode="multiple"
      placeholder="Select individual statements..."
      maxTagCount={1}
      options={validStatements}
      value={selectedStatements}
      onChange={setSelectedStatements}
      {...props}
    />
  )
}

export default CustomerKpiStatementSelect
