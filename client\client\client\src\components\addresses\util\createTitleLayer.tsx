import type { Feature, Geometry, MultiPolygon } from 'geojson'
import React from 'react'
import { GeoJSON, Tooltip } from 'react-leaflet'
import type { TitleProperties } from '../../../models/title/TitleFeatureCollection'
import { TITLE_COLOUR } from '../../../variables/definitions'

export function TitleLayer(title: Feature<Geometry | null, TitleProperties>) {
  if (
    title.geometry === null ||
    title.geometry === undefined ||
    (title.geometry as MultiPolygon)?.coordinates?.length === 0
  ) {
    return null
  }

  return (
    <GeoJSON
      key={title.id}
      data={title}
      style={{
        color: TITLE_COLOUR,
        opacity: 1,
        weight: 3,
        fillOpacity: 0,
      }}
    >
      <Tooltip
        direction={!title.properties.titleNo ? 'bottom' : 'center'}
        offset={[0, 0]}
        opacity={1}
        permanent
      >
        <div className="label-container">
          <div className="title-number">{title.properties.titleNo}</div>
          <div className="survey-area">
            {(title.properties.surveyArea / 1e4).toFixed(1)}
            &nbsp;ha
          </div>
        </div>
      </Tooltip>
    </GeoJSON>
  )
}
