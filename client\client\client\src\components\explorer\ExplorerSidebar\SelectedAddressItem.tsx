import { CloseOutlined, MoreOutlined } from '@ant-design/icons'
import { Button, type ButtonProps, Dropdown, List, Skeleton } from 'antd'
import classNames from 'classnames'
import type React from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useLeaflet } from 'react-leaflet'
import { useDispatch } from 'react-redux'
import ExplorerSelectedAddressMenu from '@components/explorer/ExplorerSelectedAddressMenu'
import { useAddressesRetrieveQuery } from '@store/services/sdk'
import { toggleSelectedAddressId } from '@store/ui/actions'
import { isLatLngTuple } from '@util/guards'
import { skipArgObject } from '@util/helpers'
import { formatAddress } from '@util/labels'
import ExplorerLayerStyleSelectionButton from '../ExplorerLayerStyleSelectionButton'
import styles from './SelectedAddressItem.module.scss'

interface SelectedAddressItemProps extends React.HTMLAttributes<HTMLElement> {
  addressId: number
  index: number
  isNew: boolean
  setSelected: (id: string) => void
}

const ItemButton = ({
  children,
  onClick,
  ...props
}: Omit<ButtonProps, 'size'>) => {
  const clickHandler: ButtonProps['onClick'] = (e) => {
    e.stopPropagation()
    if (onClick) onClick(e)
  }

  return (
    <Button type="text" {...props} size="small" onClick={clickHandler}>
      {children}
    </Button>
  )
}

export const SelectedAddressItem = ({
  addressId,
  index,
  isNew,
  setSelected,
  ...props
}: SelectedAddressItemProps) => {
  // Should do a list fetch instead of on the individual components
  const dispatch = useDispatch()
  const { map } = useLeaflet()
  const { data } = useAddressesRetrieveQuery(skipArgObject({ pk: addressId }))

  const [centerOn, setCenterOn] = useState(false)

  const coordinates = useMemo(
    () => (data ? [data.lat, data.lng] : undefined),
    [data]
  )

  function handleZoom() {
    if (!isLatLngTuple(coordinates)) return
    const zoom = map?.getZoom() ?? 15.5
    map?.setView(coordinates, zoom)
  }

  function handleRemove() {
    dispatch(toggleSelectedAddressId(addressId.toString()))
  }

  useEffect(() => {
    // Receiving and setting these variables on new entries to center map on them when they have receieved their coordinate data — needs improvement! Should probably just fetch all the addresses in the parent
    if (isNew) {
      setCenterOn(true)
    }
  }, [isNew])

  useEffect(() => {
    if (!(isLatLngTuple(coordinates) && map && centerOn)) return
    map.setView(coordinates, map.getZoom())
    setCenterOn(false)
  }, [centerOn, coordinates, map])

  return (
    <List.Item
      {...props}
      className={classNames('SelectedAddressItem', styles.container)}
      onClick={() => {
        setSelected(addressId.toString())
        handleZoom()
      }}
    >
      <>
        {data ? (
          <div className={styles.content}>
            <ExplorerLayerStyleSelectionButton
              context="address"
              id={addressId}
              index={index}
            />
            <span className={styles.label}>
              {formatAddress(data.fullAddress, { length: 45 })}
            </span>
            <Dropdown
              overlay={<ExplorerSelectedAddressMenu address={data} />}
              placement="bottomLeft"
              trigger={['click']}
            >
              <ItemButton icon={<MoreOutlined />} />
            </Dropdown>
          </div>
        ) : (
          <Skeleton.Input size="small" className={styles.skeleton} />
        )}
        <ItemButton icon={<CloseOutlined />} onClick={handleRemove} />
      </>
    </List.Item>
  )
}
