import { DatePicker } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import { useCallback } from 'react'
import { type DateInputValue, fromString, toISOString } from './helpers'

type RangeValue = RangePickerProps['value']

const { RangePicker } = DatePicker

const IsoStringDateRangePicker = ({
  value,
  onChange,
  ...props
}: RangePickerProps & {
  onChange?: (date: [DateInputValue, DateInputValue] | undefined | null) => void
}) => {
  const pickerValue =
    value === undefined ? undefined : (value?.map(fromString) as RangeValue)

  const handleChange = useCallback(
    (range: RangeValue) => {
      onChange?.(range?.map(toISOString) as RangeValue)
    },
    [onChange]
  )

  return <RangePicker value={pickerValue} onChange={handleChange} {...props} />
}

export default IsoStringDateRangePicker
