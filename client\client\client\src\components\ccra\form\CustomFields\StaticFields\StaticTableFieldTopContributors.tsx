import { QuestionCircleOutlined } from '@ant-design/icons'
import { Form, Table, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import { getFieldName } from '../../helpers'
import type { RankedContributorItem } from '../../types'

const StaticTableFieldTopContributors = ({
  providedValues,
}: { providedValues: RankedContributorItem[] }) => {
  const form = Form.useFormInstance()
  const contributorsValue = Form.useWatch(getFieldName('topContributors'), form)

  const [data, setData] = useState<RankedContributorItem[]>([])

  useEffect(() => {
    if (contributorsValue) {
      setData(contributorsValue)
    }
  }, [contributorsValue])

  useEffect(() => {
    if (providedValues) {
      setData(providedValues)
    }
  }, [providedValues])

  const columns = [
    {
      title: 'Rank',
      dataIndex: 'index',
      key: 'index',
      width: 100,
    },
    {
      title: 'Contributors',
      dataIndex: 'contributor',
      key: 'text',
      render: (text: string, _record: RankedContributorItem) => (
        <span>{text}</span>
      ),
    },
  ]

  return (
    <Table
      dataSource={data}
      columns={columns}
      rowKey="index"
      pagination={false}
      size="small"
    />
  )
}

export default StaticTableFieldTopContributors
