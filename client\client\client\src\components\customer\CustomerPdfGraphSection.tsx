import { Text, View } from '@react-pdf/renderer'
import type React from 'react'
import type { ReactElement } from 'react-markdown/lib/react-markdown'
import { PdfChartImage, PdfSection, PdfText, stylesheet } from '@components/pdf'
import { SPACE } from '@components/pdf/stylesheet'

type Props = {
  children: ReactElement
  title?: string
  description?: string
  extra?: React.ReactNode
}

const CustomerPdfGraphSection = ({
  children,
  title,
  description,
  extra,
  ...props
}: Props) => {
  return (
    <PdfSection {...props}>
      {!!title && (
        <Text
          style={{ ...stylesheet.headingL, paddingTop: SPACE[6] }}
          minPresenceAhead={300}
        >
          {title}
        </Text>
      )}
      <View
        wrap={false}
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: SPACE[6],
          marginBottom: SPACE[6],
        }}
      >
        {!!description && (
          <View style={{ flex: 1 }}>
            <PdfText>{description}</PdfText>
          </View>
        )}
        <View style={{ flex: 1 }}>
          <PdfChartImage width={description ? 230 : 460}>
            {children}
          </PdfChartImage>
        </View>
      </View>
    </PdfSection>
  )
}

export default CustomerPdfGraphSection
