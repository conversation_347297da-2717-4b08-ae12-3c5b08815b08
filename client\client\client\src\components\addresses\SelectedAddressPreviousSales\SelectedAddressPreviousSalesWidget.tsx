import { skipToken } from '@reduxjs/toolkit/query/react'
import { Table } from 'antd'
import type React from 'react'
import { Link } from 'react-router-dom'
import { Widget } from '@components/generic'
import { useGetAddressSalesQuery } from '@store/services/address'

interface SelectedAddressPreviousSalesWidgetProps
  extends React.HTMLAttributes<HTMLDivElement> {
  addressId?: string
}

const SelectedAddressPreviousSalesWidget = ({
  addressId,
}: SelectedAddressPreviousSalesWidgetProps) => {
  const { data: sales, isFetching } = useGetAddressSalesQuery(
    addressId ? { addressId } : skipToken
  )

  return (
    <Widget title="Historical Sales" isFetching={isFetching}>
      <Table
        className="agrigis-table"
        dataSource={sales?.results}
        rowKey={(sale) => sale.id}
        size="small"
        columns={[
          {
            dataIndex: 'source',
            title: 'source',
            render: (text: string, record) => {
              if (text === 'ESGIS') {
                return <Link to={`/sale/${record.id}/anz/`}>{text}</Link>
              }
              return text
            },
          },
          {
            dataIndex: 'saleDate',
            title: 'saleDate',
            render: (_, record) => {
              return (record?.saleDate || '').slice(0, 10)
            },
          },
          {
            dataIndex: 'grossSalesPrice',
            title: 'FMV',
            render: (text: string, record) => {
              return record.grossSalesPrice
                ? (record.grossSalesPrice * 1e3)?.toLocaleString('en-NZ')
                : text
            },
          },
          { dataIndex: 'vendor', title: 'Vendor' },
          { dataIndex: 'purchaser', title: 'Purchaser' },
        ]}
      />
    </Widget>
  )
}

export default SelectedAddressPreviousSalesWidget
