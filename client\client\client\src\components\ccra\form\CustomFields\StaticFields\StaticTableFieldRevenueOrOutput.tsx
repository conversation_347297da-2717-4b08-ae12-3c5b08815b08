import { Form, Table } from 'antd'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { getFieldName, integerOrTwoDecimals } from '../../helpers'
import { EMISSIONS_DATA_OUTPUT_UNITS } from '../../const'
import { formatNumber } from '@util/labels'

interface DataType {
  key: React.Key
  yearEnd: string | null
  quantity: number | null
  unit: string
}
const StaticTableFieldRevenueOrOutput = ({
  providedValues,
}: { providedValues: DataType[] }) => {
  const form = Form.useFormInstance()
  const revenueOrOutputValue = Form.useWatch(
    getFieldName('revenueOrOutput'),
    form
  )

  const [data, setData] = useState<DataType[]>([])

  useEffect(() => {
    if (revenueOrOutputValue) {
      setData(revenueOrOutputValue)
    }
  }, [revenueOrOutputValue])

  useEffect(() => {
    if (providedValues) {
      setData(providedValues)
    }
  }, [providedValues])

  const columns = [
    {
      title: 'Year End',
      dataIndex: 'yearEnd',
      width: 100,
      render: (yearEnd: string | null) =>
        yearEnd ? moment(yearEnd, 'YYYY').format('YYYY') : '',
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      render: (quantity: number | null) => {
        return quantity === null ? null : integerOrTwoDecimals(quantity)
      },
    },
    {
      title: 'Unit',
      dataIndex: 'unit',
      render: (unit: string) => {
        return EMISSIONS_DATA_OUTPUT_UNITS.find((item) => item.value === unit)
          ?.label
      },
    },
  ]

  return (
    <Table
      dataSource={data}
      columns={columns}
      rowClassName="static-row"
      pagination={false}
      size="small"
      style={{ marginBottom: 16 }}
    />
  )
}

export default StaticTableFieldRevenueOrOutput
