import logo from '@/img/customer-portal-logo.svg'
import {
  DeleteFilled,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons'
import { AutoComplete, Button, Card, Col, Empty, Row, Spin, Tag } from 'antd'
import Sider from 'antd/lib/layout/Sider'
import classNames from 'classnames'
import type React from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useLocalStorage } from 'react-use'
import { useCustomerListQuery } from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import { useDebounce } from '@util/useDebounce'
import styles from './CustomerSider.module.scss'

interface CustomerLabel {
  value: number | null | undefined
  label: string | null | undefined
}

export const CustomerSearch = () => {
  const [match, setMatch] = useState<string | undefined>()
  const debounced = useDebounce(match, 500)

  const { data, isFetching, isLoading } = useCustomerListQuery(
    skipArgObject({ match: debounced })
  )

  const options = useMemo(() => {
    return (
      data?.results?.map((option) => {
        return { value: option.customerId, label: option.entityName }
      }) ?? []
    )
  }, [data])

  const navigate = useNavigate()

  const [customerSearchHistory, setCustomerSearchHistory] = useLocalStorage(
    'customerPortalSearchHistory',
    '[]'
  )

  const [customerSearchHistoryArray, setCustomerSearchHistoryArray] = useState<
    CustomerLabel[]
  >([])

  useEffect(() => {
    const array: CustomerLabel[] = JSON.parse(
      customerSearchHistory ? customerSearchHistory : '[]'
    )
    setCustomerSearchHistoryArray(array)
  }, [customerSearchHistory])

  const handleSearchHistory = (customer: CustomerLabel) => {
    const searchHistory = [...customerSearchHistoryArray]

    const customerIndex = searchHistory.findIndex(
      (searchHistory) => searchHistory.value === customer.value
    )
    if (customerIndex >= 0) {
      searchHistory.splice(customerIndex, 1)
    }

    searchHistory.unshift(customer)
    const newSearchHistory = searchHistory.slice(0, 10)
    setCustomerSearchHistory(JSON.stringify(newSearchHistory))
  }

  const handleCloseTag = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    const target = e.target as HTMLInputElement
    const tag = target.closest('.ant-tag')
    const name = tag?.children[1].textContent

    const searchHistory = [...customerSearchHistoryArray]
    const newSearchHistory = searchHistory.filter(
      (customer) => customer.label !== name
    )
    setCustomerSearchHistory(JSON.stringify(newSearchHistory))
  }

  const navigateToCustomer = (to: string) => {
    navigate(`/customer/${to}/`)
  }

  return (
    <>
      <Row style={{ margin: '0 0.5em' }}>
        <Col span={22}>
          <AutoComplete
            onSearch={setMatch}
            allowClear={true}
            onSelect={(value: string, option: CustomerLabel) => {
              navigateToCustomer(value)
              setMatch(undefined)
              handleSearchHistory(option)
            }}
            value={match}
            style={{ width: '100%' }}
            options={isLoading || isFetching || !match ? [] : options}
            placeholder="Search for an ANZ customer by name or number"
            notFoundContent={
              match && match?.length > 0 && !isFetching ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="No customers found"
                />
              ) : null
            }
          />
        </Col>
        <Col span={1} offset={1}>
          {isFetching || isLoading ? <Spin size="small" /> : <SearchOutlined />}
        </Col>
      </Row>
      <Row>
        {customerSearchHistoryArray.length > 0 && (
          <Card
            bordered={false}
            size={'small'}
            title="Recent Searches"
            style={{ width: '100%' }}
            extra={
              <Button
                icon={<DeleteFilled style={{ color: 'var(--primary)' }} />}
                type={'text'}
                style={{ width: 'fit-content' }}
                onClick={() => setCustomerSearchHistory('[]')}
              />
            }
          >
            <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              {customerSearchHistoryArray.map((customer) => (
                <div
                  key={customer.value}
                  style={{
                    marginBottom: '5px',
                  }}
                >
                  <Tag
                    icon={<UserOutlined />}
                    style={{
                      cursor: 'pointer',
                      display: 'inline-flex',
                      alignItems: 'center',
                    }}
                    onClick={() =>
                      customer.value
                        ? navigateToCustomer(customer.value.toString())
                        : null
                    }
                    closable
                    onClose={(e) => {
                      e.persist()
                      e.stopPropagation()
                      handleCloseTag(e)
                    }}
                  >
                    {customer.label}
                  </Tag>
                </div>
              ))}
            </div>
          </Card>
        )}
      </Row>
    </>
  )
}

export const CustomerSider = ({
  children,
}: React.HTMLAttributes<HTMLDivElement>) => {
  const [collapsed, setCollapsed] = useState(false)

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      collapsedWidth={60}
      trigger={null}
      theme="light"
      className={classNames(styles.CustomerSider, {
        [styles.collapsed]: collapsed,
      })}
      width="450px"
    >
      <div className={classNames(styles.header)}>
        <div className={styles.headerText}>
          <img alt="Customer Logo" src={logo} width={175} />
        </div>
        <Button
          size="small"
          onClick={() => setCollapsed(!collapsed)}
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          type="primary"
        />
      </div>
      <div>
        <CustomerSearch />
      </div>
      {children}
    </Sider>
  )
}
