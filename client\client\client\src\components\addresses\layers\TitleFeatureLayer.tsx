import React from 'react'
import { FeatureGroup } from 'react-leaflet'
import type { TitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { TitleLayer } from '../util/createTitleLayer'

export const TitleFeatureLayer = (props: {
  titles: TitleFeatureCollection | undefined
}) => {
  const { titles } = props

  if (!titles) {
    return <></>
  }

  return (
    <FeatureGroup>
      {titles.features ? titles.features?.map(TitleLayer) : null}
    </FeatureGroup>
  )
}
