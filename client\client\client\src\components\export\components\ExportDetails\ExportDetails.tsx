import { Skeleton } from 'antd'
import React, { type ReactNode } from 'react'

interface ExportDetailsProps {
  children: ReactNode | ReactNode[]
  flex?: 25 | 50 | 100
  type?: 'information' | 'financials' | 'memorials'
  isFetching?: boolean
  title?: string
}

const ExportDetails = (props: ExportDetailsProps) => {
  if (props.isFetching) {
    return <Skeleton />
  }

  return (
    <div
      data-type={props?.type ? props?.type : 'information'}
      className={`ExportDetails ${props?.flex ? `flex-${props?.flex}` : ''}`}
    >
      {props?.title ? (
        <div className="export-details-title">{props?.title}</div>
      ) : null}
      <div className="export-details-content">{props.children}</div>
    </div>
  )
}

export default ExportDetails
