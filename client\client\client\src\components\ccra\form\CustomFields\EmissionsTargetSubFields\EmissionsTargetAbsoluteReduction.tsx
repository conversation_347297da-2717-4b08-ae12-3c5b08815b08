import { Form, InputNumber, type InputRef, Select } from 'antd'
import { type Dispatch, type SetStateAction, useRef, useState } from 'react'
import { SelectWithOptionInput } from '@components/form/SelectWithOptionInput'

const EmissionsTargetAbsoluteReduction = ({
  index,
  targetUnitOptions,
  setTargetUnitOptions,
}: {
  index: number
  targetUnitOptions: { value: string; label: string }[]
  setTargetUnitOptions: Dispatch<
    SetStateAction<{ value: string; label: string }[]>
  >
}) => {
  const form = Form.useFormInstance()
  const emissionsTargetUnits = Form.useWatch(
    ['emissionsTargets', index, 'emissionsTargetAbsoluteUnits'],
    form
  )

  return (
    <>
      <Form.Item
        name={['emissionsTargets', index, 'emissionsTargetAbsoluteFrom']}
      >
        <InputNumber
          min={1}
          style={{ width: 260, marginRight: '6px' }}
          controls={false}
          placeholder="Enter value"
          addonAfter={
            <Form.Item
              name={['emissionsTargets', index, 'emissionsTargetAbsoluteUnits']}
              style={{ marginBottom: -2 }}
            >
              <SelectWithOptionInput
                options={targetUnitOptions}
                setOptions={setTargetUnitOptions}
              />
            </Form.Item>
          }
        />
      </Form.Item>
      <p>to</p>
      <Form.Item
        name={['emissionsTargets', index, 'emissionsTargetAbsoluteTo']}
      >
        <InputNumber
          min={1}
          style={{ width: 200 }}
          controls={false}
          placeholder="Enter value"
          addonAfter={emissionsTargetUnits}
        />
      </Form.Item>
    </>
  )
}

export default EmissionsTargetAbsoluteReduction
