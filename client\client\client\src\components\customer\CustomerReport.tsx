import { <PERSON><PERSON>, Col, Row } from 'antd'
import { memo } from 'react'
import { useSelector } from '@store'
import {
  getCustomerReportState,
  getCustomerReportStateById,
} from '@store/features/customer'
import {
  useCustomerKpiListQuery,
  useCustomerRetrieveQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'
import { useDebounce } from '@util/useDebounce'
import CustomerPdf from './CustomerPdf'
import styles from './CustomerReport.module.scss'
import CustomerReportSettings from './CustomerReportSettings'
import { useCustomer } from './context'
import {
  CUSTOMER_REPORT_ENABLED,
  CUSTOMER_REPORT_DISABLED_MESSAGE,
} from './static'

type SectionProps = { title: string } & React.HTMLAttributes<HTMLDivElement>

export const Section = ({ title, children, ...props }: SectionProps) => {
  return (
    <div {...props} className={styles.Section}>
      <div className={styles.title}>{title}</div>
      <div>{children}</div>
    </div>
  )
}

const CustomerReport = () => {
  const { pk } = useCustomer()

  const { data: customer, isLoading: customerLoading } =
    useCustomerRetrieveQuery(skipArgObject({ pk }))

  const { data: kpis, isLoading: kpisLoading } = useCustomerKpiListQuery(
    skipArgObject({ customerPk: pk })
  )

  const {
    selectedComponents,
    selectedMeasures,
    denominator,
    reportTitle,
    customerName,
  } = useSelector((state) => getCustomerReportState(state, pk))

  const debouncedCustomerName = useDebounce(customerName, 500)
  const debouncedReportTitle = useDebounce(reportTitle, 500)

  const report = useSelector((state) => getCustomerReportStateById(state, pk))

  if (!CUSTOMER_REPORT_ENABLED)
    return (
      <Alert
        style={{ margin: '5px' }}
        type="warning"
        message={CUSTOMER_REPORT_DISABLED_MESSAGE}
      />
    )

  return (
    <Row gutter={[16, 16]} className={styles.CustomerReport}>
      <Col span={12} className={styles.settings}>
        <CustomerReportSettings />
      </Col>
      <Col span={12}>
        {customer?.customerId &&
        customer?.entityName &&
        (customer?.segment === 'R' || customer?.segment === 'W') ? (
          <CustomerPdf
            loading={kpisLoading || customerLoading}
            customerId={customer?.customerId}
            kpis={kpis}
            selectedComponents={selectedComponents}
            selectedMeasures={selectedMeasures}
            segment={customer?.segment}
            denominator={denominator}
            customerName={debouncedCustomerName ?? customer.entityName}
            reportTitle={debouncedReportTitle}
            report={report}
          />
        ) : (
          <Alert
            type="warning"
            description={
              'Customer reports are not yet supported for this customer segment.'
            }
          />
        )}
      </Col>
    </Row>
  )
}

export default memo(CustomerReport)
