import {
  DatePicker,
  type DatePickerProps,
  Form,
  type FormItemProps,
  Input,
} from 'antd'
import type { NamePath } from 'antd/lib/form/interface'
import moment, { type Moment } from 'moment'
import React, { useCallback, useEffect, useState } from 'react'

type Props = { path: NamePath; outputFormat?: string } & DatePickerProps

const DatePickerShadow = ({ path, outputFormat, ...props }: Props) => {
  const form = Form.useFormInstance()
  const dateString: string | undefined = Form.useWatch(path, form)

  const [value, setValue] = useState<Moment>()

  useEffect(() => {
    setValue(dateString ? moment(dateString) : undefined)
  }, [dateString])

  const handleChange = useCallback(
    (date: Moment | null) => {
      if (moment.isMoment(date)) {
        if (outputFormat) {
          form.setFieldValue(path, date.format(outputFormat))
        } else {
          form.setFieldValue(path, date.utcOffset(0).toISOString())
        }
      } else {
        form.setFieldValue(path, null)
      }

      form.setFields([{ name: path, touched: true }])
    },
    [form, outputFormat, path]
  )

  return <DatePicker {...props} onChange={handleChange} value={value} />
}

export const DatePickerShadowItem = ({
  children,
  name,
  inputProps = {},
  ...props
}: FormItemProps &
  Required<Pick<FormItemProps, 'name'>> & {
    inputProps?: Omit<Props, 'path'>
  }) => {
  return (
    <>
      <Form.Item
        shouldUpdate
        {...props}
        style={{ marginBottom: 0, ...props.style }}
      >
        <DatePickerShadow {...inputProps} path={name} />
      </Form.Item>
      <Form.Item shouldUpdate name={name} hidden>
        {children || <Input />}
      </Form.Item>
    </>
  )
}

export default DatePickerShadow
