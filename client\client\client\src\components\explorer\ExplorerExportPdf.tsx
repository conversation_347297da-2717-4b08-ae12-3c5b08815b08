import { Document, Image, Page, Text, View } from '@react-pdf/renderer'
import React from 'react'
import { stylesheet } from '@components/pdf'
import type { PrintPageFormat } from '@util/const'
import type { PageOrientation } from '@util/types'

type Props = {
  format: PrintPageFormat
  orientation: PageOrientation
  scale: string
  src: string
  title: string
}

const ExplorerExportPdf = ({
  src,
  format,
  orientation,
  scale,
  title = '',
}: Props) => {
  return (
    <Document>
      <Page size={format} orientation={orientation} wrap={false}>
        <View
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            padding: 20,
          }}
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'flex-end',
              marginBottom: stylesheet.body.paddingBottom * 0.75,
            }}
          >
            <Text
              style={{
                ...stylesheet.headingL,
                marginTop: 0,
                marginBottom: 0,
                lineHeight: 1,
              }}
            >
              {title}
            </Text>
            <Text
              style={{
                ...stylesheet.body,
                marginTop: 0,
                marginBottom: 0,
                lineHeight: 1,
              }}
            >
              {scale}
            </Text>
          </View>
          <View
            style={{
              flex: 1,
              display: 'flex',
            }}
          >
            <Image
              src={src}
              style={{
                objectFit: 'cover',
              }}
            />
          </View>
        </View>
      </Page>
    </Document>
  )
}

export default ExplorerExportPdf
