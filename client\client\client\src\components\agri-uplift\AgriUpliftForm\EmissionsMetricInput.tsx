import { InputNumber } from 'antd'
import type {
  CustomerEmissionsMetric,
  CustomerEmissionsMetricType,
} from '@store/services/finance/codegen'
import { isValidTo } from './helpers'

function EmissionsMetricInput({
  metricType,
  metricTypeId,
  reportTypeId,
  value,
  onChange,
}: {
  metricTypeId: number
  reportTypeId: number
  metricType: CustomerEmissionsMetricType
  value?: CustomerEmissionsMetric
  onChange?: (value: CustomerEmissionsMetric) => void
}) {
  const inputValue = isValidTo({ metricTypeId, reportTypeId })(value)
    ? value
    : null
  return (
    <InputNumber
      type="number"
      precision={1}
      step={0.1}
      addonAfter={metricType.unit}
      value={inputValue?.value}
      onChange={(newValue) => {
        onChange?.({
          ...(value as CustomerEmissionsMetric),
          reportTypeId,
          metricTypeId,
          value: newValue ?? '',
        })
      }}
    />
  )
}

export default EmissionsMetricInput
