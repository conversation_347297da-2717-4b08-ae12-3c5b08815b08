import type React from 'react'

interface ExportFormInputProps extends React.HTMLAttributes<HTMLDivElement> {
  label: string
  hint?: string
  options?: { label: string; value: string }[]
  value?: string | number
  multiValue?: Array<string>
  multiForm?: number
}

const ExportFormInput = (props: ExportFormInputProps) => {
  const { label, hint, value, options, multiForm, multiValue, ...divProps } =
    props

  return (
    <div className="ExportFormInput" {...divProps}>
      {label ? <div className="input-label">{label}</div> : null}
      {options ? (
        <div className="input-group" data-even={options?.length % 2 === 0}>
          {options?.map((option) => (
            <div
              key={`export-form-input-option-${option.label}`}
              className="input-checkbox-group"
            >
              <div
                key={option.label}
                className="input-container"
                data-type="checkbox"
                data-value={option.label}
              >
                {value !== undefined && value === option.value ? '✓' : ''}
              </div>
              <div className="input-checkbox-text">{option.label}</div>
            </div>
          ))}
        </div>
      ) : !multiValue ? (
        <div className="input-container">
          {value !== undefined
            ? typeof value === 'string'
              ? value
              : value?.toLocaleString('en-NZ', {
                  maximumFractionDigits: 2,
                })
            : ''}
        </div>
      ) : (
        <div className="input-group">
          {multiValue?.map((x) => (
            <div key={x} className="input-container">
              {x !== undefined ? x : ''}
            </div>
          ))}
          {[...new Array((multiForm ?? 0) - multiValue.length).keys()].map(
            (i) => {
              return <div key={i} className="input-container" />
            }
          )}
        </div>
      )}
      {hint ? <div className="input-hint">{hint}</div> : null}
    </div>
  )
}

export default ExportFormInput
