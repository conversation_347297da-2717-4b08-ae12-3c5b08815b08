import type { ReactNode } from 'react'
import { useNavigate } from 'react-router-dom'
import CustomerHeading from '../CustomerHeading'
import CustomerSearch from '../CustomerSearch'
import styles from './HeaderBar.module.css'

type Props = {
  customerId: string | undefined
  actions?: ReactNode
}

const HeaderBar = ({ customerId, actions, ...props }: Props) => {
  const navigate = useNavigate()

  return (
    <div className={styles.container} {...props}>
      <div>
        {customerId != null && (
          <CustomerHeading customerId={Number(customerId)} />
        )}
      </div>
      <div className={styles.actions}>
        <CustomerSearch
          onSelect={(_, option) => {
            navigate(`/customer/${option.customerId}/`)
          }}
        />
        {actions}
      </div>
    </div>
  )
}

export default HeaderBar
