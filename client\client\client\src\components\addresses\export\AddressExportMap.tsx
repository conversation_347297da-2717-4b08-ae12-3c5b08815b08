import type { LatLngBounds, LeafletMouseEvent } from 'leaflet'
import React, { type ReactNode, useEffect, useState } from 'react'
import { FeatureGroup, Map as LeafletMap, Pane, TileLayer } from 'react-leaflet'

const DEFAULT_ZOOM = 13

export interface AddressExportMapProps {
  center: { lat: number; lng: number }
  height?: string
  children?: ReactNode
  overlays?: ReactNode
  onClick?: (event: LeafletMouseEvent) => void
}

/**
 * @deprecated Use PdfMap
 */
export const AddressExportMap = (props: AddressExportMapProps) => {
  const [bounds, setBounds] = useState<LatLngBounds | undefined>()
  // biome-ignore lint: Will be removed
  const [layerRef, setLayerRef] = useState<any>()

  // biome-ignore lint: Will be removed
  useEffect(() => {
    let newBounds: LatLngBounds | undefined =
      layerRef?.leafletElement?.getBounds()
    if (!newBounds?.isValid()) {
      newBounds = undefined
    }

    setBounds(newBounds)
  }, [layerRef, props.children])

  return (
    <LeafletMap
      center={props.center}
      zoom={DEFAULT_ZOOM}
      bounds={bounds}
      style={{ height: props?.height || '100%' }}
      zoomControl={false}
    >
      <TileLayer
        attribution="Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community"
        url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
      />
      <Pane name="yellow-rectangle" style={{ zIndex: 1000 }}>
        <TileLayer
          attribution="Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community"
          url="https://services.arcgisonline.com/ArcGIS/rest/services/Reference/World_Transportation/MapServer/tile/{z}/{y}/{x}"
        />
      </Pane>
      <FeatureGroup ref={setLayerRef}>{props.children}</FeatureGroup>
    </LeafletMap>
  )
}
