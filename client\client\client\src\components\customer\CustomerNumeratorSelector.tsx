import { Cascader } from 'antd'
import { useParams } from 'react-router-dom'
import { useAppDispatch, useSelector } from '@store'
import { actions, getCustomerBenchmarkingState } from '@store/features/customer'
import {
  useCustomerRetrieveQuery,
  useValidKpiMeasuresCascaderOptionsQuery,
} from '@store/services/sdk'
import { skipArgObject } from '@util/helpers'

const CustomerNumeratorSelector = () => {
  const dispatch = useAppDispatch()

  const { customerId } = useParams<{ customerId: string }>()

  const { data: customer, isLoading: customerLoading } =
    useCustomerRetrieveQuery(skipArgObject({ pk: Number(customerId) }))

  const { data: cascaderOptions, isLoading: cascaderLoading } =
    useValidKpiMeasuresCascaderOptionsQuery(
      skipArgObject({ segment: customer?.segment })
    )

  const { numerator } = useSelector((state) =>
    getCustomerBenchmarkingState(state, Number(customerId))
  )
  return (
    <Cascader
      options={cascaderOptions}
      loading={cascaderLoading || customerLoading}
      value={numerator}
      showSearch
      style={{ width: '100%', minWidth: '250px' }}
      onChange={(e) =>
        dispatch(
          actions.setBenchmarkNumerator({
            numerator: e as [number, string],
            customerId: Number(customerId),
          })
        )
      }
    />
  )
}

export default CustomerNumeratorSelector
