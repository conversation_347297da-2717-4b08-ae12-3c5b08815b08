import { Form } from 'antd'
import type { NamePath } from 'rc-field-form/es/interface'
import { useEffect, useMemo, useRef } from 'react'

function useFormDependencyChange<T>(
  path: NamePath,
  callback: (value?: T) => void
) {
  const form = Form.useFormInstance()

  const lastDependencyValue = useRef<T>()
  const dependencyValue: T = Form.useWatch(path, form)

  useEffect(() => {
    if (
      lastDependencyValue.current &&
      lastDependencyValue.current !== dependencyValue
    ) {
      callback(dependencyValue)
    }
    lastDependencyValue.current = dependencyValue
  }, [callback, dependencyValue])

  return useMemo(() => dependencyValue, [dependencyValue])
}

export default useFormDependencyChange
