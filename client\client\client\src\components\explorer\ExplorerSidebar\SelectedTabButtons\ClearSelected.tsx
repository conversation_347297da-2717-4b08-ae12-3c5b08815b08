import { ClearOutlined } from '@ant-design/icons'
import { type ButtonProps, Tooltip } from 'antd'
import { useCallback } from 'react'
import { useDispatch } from 'react-redux'
import type { SelectedIDType } from '@store/ui/helpers'
import { uiSlice } from '@store/ui/slice'
import SelectedTabButton from './SelectedTabButton'

interface Props extends ButtonProps {
  layer: SelectedIDType
}

const ClearSelected = ({ layer, ...props }: Props) => {
  const dispatch = useDispatch()

  const handleClick = useCallback(() => {
    dispatch(uiSlice.actions.setSelectedEmptyByType(layer))
  }, [dispatch, layer])

  return (
    <Tooltip title="Clear" placement="top">
      <SelectedTabButton
        onClick={handleClick}
        icon={<ClearOutlined title="Clear" />}
        {...props}
      />
    </Tooltip>
  )
}

export default ClearSelected
