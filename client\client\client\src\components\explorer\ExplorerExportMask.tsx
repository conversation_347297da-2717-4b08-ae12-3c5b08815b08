import classNames from 'classnames'
import React from 'react'
import { useMeasure } from 'react-use'
import { useSelector } from '@store'
import { getExportState } from '@store/features/explorer'
import styles from './ExplorerExportMask.module.scss'
import { getPageDimensions } from './helpers'

const ExplorerExportMask = () => {
  const { active, isLandscape, pageFormat } = useSelector(getExportState)
  const [ref, { width: containerWidth, height: containerHeight }] =
    useMeasure<HTMLDivElement>()

  const { widthRatio, heightRatio } = getPageDimensions(pageFormat, isLandscape)

  const flexDirection =
    containerHeight * widthRatio < containerWidth ? 'row' : 'column'
  const aspectRatio = `${widthRatio} / ${heightRatio}`

  if (!active) return null

  return (
    <div
      ref={ref}
      className={classNames('ExplorerExportMask', styles.container)}
      style={{ flexDirection }}
    >
      <div className={styles.mask} />
      <div className={styles.view} style={{ aspectRatio }} />
      <div className={styles.mask} />
    </div>
  )
}

export default ExplorerExportMask
