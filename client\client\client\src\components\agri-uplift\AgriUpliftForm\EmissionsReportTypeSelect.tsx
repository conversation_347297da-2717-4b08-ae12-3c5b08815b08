import { Select, type SelectProps } from 'antd'
import React from 'react'
import { useFinanceAgriUpliftProductRetrieveQuery } from '@store/services/finance/codegen'

type Props = SelectProps

const EmissionsReportTypeSelect = (props: Props) => {
  const {
    data: product = { reportTypes: [] },
    isLoading: productLoading,
    fulfilledTimeStamp,
  } = useFinanceAgriUpliftProductRetrieveQuery()

  const reportTypeOptions = product?.reportTypes?.map(({ pk, name }) => ({
    key: pk,
    label: name,
    value: pk,
  }))

  return (
    <Select
      key={fulfilledTimeStamp}
      loading={productLoading}
      options={reportTypeOptions}
      {...props}
    />
  )
}

export default EmissionsReportTypeSelect
