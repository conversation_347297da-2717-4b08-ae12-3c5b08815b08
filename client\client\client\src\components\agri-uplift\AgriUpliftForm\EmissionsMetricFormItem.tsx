import { emissionsMetricTypeIdToName } from '@components/emissions-metrics/util'
import type { CustomerEmissionsMetricType } from '@store/services/sdk'
import { concatPaths } from '@util/helpers'
import { createPrecisionRule, isValidTo } from './helpers'
import { Form } from 'antd'
import EmissionsMetricInput from './EmissionsMetricInput'

const EmissionsMetricFormItem = ({
  reportTypeId,
  metricType,
  tooltip,
  fieldPath,
  includeLabel = true,
}: {
  reportTypeId: number
  metricType: CustomerEmissionsMetricType
  tooltip?: string
  fieldPath: (string | number)[]
  includeLabel?: boolean
}) => {
  return (
    <Form.Item
      key={`${reportTypeId}-${metricType.pk}`}
      name={concatPaths(fieldPath, emissionsMetricTypeIdToName(metricType.pk))}
      label={includeLabel ? metricType.name : undefined}
      style={{ ...(metricType.isSection && { gridColumn: 1 }) }}
      rules={[
        {
          required: true,
          message: `${metricType.name} is required`,
          validator: (_, value) => {
            if (
              value?.value !== '' &&
              !Number.isNaN(value?.value) &&
              // Could store this in a definition and pass it to the child
              isValidTo({ reportTypeId, metricTypeId: metricType.pk })(value)
            )
              return Promise.resolve()
            return Promise.reject()
          },
        },
        {
          transform: (value) => value?.value,
          ...createPrecisionRule(1),
        },
      ]}
      tooltip={tooltip}
    >
      <EmissionsMetricInput
        metricType={metricType}
        metricTypeId={metricType.pk}
        reportTypeId={reportTypeId}
      />
    </Form.Item>
  )
}

export default EmissionsMetricFormItem
