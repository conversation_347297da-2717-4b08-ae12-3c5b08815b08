import { Document } from '@react-pdf/renderer'
import { memo, useEffect, useMemo } from 'react'
import { PDFView } from '@components/pdf'
import type { CustomerReportState } from '@store/features/customer'
import useReport from '@store/hooks/useReport'
import { useEventsCreateMutation, type Kpi } from '@store/services/sdk'
import CustomerPdfBenchmarkingPage from './CustomerPdfBenchmarkingPage'
import CustomerPdfCoverPage from './CustomerPdfCoverPage'
import CustomerPdfDisclaimerPage from './CustomerPdfDisclaimerPage'
import CustomerPdfFinancialAnalysisPage from './CustomerPdfFinancialAnalysisPage'
import CustomerPdfFinancialsPage from './CustomerPdfFinancialsPage'
import CustomerPdfPerformanceGraphsPage from './CustomerPdfPerformanceGraphsPage'
import CustomerReportLoadingAlert from './CustomerReportLoadingAlert'

const mapSelectedKeys = (selection: string[][] = []) =>
  selection.flatMap((e) => e[1])

export type CustomerPdfProps = {
  customerId: number
  selectedMeasures: string[][]
  customerName: string
  denominator: string
  kpis: Kpi[] | undefined
  segment: 'W' | 'R'
  loading?: boolean
  selectedComponents: string[][]
  reportTitle: string
  report: CustomerReportState
}

const CustomerPdf = ({
  customerId,
  selectedMeasures,
  customerName,
  segment,
  kpis,
  denominator,
  selectedComponents,
  loading: parentLoading,
  reportTitle,
  report,
}: CustomerPdfProps) => {
  const measures = useMemo(
    () => mapSelectedKeys(selectedMeasures),
    [selectedMeasures]
  )

  const components = useMemo(
    () => mapSelectedKeys(selectedComponents),
    [selectedComponents]
  )

  const { data, isLoading: loading } = useReport(
    measures,
    customerId,
    segment,
    denominator
  )

  const [createEvent] = useEventsCreateMutation()

  useEffect(() => {
    /*
    TODO: Remove once report data is fully generated on the backend and this can be done as part of the data request.
    */
    if (!loading && !parentLoading) {
      createEvent({
        event: {
          eventName: 'create_customer_report',
          kwargs: {
            customerId,
            selectedMeasures,
            selectedComponents,
            reportTitle,
          },
        },
      })
    }
  }, [
    createEvent,
    loading,
    parentLoading,
    customerId,
    selectedMeasures,
    selectedComponents,
    reportTitle,
  ])

  const isLoading = loading || parentLoading

  if (isLoading || !kpis || data.length < measures.length) {
    return <CustomerReportLoadingAlert />
  }

  return (
    <PDFView>
      <Document>
        <CustomerPdfCoverPage
          customerName={customerName}
          reportTitle={reportTitle}
        />
        <CustomerPdfDisclaimerPage />
        <CustomerPdfPerformanceGraphsPage
          components={components}
          kpis={kpis}
          segment={segment}
        />
        <CustomerPdfBenchmarkingPage data={data} denominator={denominator} />
        <CustomerPdfFinancialsPage
          kpis={kpis}
          selectedSections={report.selectedFinancials}
        />
        <CustomerPdfFinancialAnalysisPage
          components={components}
          kpis={kpis}
          segment={segment}
        />
      </Document>
    </PDFView>
  )
}

export default memo(CustomerPdf)
