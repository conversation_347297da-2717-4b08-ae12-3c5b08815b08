import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Dropdown } from 'antd'
import ExplorerSelectedValocityListingMenu from '@components/explorer/ExplorerSelectedValocityListingMenu'
import { useGetValocityListingQuery } from '@store/services/valocitySale'
import { formatArea } from '@util'

interface SelectedValocityListingItemProps {
  valocityListingId: string
  position: number
}

export const SelectedValocityListingItem = ({
  valocityListingId,
}: SelectedValocityListingItemProps) => {
  const { data: valocityListing } = useGetValocityListingQuery(
    valocityListingId ?? skipToken
  )

  if (!valocityListing) return null

  return (
    <Dropdown
      overlay={
        <ExplorerSelectedValocityListingMenu id={Number(valocityListingId)} />
      }
      placement="bottomLeft"
      trigger={['click']}
    >
      <tr className="map-container-list-item">
        <td>{valocityListing.properties?.fullAddress}</td>
        <td>{valocityListing.properties?.askingPrice}</td>
        <td>{valocityListing.properties?.listingDate}</td>
        <td>{formatArea(valocityListing.properties?.totalArea, 'ha')}</td>
        <td>{valocityListing.properties?.listingSite}</td>
      </tr>
    </Dropdown>
  )
}
