import { Al<PERSON>, Card, type CardProps, Col, Divider } from 'antd'
import React, { useMemo } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { PrettyStatistics } from '@components/generic/PrettyStatistics/PrettyStatistics'
import sdk, { type Facility } from '@store/services/sdk'
import { formatDollarValue } from '@util'
import { skipPk } from '@util/helpers'
import CustomerFacilitiesGraph from './CustomerFacilitiesGraph'
import CustomerFacilitiesTable, {
  type CustomerFacilitiesTableProps,
} from './CustomerFacilitiesTable'
import { COL_PROPS } from './helpers'

export const CustomerLoansCard = ({ children, ...props }: CardProps) => {
  const { customerId } = useParams()

  const {
    data: facilities = [],
    isLoading,
    error,
  } = sdk.useCustomerGroupLendingListQuery(skipPk(customerId))

  const [params, setSelected] = useSearchParams()

  const selectedIds = useMemo(() => {
    const param = params.get('selected')
    if (param === null) return []
    return param.split(',')
  }, [params])

  const selectedFacilities = useMemo(
    () =>
      !selectedIds.length
        ? facilities
        : facilities.filter((facility) =>
            selectedIds.includes(facility.pk.toString())
          ),
    [facilities, selectedIds]
  )

  const handleSelectedFacilitiesChange: CustomerFacilitiesTableProps['onChange'] =
    (selectedRowKeys) => {
      setSelected({ selected: selectedRowKeys.join(',') })
    }

  const facilitiesCounter = useMemo(() => {
    const counter: { [key: Facility['accountType']]: number } = {
      'Total Lending': 0,
      Loan: 0,
      'Green Loan': 0,
      Revolving: 0,
    }
    for (const { accountType, limit } of selectedFacilities) {
      counter['Total Lending'] += Number(limit)
      counter[accountType === 'Limit' ? 'Revolving' : accountType] +=
        Number(limit)
    }
    return Object.keys(counter).map((label) => ({
      label,
      value: counter[label],
      formatter: formatDollarValue,
      verbose: 'ANZ Faciltities',
    }))
  }, [selectedFacilities])

  return (
    <>
      <Col {...COL_PROPS}>
        <Card title="Customer Group Facilities" {...props} loading={isLoading}>
          {!error && facilities && [...(facilities ?? [])].length > 0 ? (
            <React.Fragment>
              <PrettyStatistics dataSource={facilitiesCounter} />
              <Divider />
              <CustomerFacilitiesTable
                facilities={facilities}
                selectedFacilities={selectedFacilities}
                onChange={handleSelectedFacilitiesChange}
              />
            </React.Fragment>
          ) : (
            <Alert
              type="warning"
              showIcon
              message="No facilities were found for this customer, or this customer may not be in a currently supported segment."
            />
          )}
          {children}
        </Card>
      </Col>
      <Col {...COL_PROPS}>
        <CustomerFacilitiesGraph
          facilities={selectedFacilities}
          loading={isLoading}
          error={error}
        />
      </Col>
    </>
  )
}
