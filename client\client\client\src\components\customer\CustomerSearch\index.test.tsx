import React from 'react'
import { rest } from 'msw'
import { setupServer } from 'msw/node'
import type { Customer } from '@store/services/sdk'
import { renderWithProviders } from '@/tests/util'
import CustomerSearch, { PLACEHOLDER_TEXT } from './index'
import { STORAGE_KEY } from './useCustomerSearchHistory'
import {
  fireEvent,
  waitFor,
  waitForElementToBeRemoved,
} from '@testing-library/react'

const makeResult = (n: number) => ({
  pk: n,
  key: n,
  customerId: n,
  customerNumber: n.toString(),
  entityName: `Customer ${n}`,
  customerSetCode: `R${n}`,
  anzsic: '0000n',
  anzsicDescription: `ANZSIC ${n}`,
  segment: 'R',
  businessUnit: 'AGRI',
  customerGroupId: 1,
  tradingGroup: {
    tradingGroupId: n.toString(),
    tradingGroupNumber: n,
    tradingGroupName: `Trading Group ${n}`,
    deleted: 0,
  },
})

const results: Customer[] = [makeResult(1), makeResult(2)]

const handlers = [
  rest.get('/api/customer-search/', (req, res, ctx) => {
    const searchParams = req.url.searchParams

    let data = { results }

    if (searchParams.get('match') === 'Customer') {
      data = { results }
    } else if (searchParams.get('ids') === '1') {
      data = { results: results.slice(0, 1) }
    }

    return res(ctx.json(data))
  }),
]

const server = setupServer(...handlers)

beforeAll(() => {
  server.listen()
})

afterEach(() => {
  server.resetHandlers()
})

afterAll(() => {
  server.resetHandlers()
})

describe('CustomerSearch', () => {
  it('Renders', () => {
    const { getByPlaceholderText } = renderWithProviders(<CustomerSearch />)
    expect(getByPlaceholderText(PLACEHOLDER_TEXT)).toBeInTheDocument()
  })

  it('Clears result', async () => {
    const query = 'Customer'
    const { findByText, getByPlaceholderText } = renderWithProviders(
      <CustomerSearch />
    )
    fireEvent.change(getByPlaceholderText(PLACEHOLDER_TEXT), {
      target: { value: query },
    })
    expect(getByPlaceholderText(PLACEHOLDER_TEXT)).toHaveValue(query)
    const result = await findByText(results[0].entityName)
    fireEvent.click(result)
    expect(getByPlaceholderText(PLACEHOLDER_TEXT)).toHaveValue(
      results[0].entityName
    )
  })
})

describe('CustomerSearch History', () => {
  beforeEach(() => {
    localStorage.setItem(STORAGE_KEY, '["1"]')
  })

  it('Displays search history', async () => {
    // Keeping the dropdown open because ant's interactivity is very hard to test
    const { getByText } = renderWithProviders(<CustomerSearch open />)

    await waitFor(() => {
      expect(getByText(results[0].entityName)).toBeInTheDocument()
    })
  })

  it('Replaces history with search results', async () => {
    const { getByPlaceholderText, getByText } = renderWithProviders(
      <CustomerSearch open />
    )

    const input = getByPlaceholderText(PLACEHOLDER_TEXT)

    await waitFor(() => {
      expect(getByText(results[0].entityName)).toBeInTheDocument()
    })

    fireEvent.change(input, { target: { value: 'Customer' } })

    await waitForElementToBeRemoved(() => getByText(results[0].entityName))

    await waitFor(() => {
      expect(getByText(results[1].entityName)).toBeInTheDocument()
    })
  })
})
