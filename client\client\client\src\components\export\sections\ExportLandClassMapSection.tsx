import React from 'react'
import { TitleOutlineLayer } from '@components/physicalProperties/titles/TitleOutlineLayer'
import {
  useLandAssetLayerLegend,
  useLegendAreaSummary,
  usePropertyAreas,
} from '@store/features/assets/hooks'
import { useGetValuationSummaryQuery } from '@store/services/valuations'
import type { Valuation } from '@types'
import { GeoJSON } from '../../leaflet/GeoJSON'
import { ExportLegend, ExportLegendItem } from '../components/ExportLegend'
import { ExportMap } from '../components/ExportMap'
import { ExportSection } from '../components/ExportSection'
import styles from './ExportLandClassMapSection.module.scss'

const FIELD_LABEL = 'bestUseLandClass'
export interface ExportLandClassMapSectionProps {
  center: { lat: number; lng: number }
  valuation: Valuation
}

export const ExportLandClassMapSection = (
  props: ExportLandClassMapSectionProps
) => {
  const { center, valuation } = props

  const propertyAreas = usePropertyAreas(valuation.valuationId)
  const [landAssetLayer, legend] = useLandAssetLayerLegend(
    valuation.valuationId
  )
  const legendMap = useLegendAreaSummary(
    landAssetLayer,
    legend,
    propertyAreas,
    FIELD_LABEL,
    'area'
  )

  const { data } = useGetValuationSummaryQuery(props.valuation.valuationId)

  return (
    <React.Fragment>
      <ExportSection>
        <div className="export-section-inner flex">
          <ExportLegend direction="horizontal">
            {Object.keys(legendMap ?? {})?.map((k) => {
              const entry = legendMap[k]
              return (
                <ExportLegendItem
                  key={k}
                  fillColor={k}
                  borderColor={k}
                  label={entry}
                />
              )
            })}
          </ExportLegend>
        </div>
      </ExportSection>
      <ExportSection title="Land Class Breakdown">
        <ExportMap maxZoom={19} type="terrain" center={center} size="half">
          {landAssetLayer?.features?.map((feature, index) => {
            const color = legend?.getColor(
              feature?.properties?.[
                FIELD_LABEL as keyof typeof feature.properties
              ] as string
            )
            return (
              <GeoJSON
                key={`landa-asset-${index}-${
                  (feature?.id as string) ?? ''
                }-${color}`}
                data={feature}
                style={{
                  color: color,
                  weight: 2,
                  fillOpacity: 0.3,
                }}
              />
            )
          })}
        </ExportMap>
      </ExportSection>
      <ExportSection title="Property Boundaries" className={styles.boundaries}>
        <ExportMap maxZoom={19} type="terrain" center={center} size="half">
          {data && <TitleOutlineLayer valuationSummary={data} />}
        </ExportMap>
      </ExportSection>
    </React.Fragment>
  )
}
