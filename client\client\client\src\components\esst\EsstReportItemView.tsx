import { useEsstReportItemsRetrieveQuery } from '@store/services/esst/codegen'
import EsstReportHeading from './EsstReportHeading'
import EsstReportItemSummary from './EsstReportItemSummary'
import EsstReportItemEditView from './EsstReportItemEditView'

type Props = {
  esstReportItemId: number
}

export default function EsstReportItemView({ esstReportItemId }: Props) {
  const { data: esstReportItem, isLoading: esstReportItemLoading } =
    useEsstReportItemsRetrieveQuery({ pk: esstReportItemId })

  if (esstReportItemLoading) return <div>Loading...</div>
  if (!esstReportItem) return <div>Error</div>

  return (
    <>
      <EsstReportHeading />
      {esstReportItem.completed ? (
        <EsstReportItemSummary esstReportItemId={esstReportItem.id} />
      ) : (
        <EsstReportItemEditView esstReportItemId={esstReportItem.id} />
      )}
    </>
  )
}
