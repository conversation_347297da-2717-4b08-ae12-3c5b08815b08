import {
  DeleteOutlined,
  LoadingOutlined,
  SaveOutlined,
  StopOutlined,
} from '@ant-design/icons'
import { Button, Input, List, Tag } from 'antd'
import React, { useCallback, useState } from 'react'
import { useDispatch } from 'react-redux'
import type { CustomerReportState } from '@store/features/customer'
import { actions } from '@store/features/customer'
import {
  type CustomerReportTemplate,
  useCustomerReportTemplateDestroyMutation,
  useCustomerReportTemplatePartialUpdateMutation,
} from '@store/services/sdk'
import styles from './CustomerReportTemplatesItem.module.scss'
import { useRouteCustomerId } from './useRouteCustomer'

type Props = typeof List.Item.defaultProps & {
  record: CustomerReportTemplate // TODO: This enpoint needs to be structured
}

const CustomerReportTemplatesItem = ({ record, ...props }: Props) => {
  const dispatch = useDispatch()

  const customerId = useRouteCustomerId()

  const [editing, setEditing] = useState<boolean>(false)
  const [name, setName] = useState<string>(record.name)

  const [updateReportTemplate, { isLoading }] =
    useCustomerReportTemplatePartialUpdateMutation()

  const [destroyReportTemplate, { isLoading: destroyLoading }] =
    useCustomerReportTemplateDestroyMutation()

  const handleLoad = useCallback(
    (newState: Partial<CustomerReportState>) => {
      dispatch(actions.setReportState({ ...newState, customerId }))
    },
    [dispatch, customerId]
  )

  return (
    <List.Item
      {...props}
      key={record.id}
      extra={
        <Tag style={{ marginLeft: '8px' }}>
          {record.segment === 'W' ? 'Commercial' : 'Agri'}
        </Tag>
      }
    >
      {editing ? (
        <div className={styles.name}>
          <Input value={name} onChange={(e) => setName(e.target.value)} />
          <React.Fragment>
            <Button
              icon={<SaveOutlined />}
              onClick={async () => {
                setEditing(false)
                await Promise.resolve(
                  dispatch(
                    updateReportTemplate({
                      pk: Number(record.id),
                      patchedCustomerReportTemplate: {
                        name,
                      },
                    })
                  )
                )
                setName(record.name)
              }}
            />
            <Button
              icon={<StopOutlined />}
              onClick={() => {
                setEditing(false)
                setName(record.name)
              }}
            />
          </React.Fragment>
        </div>
      ) : (
        <div className={styles.name}>
          <button type="button" onClick={() => setEditing(true)}>
            {isLoading || destroyLoading ? <LoadingOutlined /> : record.name}
          </button>
          <Button onClick={() => handleLoad(record.data)}>Load</Button>
          <Button
            icon={<DeleteOutlined />}
            onClick={async () => {
              await Promise.resolve(
                dispatch(
                  destroyReportTemplate({
                    pk: Number(record.id),
                  })
                )
              )
            }}
          />
        </div>
      )}
    </List.Item>
  )
}

export default CustomerReportTemplatesItem
