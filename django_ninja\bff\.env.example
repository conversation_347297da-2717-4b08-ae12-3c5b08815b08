# BFF Configuration Example - ANZ Development Environment
# Copy this file to .env and update with your actual values

# PingFederate Authentication Configuration - ANZ Development Environment
PINGFEDERATE_ISSUER=https://iamidentity.federate.dev.service.dev
PINGFEDERATE_AUDIENCE=AU-CROP-UI-CLIENT_ID
PINGFEDERATE_CLIENT_ID=2uNacQABwi04dFsLekUrM4TcTV2FkZ7tTFI8XznGhymiMsv8XshQbISFJncNkYlv
PINGFEDERATE_CLIENT_SECRET=Kn5cKkGcEx817zJHP5lV4UmvbedVu7QN05NXr2fSwqIyxt6V1Vlt8LtBJTAhZod6
PINGFEDERATE_JWKS_URL=https://idpengine.coz.dev-5.stau.np.au1.aws.anz.com/ext/pf/JWKS
PINGFEDERATE_AUTH_ENDPOINT=https://idpengine.coz.dev-5.stau.np.au1.aws.anz.com/as/authorization.oauth2
PINGFEDERATE_TOKEN_ENDPOINT=https://idpengine.coz.dev-5.stau.np.au1.aws.anz.com/as/token.oauth2
PINGFEDERATE_ALGORITHM=RS256
PINGFEDERATE_LEEWAY=30
PINGFEDERATE_CACHE_TIMEOUT=3600

# Rate Limiting Configuration
BFF_RATE_LIMITING_ENABLED=true
BFF_DEFAULT_RATE_LIMIT=100
BFF_DEFAULT_RATE_WINDOW=3600
BFF_BURST_RATE_LIMIT=20
BFF_BURST_RATE_WINDOW=60
BFF_RATE_LIMIT_PREFIX=bff_rate_limit

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_TIMEOUT=5

# RiskRadar API Configuration (BFF only handles RiskRadar endpoints)
RISKRADAR_API_URL=http://localhost:8000/bff/api/riskradar
RISKRADAR_API_TIMEOUT=30
RISKRADAR_API_RETRIES=3

# Logging Configuration
BFF_LOG_LEVEL=INFO
BFF_LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
BFF_LOG_REQUESTS=true
BFF_LOG_RESPONSES=false

# Security Configuration
BFF_ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.com
BFF_MAX_REQUEST_SIZE=10485760
BFF_REQUIRE_HTTPS=false
BFF_CORS_ENABLED=true

# Response Aggregation Configuration
BFF_DEFAULT_AGGREGATION=merge
BFF_MAX_CONCURRENT_REQUESTS=10
BFF_AGGREGATION_TIMEOUT=30
BFF_INCLUDE_METADATA=true

# Health Check Configuration
BFF_HEALTH_CHECK_ENABLED=true
BFF_HEALTH_CHECK_BACKENDS=true
BFF_HEALTH_CHECK_REDIS=true
BFF_HEALTH_CHECK_PINGFEDERATE=true
BFF_HEALTH_CHECK_TIMEOUT=5
